config {
  type: "test",
  dataset: "dataform_galoy_bbw_test",
}

WITH test_data AS (
  -- Create test data for stg_user_days
  SELECT 
    DATE('2025-04-16') AS day, 
    95654 AS user_key, 
    TIMESTAMP('2025-01-01') AS created_at, 
    1000 AS spot_close_sats_per_cent
  UNION ALL
  SELECT 
    DATE('2025-04-17') AS day, 
    95654 AS user_key, 
    TIMESTAMP('2025-01-01') AS created_at, 
    1000 AS spot_close_sats_per_cent
  UNION ALL
  SELECT 
    DATE('2025-04-18') AS day, 
    95654 AS user_key, 
    TIMESTAMP('2025-01-01') AS created_at, 
    1000 AS spot_close_sats_per_cent
),

test_transactions AS (
  -- Create test data for stg_daily_user_transaction_summaries
  SELECT 
    DATE('2025-04-16') AS day, 
    95654 AS user_key, 
    296095 AS sat_bitcoin_balance_change,
    0 AS cent_stablesats_balance_change,
    NULL AS previous_recorded_at
  UNION ALL
  SELECT 
    DATE('2025-04-17') AS day, 
    95654 AS user_key, 
    -296095 AS sat_bitcoin_balance_change, -- <PERSON><PERSON> becomes 0
    0 AS cent_stablesats_balance_change,
    NULL AS previous_recorded_at
  UNION ALL
  SELECT 
    DATE('2025-04-18') AS day, 
    95654 AS user_key, 
    50000 AS sat_bitcoin_balance_change, -- New transaction after balance was 0
    0 AS cent_stablesats_balance_change,
    NULL AS previous_recorded_at
)

-- Test the stg_daily_user_balances view
SELECT
  day,
  user_key,
  sat_bitcoin_close_balance,
  cent_stablesats_close_balance
FROM (
  SELECT 
    day, 
    user_key, 
    created_at, 
    spot_close_sats_per_cent,
    SUM(COALESCE(sat_bitcoin_balance_change, 0)) OVER (
      PARTITION BY user_key
      ORDER BY day
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ) AS sat_bitcoin_close_balance,
    SUM(COALESCE(cent_stablesats_balance_change, 0)) OVER (
      PARTITION BY user_key
      ORDER BY day
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ) AS cent_stablesats_close_balance,
    LAST_VALUE(previous_recorded_at IGNORE NULLS) OVER (
      PARTITION BY user_key
      ORDER BY day
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ) AS previous_recorded_at
  FROM test_data
  LEFT JOIN test_transactions USING (day, user_key)
)
ORDER BY day

-- Expected results:
-- | day        | user_key | sat_bitcoin_close_balance | cent_stablesats_close_balance |
-- |------------|----------|---------------------------|------------------------------|
-- | 2025-04-16 | 95654    | 296095                    | 0                            |
-- | 2025-04-17 | 95654    | 0                         | 0                            |
-- | 2025-04-18 | 95654    | 50000                     | 0                            |
