variable "environment" {}

locals {
  project     = "galoy-reporting"
  environment = var.environment
  tables = [
    "pg_stablesats_public_galoy_transactions",
    "pg_stablesats_public_okex_orders",
    "pg_stablesats_public_okex_transfers",
    "pg_stablesats_public_sqlx_ledger_balances",
    "pg_stablesats_public_user_trades"
  ]
}

resource "google_service_account" "kafka" {
  project      = local.project
  account_id   = "kafka-${local.environment}"
  display_name = "Account for running kafka in ${local.environment}"
}

# create a dataset for kafka in each environment - ${local.environment}_kafka_raw
module "dataset" {
  source        = "../imports/raw_dataset"
  environment   = local.environment
  component     = "kafka"
  view_all_envs = false
}

# give access to the kafka-${local.environment} service account to write to the dataset
resource "google_bigquery_dataset_iam_member" "kafka" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:${google_service_account.kafka.email}"
}

# create tables in the dataset to match the source collections streamed from mongodb with kafka
resource "google_bigquery_table" "mongodb_galoy_medici_balances" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = "mongodb_galoy_medici_balances"
  time_partitioning {
    type = "DAY"
  }
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/mongo-medici-balances.json")
}

resource "google_bigquery_table" "mongodb_galoy_medici_journals" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = "mongodb_galoy_medici_journals"
  time_partitioning {
    type = "DAY"
  }
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/mongo-medici-journals.json")
}

resource "google_bigquery_table" "mongodb_galoy_medici_transaction_metadatas" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = "mongodb_galoy_medici_transaction_metadatas"
  time_partitioning {
    type = "DAY"
  }
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/mongo-medici-transaction-metadatas.json")
}

resource "google_bigquery_table" "mongodb_galoy_medici_transactions" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = "mongodb_galoy_medici_transactions"
  time_partitioning {
    type = "DAY"
  }
  clustering          = ["operationType"]
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/mongo-medici-transactions.json")
}

resource "google_bigquery_table" "mongodb_galoy_accounts" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = "mongodb_galoy_accounts"
  time_partitioning {
    type = "DAY"
  }
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/mongo-accounts.json")
}

resource "google_bigquery_table" "mongodb_galoy_wallets" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = "mongodb_galoy_wallets"
  time_partitioning {
    type = "DAY"
  }
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/mongo-wallets.json")
}

# create tables in the dataset to match the tables streamed from the stablesats postgres
resource "google_bigquery_table" "postgres_stablesats" {
  for_each = toset(local.tables)

  project    = local.project
  dataset_id = module.dataset.dataset_id
  table_id   = each.value
  time_partitioning {
    type = "DAY"
  }
  deletion_protection = false
  schema              = file("${path.module}/bigquery-schemas/${each.value}.json")
}
