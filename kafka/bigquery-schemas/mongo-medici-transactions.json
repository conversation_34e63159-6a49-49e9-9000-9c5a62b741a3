[{"name": "_id", "type": "RECORD", "fields": [{"name": "_data", "type": "STRING"}]}, {"name": "_journal", "type": "STRING"}, {"name": "clusterTime", "type": "TIMESTAMP"}, {"name": "documentKey", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}]}, {"name": "fullDocument", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "_journal", "type": "STRING"}, {"name": "account_path", "type": "STRING", "mode": "REPEATED"}, {"name": "accounts", "type": "STRING"}, {"name": "book", "type": "STRING"}, {"name": "credit", "type": "FLOAT64"}, {"name": "datetime", "type": "TIMESTAMP"}, {"name": "debit", "type": "FLOAT64"}, {"name": "memo", "type": "STRING"}, {"name": "timestamp", "type": "TIMESTAMP"}, {"name": "type", "type": "STRING"}, {"name": "pending", "type": "BOOLEAN"}, {"name": "hash", "type": "STRING"}, {"name": "payee_addresses", "type": "STRING", "mode": "REPEATED"}, {"name": "fee", "type": "INTEGER"}, {"name": "feeUsd", "type": "FLOAT64"}, {"name": "usd", "type": "FLOAT64"}, {"name": "meta", "type": "RECORD", "fields": [{"name": "sendAll", "type": "BOOL"}, {"name": "feesCollectedOn", "type": "STRING"}]}, {"name": "currency", "type": "STRING"}, {"name": "related_journal", "type": "STRING"}, {"name": "satsAmount", "type": "INTEGER"}, {"name": "centsAmount", "type": "INTEGER"}, {"name": "centsFee", "type": "INTEGER"}, {"name": "displayAmount", "type": "STRING"}, {"name": "displayFee", "type": "STRING"}, {"name": "satsFee", "type": "INTEGER"}, {"name": "displayCurrency", "type": "STRING"}, {"name": "username", "type": "STRING"}, {"name": "memoPayer", "type": "STRING"}, {"name": "satsAmountMigration", "type": "BOOLEAN"}, {"name": "feeKnownInAdvance", "type": "BOOLEAN"}, {"name": "voided", "type": "BOOLEAN"}, {"name": "void_reason", "type": "STRING"}, {"name": "_original_journal", "type": "STRING"}, {"name": "pubkey", "type": "STRING"}, {"name": "request_id", "type": "STRING"}, {"name": "payout_id", "type": "STRING"}, {"name": "txid", "type": "STRING"}, {"name": "vout", "type": "INTEGER"}, {"name": "external_id", "type": "STRING"}, {"name": "bundle_completion_state", "type": "STRING"}, {"name": "sats", "type": "INTEGER"}, {"name": "err", "type": "STRING"}, {"name": "revealedPreImage", "type": "STRING"}]}, {"name": "ns", "type": "RECORD", "fields": [{"name": "coll", "type": "STRING"}, {"name": "db", "type": "STRING"}]}, {"name": "operationType", "type": "STRING"}, {"name": "updateDescription", "type": "RECORD", "fields": [{"name": "removedFields", "type": "STRING", "mode": "REPEATED"}, {"name": "truncatedArrays", "type": "STRING", "mode": "REPEATED"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "RECORD", "fields": [{"name": "payout_id", "type": "STRING"}, {"name": "void_reason", "type": "STRING"}, {"name": "voided", "type": "BOOLEAN"}, {"name": "pending", "type": "BOOLEAN"}, {"name": "hash", "type": "STRING"}, {"name": "vout", "type": "INTEGER"}, {"name": "bundle_completion_state", "type": "STRING"}]}]}, {"name": "wallTime", "type": "TIMESTAMP"}]