[{"name": "_id", "type": "RECORD", "fields": [{"name": "_data", "type": "STRING"}]}, {"name": "clusterTime", "type": "TIMESTAMP"}, {"name": "documentKey", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}]}, {"name": "fullDocument", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "__v", "type": "INTEGER"}, {"name": "contactEnabled", "type": "BOOLEAN"}, {"name": "contacts", "mode": "REPEATED", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "id", "type": "STRING"}, {"name": "transactionsCount", "type": "INTEGER"}, {"name": "name", "type": "STRING"}]}, {"name": "created_at", "type": "TIMESTAMP"}, {"name": "earn", "type": "STRING", "mode": "REPEATED"}, {"name": "id", "type": "STRING"}, {"name": "kratosUserId", "type": "STRING"}, {"name": "role", "type": "STRING"}, {"name": "title", "type": "STRING"}, {"name": "coordinates", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "latitude", "type": "FLOAT64"}, {"name": "longitude", "type": "FLOAT64"}]}, {"name": "statusHistory", "mode": "REPEATED", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "comment", "type": "STRING"}, {"name": "status", "type": "STRING"}, {"name": "updatedAt", "type": "TIMESTAMP"}, {"name": "updatedByUserId", "type": "STRING"}, {"name": "updatedByPrivilegedClientId", "type": "STRING"}]}, {"name": "withdrawFee", "type": "INTEGER"}, {"name": "notificationSettings", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "push", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "enabled", "type": "BOOLEAN"}, {"name": "disabledCategories", "type": "STRING", "mode": "REPEATED"}]}]}]}, {"name": "ns", "type": "RECORD", "fields": [{"name": "coll", "type": "STRING"}, {"name": "db", "type": "STRING"}]}, {"name": "operationType", "type": "STRING"}, {"name": "updateDescription", "type": "RECORD", "fields": [{"name": "removedFields", "type": "STRING", "mode": "REPEATED"}, {"name": "truncatedArrays", "type": "STRING", "mode": "REPEATED"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "RECORD", "fields": [{"name": "contactEnabled", "type": "BOOLEAN"}, {"name": "earn", "type": "STRING", "mode": "REPEATED"}, {"name": "earn_1", "type": "STRING"}, {"name": "earn_2", "type": "STRING"}, {"name": "earn_3", "type": "STRING"}, {"name": "earn_4", "type": "STRING"}, {"name": "earn_5", "type": "STRING"}, {"name": "earn_6", "type": "STRING"}, {"name": "earn_7", "type": "STRING"}, {"name": "earn_8", "type": "STRING"}, {"name": "earn_9", "type": "STRING"}, {"name": "earn_10", "type": "STRING"}, {"name": "earn_11", "type": "STRING"}, {"name": "earn_12", "type": "STRING"}, {"name": "earn_13", "type": "STRING"}, {"name": "earn_14", "type": "STRING"}, {"name": "earn_15", "type": "STRING"}, {"name": "earn_16", "type": "STRING"}, {"name": "earn_17", "type": "STRING"}, {"name": "earn_18", "type": "STRING"}, {"name": "earn_19", "type": "STRING"}, {"name": "earn_20", "type": "STRING"}, {"name": "earn_21", "type": "STRING"}, {"name": "earn_22", "type": "STRING"}, {"name": "earn_23", "type": "STRING"}, {"name": "earn_24", "type": "STRING"}, {"name": "earn_25", "type": "STRING"}, {"name": "earn_26", "type": "STRING"}, {"name": "earn_27", "type": "STRING"}, {"name": "earn_28", "type": "STRING"}, {"name": "earn_29", "type": "STRING"}, {"name": "earn_30", "type": "STRING"}, {"name": "earn_31", "type": "STRING"}, {"name": "earn_32", "type": "STRING"}, {"name": "earn_33", "type": "STRING"}, {"name": "earn_34", "type": "STRING"}, {"name": "earn_35", "type": "STRING"}, {"name": "earn_36", "type": "STRING"}, {"name": "earn_37", "type": "STRING"}, {"name": "earn_38", "type": "STRING"}, {"name": "earn_39", "type": "STRING"}, {"name": "earn_40", "type": "STRING"}, {"name": "earn_41", "type": "STRING"}, {"name": "earn_42", "type": "STRING"}, {"name": "earn_43", "type": "STRING"}, {"name": "earn_44", "type": "STRING"}, {"name": "earn_45", "type": "STRING"}, {"name": "earn_46", "type": "STRING"}, {"name": "earn_47", "type": "STRING"}, {"name": "earn_48", "type": "STRING"}, {"name": "earn_49", "type": "STRING"}, {"name": "earn_50", "type": "STRING"}, {"name": "earn_51", "type": "STRING"}, {"name": "earn_52", "type": "STRING"}, {"name": "earn_53", "type": "STRING"}, {"name": "earn_54", "type": "STRING"}, {"name": "earn_55", "type": "STRING"}, {"name": "earn_56", "type": "STRING"}, {"name": "earn_57", "type": "STRING"}, {"name": "earn_58", "type": "STRING"}, {"name": "earn_59", "type": "STRING"}, {"name": "earn_60", "type": "STRING"}, {"name": "earn_61", "type": "STRING"}, {"name": "earn_62", "type": "STRING"}, {"name": "earn_63", "type": "STRING"}, {"name": "earn_64", "type": "STRING"}, {"name": "earn_65", "type": "STRING"}, {"name": "earn_66", "type": "STRING"}, {"name": "earn_67", "type": "STRING"}, {"name": "earn_68", "type": "STRING"}, {"name": "earn_69", "type": "STRING"}, {"name": "earn_70", "type": "STRING"}, {"name": "earn_71", "type": "STRING"}, {"name": "earn_72", "type": "STRING"}, {"name": "earn_73", "type": "STRING"}, {"name": "earn_74", "type": "STRING"}, {"name": "earn_75", "type": "STRING"}, {"name": "earn_76", "type": "STRING"}, {"name": "earn_77", "type": "STRING"}, {"name": "earn_78", "type": "STRING"}, {"name": "earn_79", "type": "STRING"}, {"name": "earn_80", "type": "STRING"}, {"name": "earn_81", "type": "STRING"}, {"name": "earn_82", "type": "STRING"}, {"name": "earn_83", "type": "STRING"}, {"name": "earn_84", "type": "STRING"}, {"name": "earn_85", "type": "STRING"}, {"name": "earn_86", "type": "STRING"}, {"name": "earn_87", "type": "STRING"}, {"name": "earn_88", "type": "STRING"}, {"name": "earn_89", "type": "STRING"}, {"name": "earn_90", "type": "STRING"}, {"name": "earn_91", "type": "STRING"}, {"name": "earn_92", "type": "STRING"}, {"name": "earn_93", "type": "STRING"}, {"name": "earn_94", "type": "STRING"}, {"name": "earn_95", "type": "STRING"}, {"name": "earn_96", "type": "STRING"}, {"name": "earn_97", "type": "STRING"}, {"name": "earn_98", "type": "STRING"}, {"name": "earn_99", "type": "STRING"}, {"name": "earn_100", "type": "STRING"}, {"name": "earn_101", "type": "STRING"}, {"name": "earn_102", "type": "STRING"}, {"name": "earn_103", "type": "STRING"}, {"name": "earn_104", "type": "STRING"}, {"name": "earn_105", "type": "STRING"}, {"name": "earn_106", "type": "STRING"}, {"name": "earn_107", "type": "STRING"}, {"name": "earn_108", "type": "STRING"}, {"name": "earn_109", "type": "STRING"}, {"name": "earn_110", "type": "STRING"}, {"name": "earn_111", "type": "STRING"}, {"name": "earn_112", "type": "STRING"}, {"name": "earn_113", "type": "STRING"}, {"name": "earn_114", "type": "STRING"}, {"name": "earn_115", "type": "STRING"}, {"name": "earn_116", "type": "STRING"}, {"name": "earn_117", "type": "STRING"}, {"name": "earn_118", "type": "STRING"}, {"name": "earn_119", "type": "STRING"}, {"name": "earn_120", "type": "STRING"}, {"name": "earn_121", "type": "STRING"}, {"name": "earn_122", "type": "STRING"}, {"name": "earn_123", "type": "STRING"}, {"name": "earn_124", "type": "STRING"}, {"name": "earn_125", "type": "STRING"}, {"name": "earn_126", "type": "STRING"}, {"name": "earn_127", "type": "STRING"}, {"name": "earn_128", "type": "STRING"}, {"name": "earn_129", "type": "STRING"}, {"name": "earn_130", "type": "STRING"}, {"name": "earn_131", "type": "STRING"}, {"name": "earn_132", "type": "STRING"}, {"name": "role", "type": "STRING"}, {"name": "title", "type": "STRING"}, {"name": "coordinates", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "latitude", "type": "FLOAT64"}, {"name": "longitude", "type": "FLOAT64"}]}, {"name": "defaultWalletId", "type": "STRING"}, {"name": "displayCurrency", "type": "STRING"}, {"name": "level", "type": "INTEGER"}, {"name": "statusHistory", "mode": "REPEATED", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "comment", "type": "STRING"}, {"name": "status", "type": "STRING"}, {"name": "updatedAt", "type": "TIMESTAMP"}, {"name": "updatedByUserId", "type": "STRING"}, {"name": "updatedByPrivilegedClientId", "type": "STRING"}]}, {"name": "username", "type": "STRING"}, {"name": "contacts", "mode": "REPEATED", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "id", "type": "STRING"}, {"name": "transactionsCount", "type": "INTEGER"}, {"name": "name", "type": "STRING"}]}, {"name": "notificationSettings", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "push", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "enabled", "type": "BOOLEAN"}, {"name": "disabledCategories", "type": "STRING", "mode": "REPEATED"}]}]}]}]}, {"name": "wallTime", "type": "TIMESTAMP"}]