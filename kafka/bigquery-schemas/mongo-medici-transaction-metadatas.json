[{"name": "_id", "type": "RECORD", "fields": [{"name": "_data", "type": "STRING"}]}, {"name": "clusterTime", "type": "TIMESTAMP"}, {"name": "documentKey", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}]}, {"name": "fullDocument", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "__v", "type": "INTEGER"}, {"name": "hash", "type": "STRING"}, {"name": "revealedPreImage", "type": "STRING"}]}, {"name": "ns", "type": "RECORD", "fields": [{"name": "coll", "type": "STRING"}, {"name": "db", "type": "STRING"}]}, {"name": "operationType", "type": "STRING"}, {"name": "updateDescription", "type": "RECORD", "fields": [{"name": "removedFields", "type": "STRING", "mode": "REPEATED"}, {"name": "truncatedArrays", "type": "STRING", "mode": "REPEATED"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "RECORD", "fields": [{"name": "revealedPreImage", "type": "STRING"}]}]}, {"name": "wallTime", "type": "TIMESTAMP"}]