[{"name": "_id", "type": "RECORD", "fields": [{"name": "_data", "type": "STRING"}]}, {"name": "clusterTime", "type": "TIMESTAMP"}, {"name": "documentKey", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}]}, {"name": "fullDocument", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "account", "type": "STRING"}, {"name": "balance", "type": "FLOAT64"}, {"name": "book", "type": "STRING"}, {"name": "createdAt", "type": "TIMESTAMP"}, {"name": "expireAt", "type": "TIMESTAMP"}, {"name": "key", "type": "STRING"}, {"name": "meta", "type": "STRING"}, {"name": "notes", "type": "INT64"}, {"name": "<PERSON><PERSON><PERSON>", "type": "STRING"}, {"name": "transaction", "type": "STRING"}]}, {"name": "ns", "type": "RECORD", "fields": [{"name": "coll", "type": "STRING"}, {"name": "db", "type": "STRING"}]}, {"name": "operationType", "type": "STRING"}, {"name": "wallTime", "type": "TIMESTAMP"}]