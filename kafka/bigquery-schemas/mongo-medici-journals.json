[{"name": "_id", "type": "RECORD", "fields": [{"name": "_data", "type": "STRING"}]}, {"name": "clusterTime", "type": "TIMESTAMP"}, {"name": "documentKey", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}]}, {"name": "fullDocument", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "_transactions", "type": "STRING", "mode": "REPEATED"}, {"name": "book", "type": "STRING"}, {"name": "datetime", "type": "TIMESTAMP"}, {"name": "memo", "type": "STRING"}, {"name": "voided", "type": "BOOLEAN"}, {"name": "void_reason", "type": "STRING"}]}, {"name": "ns", "type": "RECORD", "fields": [{"name": "coll", "type": "STRING"}, {"name": "db", "type": "STRING"}]}, {"name": "operationType", "type": "STRING"}, {"name": "updateDescription", "type": "RECORD", "fields": [{"name": "removedFields", "type": "STRING", "mode": "REPEATED"}, {"name": "truncatedArrays", "type": "STRING", "mode": "REPEATED"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "RECORD", "fields": [{"name": "void_reason", "type": "STRING"}, {"name": "voided", "type": "BOOLEAN"}]}]}, {"name": "wallTime", "type": "TIMESTAMP"}]