[{"name": "client_order_id", "type": "STRING", "mode": "REQUIRED"}, {"name": "correlation_id", "type": "STRING", "mode": "REQUIRED"}, {"name": "instrument", "type": "STRING", "mode": "REQUIRED"}, {"name": "action", "type": "STRING", "mode": "REQUIRED"}, {"name": "unit", "type": "STRING", "mode": "REQUIRED"}, {"name": "size", "type": "FLOAT64", "mode": "NULLABLE"}, {"name": "size_usd_value", "type": "FLOAT64", "mode": "NULLABLE"}, {"name": "target_usd_value", "type": "FLOAT64", "mode": "REQUIRED"}, {"name": "position_usd_value_before_order", "type": "FLOAT64", "mode": "REQUIRED"}, {"name": "complete", "type": "BOOLEAN", "mode": "REQUIRED"}, {"name": "lost", "type": "BOOLEAN", "mode": "REQUIRED"}, {"name": "created_at", "type": "TIMESTAMP", "mode": "REQUIRED"}, {"name": "order_id", "type": "STRING", "mode": "NULLABLE"}, {"name": "avg_price", "type": "FLOAT64", "mode": "NULLABLE"}, {"name": "fee", "type": "FLOAT64", "mode": "NULLABLE"}, {"name": "state", "type": "STRING", "mode": "NULLABLE"}]