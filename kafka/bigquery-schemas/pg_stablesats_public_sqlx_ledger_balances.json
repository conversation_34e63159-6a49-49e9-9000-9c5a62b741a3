[{"name": "journal_id", "type": "STRING"}, {"name": "account_id", "type": "STRING"}, {"name": "entry_id", "type": "STRING"}, {"name": "currency", "type": "STRING"}, {"name": "settled_dr_balance", "type": "FLOAT64"}, {"name": "settled_cr_balance", "type": "FLOAT64"}, {"name": "settled_entry_id", "type": "STRING"}, {"name": "settled_modified_at", "type": "TIMESTAMP"}, {"name": "pending_dr_balance", "type": "FLOAT64"}, {"name": "pending_cr_balance", "type": "FLOAT64"}, {"name": "pending_entry_id", "type": "STRING"}, {"name": "pending_modified_at", "type": "TIMESTAMP"}, {"name": "encumbered_dr_balance", "type": "FLOAT64"}, {"name": "encumbered_cr_balance", "type": "FLOAT64"}, {"name": "encumbered_entry_id", "type": "STRING"}, {"name": "encumbered_modified_at", "type": "TIMESTAMP"}, {"name": "version", "type": "INTEGER"}, {"name": "modified_at", "type": "TIMESTAMP"}, {"name": "created_at", "type": "TIMESTAMP"}]