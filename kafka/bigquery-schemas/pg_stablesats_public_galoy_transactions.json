[{"name": "id", "type": "STRING"}, {"name": "cursor", "type": "STRING"}, {"name": "is_paired", "type": "BOOLEAN", "mode": "NULLABLE"}, {"name": "settlement_amount", "type": "FLOAT64"}, {"name": "settlement_currency", "type": "STRING"}, {"name": "settlement_method", "type": "STRING"}, {"name": "direction", "type": "STRING"}, {"name": "memo", "type": "STRING", "mode": "NULLABLE"}, {"name": "cents_per_unit", "type": "FLOAT64"}, {"name": "amount_in_usd_cents", "type": "FLOAT64"}, {"name": "created_at", "type": "TIMESTAMP"}, {"name": "unpaired_last_checked_at", "type": "TIMESTAMP"}]