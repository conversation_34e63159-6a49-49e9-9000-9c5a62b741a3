[{"name": "_id", "type": "RECORD", "fields": [{"name": "_data", "type": "STRING"}]}, {"name": "clusterTime", "type": "TIMESTAMP"}, {"name": "documentKey", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}]}, {"name": "fullDocument", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "__v", "type": "STRING"}, {"name": "_accountId", "type": "STRING"}, {"name": "accountId", "type": "STRING"}, {"name": "currency", "type": "STRING"}, {"name": "id", "type": "STRING"}, {"name": "type", "type": "STRING"}, {"name": "onchain", "mode": "REPEATED", "type": "RECORD", "fields": [{"name": "oid", "type": "STRING"}, {"name": "address", "type": "STRING"}, {"name": "pubkey", "type": "STRING"}]}]}, {"name": "ns", "type": "RECORD", "fields": [{"name": "coll", "type": "STRING"}, {"name": "db", "type": "STRING"}]}, {"name": "operationType", "type": "STRING"}, {"name": "updateDescription", "type": "RECORD", "fields": [{"name": "removedFields", "type": "STRING", "mode": "REPEATED"}, {"name": "truncatedArrays", "type": "STRING", "mode": "REPEATED"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "RECORD", "fields": [{"name": "accountId", "type": "STRING"}, {"name": "onchain", "mode": "REPEATED", "type": "RECORD", "fields": [{"name": "_id", "type": "STRING"}, {"name": "address", "type": "STRING"}, {"name": "pubkey", "type": "STRING"}]}]}]}, {"name": "wallTime", "type": "TIMESTAMP"}]