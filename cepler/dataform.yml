deployment: dataform
environments:
  galoy-staging:
    latest:
    - .df-credentials.json
    - dataform.json
    - package.json
    - package-lock.json
    - Makefile
    - includes/**
    - definitions/**
    - ci/tasks/execute-dataform.sh
  galoy-bbw:
    passed: galoy-staging
    ignore_queue: true
    propagated:
    - .df-credentials.json
    - dataform.json
    - package.json
    - package-lock.json
    - Makefile
    - includes/**
    - definitions/**
    - ci/tasks/execute-dataform.sh
