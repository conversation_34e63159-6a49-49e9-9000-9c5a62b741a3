---
version: 411
current:
  head_commit: 92939b330a78a350ffdac1e4dac577078faf02bd
  propagated_head: c360dea2334468432b781ab3672e0444d0e97377
  files:
    "{galoy-staging}/.df-credentials.json":
      file_hash: 72f56df789a4e7d0c05b727bffe9ccae8414520c
      from_commit: 45e677962d313f1aafdbd7e0a9082952da71b3c6
      message: "chore: add dataform"
    "{galoy-staging}/Makefile":
      file_hash: 12c939c791a7dfab7d290498cb3e7e2974d6409d
      from_commit: bea4ccf8b851639a19b4f24078f62c82db864fa9
      message: "feat: reg report list (#1391)"
    "{galoy-staging}/ci/tasks/execute-dataform.sh":
      file_hash: 2f9424a7428596eff9b214ccf7824bcc7609c11a
      from_commit: 15d51464eefbcee4ef7f441fe2cc60d6bfaff4fe
      message: "fix: revert breaking `make install-lava` in ci/tasks/execute-dataform.sh"
    "{galoy-staging}/dataform.json":
      file_hash: 94601a0c769ee20161cdc15d698d6b584815242c
      from_commit: f8b692873cf493e9d2dbd3ce2eb9d0eac9dbe949
      message: "chore: initial model definition"
    "{galoy-staging}/definitions/assertions/assert_avg_open_prices.sqlx":
      file_hash: be08aa325abb2177c22852ba94875f3b55496ef8
      from_commit: f9dfd3042bbc9c79da1da4ab5d7fe3b0139298f9
      message: "Revert \"fix!: disable assert_avg_open_prices to unblock daily dataform (#1457)\" (#1459)"
    "{galoy-staging}/definitions/assertions/assert_bills_archive_function.sqlx":
      file_hash: adce0918471539403bb0c01aa981ad2c9b839cbd
      from_commit: b6d0325821ede13be581eeaadc5d85d62ee7e286
      message: "fix: temporarily disable okex bills assertions (#1435)"
    "{galoy-staging}/definitions/assertions/assert_bills_function.sqlx":
      file_hash: ad14c67fb4b887ae0aacd00bb1245511a7feebf5
      from_commit: b6d0325821ede13be581eeaadc5d85d62ee7e286
      message: "fix: temporarily disable okex bills assertions (#1435)"
    "{galoy-staging}/definitions/assertions/assert_coincap_price_function.sqlx":
      file_hash: cee8b6bcaded97f6d82af4d6460e2adbe76a0838
      from_commit: bdb7d25aee854a2682b197923a246ae9c21ca679
      message: "feat: assert that cloud functions are adding new rows as expected (#863)"
    "{galoy-staging}/definitions/assertions/assert_email_updates_unique_key.sqlx":
      file_hash: 443f617dcd12ca299d1be2e44a6190531ae63f8c
      from_commit: 93278c1651567939089685d0523e339f8573a056
      message: "feat: keep track of users adding their emails for authentication (#1389)"
    "{galoy-staging}/definitions/assertions/assert_gossip_function.sqlx":
      file_hash: 39dc658db3b31b79a42fc6d8f411f0e5ae814235
      from_commit: 18ac2ea7aec8299bcf50e449c4e0b1bab427ae84
      message: "chore: allow for 30 days without gossip updates"
    "{galoy-staging}/definitions/assertions/assert_inactive_ip.sqlx":
      file_hash: 700bbc0fb6876cffe93854036046af132d59a00b
      from_commit: c360dea2334468432b781ab3672e0444d0e97377
      message: "fix: disable assertion assert_inactive_ip (#1473)"
    "{galoy-staging}/definitions/assertions/assert_intraledger_balance.sqlx":
      file_hash: fdfafdddc98d48840912a374749ef09a41cc9c84
      from_commit: c560720f3b6fb40b0a17bff5826bb23eedb613f9
      message: "chore: add balance assertions for ledger (#637)"
    "{galoy-staging}/definitions/assertions/assert_journal_types.sqlx":
      file_hash: 00b724451fe7f211d445352034cf29ae7058b616
      from_commit: ba3e01f5b64b80f57b69dee1da3435bc8405573a
      message: "chore: rename assertions"
    "{galoy-staging}/definitions/assertions/assert_okex_bid_ask_function.sqlx":
      file_hash: ca7f54f52d78128bc49ad36113ec6815bfaeb493
      from_commit: bdb7d25aee854a2682b197923a246ae9c21ca679
      message: "feat: assert that cloud functions are adding new rows as expected (#863)"
    "{galoy-staging}/definitions/assertions/assert_onchain_fees.sqlx":
      file_hash: be19cd5b71abeb9fe14114365b650bf6d625311a
      from_commit: 317f8abec2ed88020e2fd4d2cf1b511dfa97e1a1
      message: "fix: account for revenue from onchain deposit fees"
    "{galoy-staging}/definitions/assertions/assert_pg_imports.sqlx":
      file_hash: 36a4c9abb6f56bfe1701d3d87a5f70476653cfbe
      from_commit: 194902bd3befca4f71a12ca8f3b3d12b6649b720
      message: "chore: disable assert_pg_imports to unblock concourse"
    "{galoy-staging}/definitions/assertions/assert_pg_imports_output.sqlx":
      file_hash: b6a5aeba0e873ab39545c0fcb72e2151acb26c76
      from_commit: 6edc8bdf2dc0ecc65c7d7ecd5a168672bc4e21a5
      message: "fix: user `resolve` instead of `ref` for assertion output (#1280)"
    "{galoy-staging}/definitions/assertions/assert_positive_balances.sqlx":
      file_hash: 2b8972ac79709c91f3199a683f045d2840be0951
      from_commit: c560720f3b6fb40b0a17bff5826bb23eedb613f9
      message: "chore: add balance assertions for ledger (#637)"
    "{galoy-staging}/definitions/assertions/assert_wallet_currencies.sqlx":
      file_hash: ad75330c5c36f92e940f63ce84ecb79e532e5f03
      from_commit: 6788c3e8afbe391807360ff1bdbda0b29947ee07
      message: "chore: cleanup stg_journal_entries (#1072)"
    "{galoy-staging}/definitions/assertions/assert_wallet_journal_entries.sqlx":
      file_hash: f292fe9642a35fcd06a1b3ae8b76377cb5ece0b8
      from_commit: e403a2081fc73cff2034dc244b50d6bb9f5b0a2d
      message: "chore: exclude static accounts from wallets in a journal check"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-08-22/report_ago20230822_summaries.sqlx":
      file_hash: 992bf0278145482175a090d7a9a51d5cdb9348ef
      from_commit: c4e9b8751614e785396381f786bcd8e40130e3cc
      message: "feat: add IP addresses to tvf_user_metadata"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-08-22/report_ago20230822_transactions.sqlx":
      file_hash: fb474b5b96b1e12bc4d14daa0bed2246a12b2d29
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-08-22/stg_ago20230822_phones.sqlx":
      file_hash: 272ef5c27adac800cecaa50dae63707d220cb1dd
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-10-26/report_ago20231026_summaries.sqlx":
      file_hash: 7fa26277eaa9fe576e379233454d3ce435ae11c4
      from_commit: c4e9b8751614e785396381f786bcd8e40130e3cc
      message: "feat: add IP addresses to tvf_user_metadata"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-10-26/report_ago20231026_transaction_counter_parties.sqlx":
      file_hash: e02e69f513688fcc1c7fd5caa928bed4e4bad37b
      from_commit: c4e9b8751614e785396381f786bcd8e40130e3cc
      message: "feat: add IP addresses to tvf_user_metadata"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-10-26/report_ago20231026_transactions.sqlx":
      file_hash: 85b1be2c50c8df2d86b595dc12a4d0aa78c42478
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-10-26/report_ago20231026_victims.sqlx":
      file_hash: 52a2006253911423ee84b241fff026953104bbeb
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/ago2023-10-26/stg_ago20231026_phones.sqlx":
      file_hash: c409cd2dabdf2e390240529c3112ae8147c3630a
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/blink-dot-sv/fct_daily_blinksv_first_visits.sqlx":
      file_hash: a42206dde471baf7f988c1c7500da2380b1de5a5
      from_commit: 77c37ec9fdaee9943f5caeebc1520aff09de3525
      message: "chore: add forward shifted date for weekly updates (#1243)"
    "{galoy-staging}/definitions/env-specific/bbw/blink-dot-sv/stg_daily_blinksv_first_visits.sqlx":
      file_hash: 1a437d40ca146448993e28410dcc3193731b206c
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/assert_report_circles_number_of_users.sqlx":
      file_hash: ba3e50973cbcdb6d0a53a3eecf8d1f389c8d0ae6
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_circle_updates.sqlx":
      file_hash: ****************************************
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_circles.sqlx":
      file_hash: ****************************************
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_circles_challenge_202309.sqlx":
      file_hash: 321acb65faa64e0e3aaab3fef1e741d517e36fe1
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_circles_challenge_202310.sqlx":
      file_hash: 2442425c8a30cd6e18aaa2673d8ad72a7605916b
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_circles_challenge_202311.sqlx":
      file_hash: d7ca77370d7a019a36c87507d0ad070b29d7a0be
      from_commit: 7ec6e27c4fbf04c2ac64a03497d779fc18721f33
      message: "chore: circles stragglers"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_circles_challenge_202312.sqlx":
      file_hash: e78663e6df4606bda81405ca4c304d107d43011e
      from_commit: 2adf30491756d155c4277ef04b1e459dffd54726
      message: "chore: fix array syntax"
    "{galoy-staging}/definitions/env-specific/bbw/circles/report_rt_circles.sqlx":
      file_hash: ****************************************
      from_commit: 91180923f2871255bafb1da37c40f3354100e3ae
      message: "chore: add kratos user_id to report_rt_circles (#1108)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_batch_onboarding_graph.sqlx":
      file_hash: c6c6779af6017589e32cfd70ca807e5f230691b9
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_batch_onboarding_graph_ordered.sqlx":
      file_hash: be86368a9a059e19aae391fa5ad45992ad9b5dba
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_rt_circles_accounts.sqlx":
      file_hash: ****************************************
      from_commit: 308f6e7e59ecdfa991ae8dc1217c2305f047541d
      message: "fix: cluster kafka medici_transactions table by operationType (#1366)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_rt_circles_wallets.sqlx":
      file_hash: ****************************************
      from_commit: 308f6e7e59ecdfa991ae8dc1217c2305f047541d
      message: "fix: cluster kafka medici_transactions table by operationType (#1366)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_rt_new_onboardings.sqlx":
      file_hash: 7a4652685e6d0b1e207094c83ae9ed8385132011
      from_commit: 41eddf206b12ce7057bea8431d8805b4d1bf9e50
      message: "chore: small optimizations for circles query (#1111)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_rt_onboarding_graph.sqlx":
      file_hash: 6b80bc3a9aafbb9f91602ebd83398c901b7c0801
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/stg_rt_onboarding_graph_ordered.sqlx":
      file_hash: 7173ef082333b21e02160c387bf42b1f9fe42a72
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/test_onboarding_graph.sqlx":
      file_hash: 081ed3d571568f106abfbbeb2df70f1d56f9f7bc
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/circles/test_rt_onboarding_graph.sqlx":
      file_hash: d82afd3f39f875a8f7ef10a8d7eee0c5fe4b580d
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/fiat/stg_blink_fiat_transactions.sqlx":
      file_hash: 557592a7cb53df3e89879e7abd20a3d75a4ade8e
      from_commit: a78e94087b24fe68d230e0c9852ea06ff1074c26
      message: "chore: add both old and new blink fiat account ids (#1438)"
    "{galoy-staging}/definitions/env-specific/bbw/fiat/stg_blink_fiat_transactions_accounting.sqlx":
      file_hash: 80e477d0e71f4ac9f0a6e9a38bcce2d11c0196e2
      from_commit: 9e9d24f23c8ad23d2aabad6a0995e5d3abe5413c
      message: "chore: add usd_blink_fiat_revenue_accounting to fct_daily_user_summaries (#1448)"
    "{galoy-staging}/definitions/env-specific/bbw/onfido/assert_onfido_workflow_runs_function.sqlx":
      file_hash: 173848fadf667071db2b457a66e629e50e2001d7
      from_commit: 7604dfaae39b86220853c8e63966d633659c5c69
      message: "chore: disable check for weekly updates on staging onfido import"
    "{galoy-staging}/definitions/env-specific/bbw/onfido/fct_onfido.sqlx":
      file_hash: e562103af1f66f10ed54e721b414637d422adba5
      from_commit: 1fa5b39b5edca3012e966a5b35ee94f679695b0f
      message: "feat: simple AML \"alerts\" query (#1200)"
    "{galoy-staging}/definitions/env-specific/bbw/onfido/report_onfido_es_applicants.sqlx":
      file_hash: cf7e696b026f821c030eaa89774c0704a51a3d1c
      from_commit: 1fa5b39b5edca3012e966a5b35ee94f679695b0f
      message: "feat: simple AML \"alerts\" query (#1200)"
    "{galoy-staging}/definitions/env-specific/bbw/onfido/stg_onfido_identities.sqlx":
      file_hash: 97c3f029927115863a92380a4bcfab111d9cae39
      from_commit: 5b87cf0593e9c9674c44bee29d90d9dfcc2b2237
      message: "feat: onfido workflow runs fact table (#1038)"
    "{galoy-staging}/definitions/env-specific/bbw/onfido/stg_onfido_workflow_runs.sqlx":
      file_hash: 0a72d33864d649aa8dac6909d9f5e18c83eca075
      from_commit: 1fa5b39b5edca3012e966a5b35ee94f679695b0f
      message: "feat: simple AML \"alerts\" query (#1200)"
    "{galoy-staging}/definitions/env-specific/bbw/report_es_active_merchants.sqlx":
      file_hash: 003b588e011f738da13365ccce2333333653aa5b
      from_commit: 2e7ff0654ec994d6c195023902a6162c270a45f6
      message: "chore: report active El Salvador merchant phone numbers (#1165)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/report_solette_account_ips.sqlx":
      file_hash: ae152a21450fa544be991f3907466971ee3f61ef
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/report_solette_account_metadata.sqlx":
      file_hash: 8d75d98db725b6da34fdc15eb30ff786bd97b6b9
      from_commit: c4e9b8751614e785396381f786bcd8e40130e3cc
      message: "feat: add IP addresses to tvf_user_metadata"
    "{galoy-staging}/definitions/env-specific/bbw/solette/report_solette_account_transaction_summaries.sqlx":
      file_hash: 029c025091107909643b712dcc4140db28213213
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/report_solette_counter_parties.sqlx":
      file_hash: 57a2ec7a66e2dc33b6d80fdcd62f770744c43b4b
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/report_solette_overall_summary.sqlx":
      file_hash: de75781918d62550ab34cdb10be6f67125c30379
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/stg_solette_account_balances.sqlx":
      file_hash: b398ec72dd087e7331805db7ba3fbc61b3253146
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/stg_solette_account_transactions.sqlx":
      file_hash: f1f77103050ebbd6dee096e57f6d3dd8bdc807df
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/solette/stg_solette_accounts.sqlx":
      file_hash: 34675802e244da1bc69e0d1b9c1c19be5880921d
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/bbw/ssf-audit2024/repot_ssf_example_accounts.sqlx":
      file_hash: 0642c6087c6d9d1238bc6035ef3c18db390639a4
      from_commit: cda45f9b5e1e8c34195242e2e845ccea25ad73d1
      message: "chore: example reports for test accounts created for SSF audit"
    "{galoy-staging}/definitions/env-specific/bbw/ssf-audit2024/repot_ssf_example_transactions.sqlx":
      file_hash: 266aa84761c4f8b9c37c72f851c74d632ca4e61e
      from_commit: 351d024d69997efaf13411add58f6956a919da1a
      message: "chore: additional transactions for ssf example reports"
    "{galoy-staging}/definitions/env-specific/bbw/stablesats/report_daily_summary.sqlx":
      file_hash: ca203db6405328ce815d499f8056d3dd0548c3d5
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_custom_merchants.sqlx":
      file_hash: 2bc0b15fc755a1f85bb5407ae82d8e740429d7c8
      from_commit: dd128d9ff40d56e13c5df52fcaef86762402cb4e
      message: "chore: add dimension for blink merchant onboarding (#1345)"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_daily_user_facts.sqlx":
      file_hash: cbf4dceefc69a772e5e59fbb22a2104c7c7774f0
      from_commit: 9447417286794e9b8c5002868e24f0bffc35ff2f
      message: "chore: blink-fiat Sell order revenue in daily user summaries"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_daily_user_facts_accounting.sqlx":
      file_hash: 103487a28307f1851300f9d82857e2693062e81b
      from_commit: 9e9d24f23c8ad23d2aabad6a0995e5d3abe5413c
      message: "chore: add usd_blink_fiat_revenue_accounting to fct_daily_user_summaries (#1448)"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_merchant_onboarding.sqlx":
      file_hash: 7b23e4c02ec9b293cd5773505586a9b7a7c66db1
      from_commit: 207d629014b4d7d10a8064b781045bcf4d8f3af6
      message: "chore: new onboarded merchants"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_user_dimensions.sqlx":
      file_hash: 956aec0abed46aac720a4e5d575def7e2597c3c9
      from_commit: 4a7ccc59ceb1be8a42122a0c875591b3991c9f30
      message: "feat: use community leader accounts to add dimensions for communities (#1357)"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_user_wallet_transaction_facts.sqlx":
      file_hash: e017ed0552130aedf298202cb7bea0c26a4d3c73
      from_commit: 2d0cef7b66457036ec641e63add552ef2396c0da
      message: "feat: blink-fiat sell Bitcoin revenue (#1070)"
    "{galoy-staging}/definitions/env-specific/bbw/stg_blink_user_wallet_transaction_facts_accounting.sqlx":
      file_hash: a75ca25781e4c2d3002bd0f7168578a101f2a634
      from_commit: 9e9d24f23c8ad23d2aabad6a0995e5d3abe5413c
      message: "chore: add usd_blink_fiat_revenue_accounting to fct_daily_user_summaries (#1448)"
    "{galoy-staging}/definitions/env-specific/bbw/stg_dismo.sqlx":
      file_hash: a9baa150e12df09fc2b54e6579c38f8f35b26394
      from_commit: aba0e436c7ecc6732618cfcb6d6a925eaaea2efb
      message: "chore: set correct city for `costenoazulsv`"
    "{galoy-staging}/definitions/env-specific/bbw/theft-connected-accounts/stg_theft_connected_accounts.sqlx":
      file_hash: 69d09735437a598a841eab25504038cc3a4d4cd0
      from_commit: c93f6e4fae60e61d3e44b35f3e7ff838ed069e35
      message: "chore: reveal accounts associated with a theft of funds (#1063)"
    "{galoy-staging}/definitions/env-specific/fct_bigquery_jobs.sqlx":
      file_hash: 654d181c3466ae89209d13770ddba727d80bedf7
      from_commit: a0310b1fd5eb6b7ff2c25c196328e65dff369f64
      message: "feat: fct_google_cloud_bills (#1355)"
    "{galoy-staging}/definitions/env-specific/fct_google_cloud_bills.sqlx":
      file_hash: afd75a51cd01834de8489c12ab2375cda46cf1cf
      from_commit: 93278c1651567939089685d0523e339f8573a056
      message: "feat: keep track of users adding their emails for authentication (#1389)"
    "{galoy-staging}/definitions/env-specific/stg_env_custom_merchants.sqlx":
      file_hash: 84627dbfacc68a3bfea8bad95d22e046d0f15db1
      from_commit: 5aef1b16e0cb1e26fd174a4824a0bc843bc4aad5
      message: "chore: rename `definitions/reports` to `definitions/env-specific` (#1026)"
    "{galoy-staging}/definitions/env-specific/stg_env_daily_user_facts.sqlx":
      file_hash: d3bf345c3019dbb1d5917b9d9bab6060050bff9c
      from_commit: 9447417286794e9b8c5002868e24f0bffc35ff2f
      message: "chore: blink-fiat Sell order revenue in daily user summaries"
    "{galoy-staging}/definitions/env-specific/stg_env_daily_user_facts_accounting.sqlx":
      file_hash: ee014897d046750b716032196faa0ff3e83cc7d4
      from_commit: 9e9d24f23c8ad23d2aabad6a0995e5d3abe5413c
      message: "chore: add usd_blink_fiat_revenue_accounting to fct_daily_user_summaries (#1448)"
    "{galoy-staging}/definitions/env-specific/stg_env_user_dimensions.sqlx":
      file_hash: 957c5590735b3c54c86bf143499e471bc92e1419
      from_commit: 4a7ccc59ceb1be8a42122a0c875591b3991c9f30
      message: "feat: use community leader accounts to add dimensions for communities (#1357)"
    "{galoy-staging}/definitions/env-specific/stg_env_user_wallet_transaction_facts.sqlx":
      file_hash: 57a508de9890062d215a57845b90d7cffabff148
      from_commit: 2d0cef7b66457036ec641e63add552ef2396c0da
      message: "feat: blink-fiat sell Bitcoin revenue (#1070)"
    "{galoy-staging}/definitions/models/dim_accounting_recently_active_accounts.sqlx":
      file_hash: c18c370c6c8c78ec2e8cb9adfc1186d02f19e486
      from_commit: 2361fc48c19b0bb9da3a3090cb2bf52e4de1c0dc
      message: "fix: permissions for dim_accounting_recently_active_accounts"
    "{galoy-staging}/definitions/models/dim_counter_parties.sqlx":
      file_hash: 66e221ed4c33602f462ddc8fa4197de5db943618
      from_commit: 4a7ccc59ceb1be8a42122a0c875591b3991c9f30
      message: "feat: use community leader accounts to add dimensions for communities (#1357)"
    "{galoy-staging}/definitions/models/dim_merchant_stores.sqlx":
      file_hash: 4a53f1fca98f2a925978a8b4c2327245711e4dac
      from_commit: 514fc84591eb022f9441bb97fa9e50ad3a713c13
      message: "feat: refactor merchant tables to allow for merchants with multiple stores (#1180)"
    "{galoy-staging}/definitions/models/dim_regions.sqlx":
      file_hash: 76832ac5654ab6245d7869a371c09effff6fc12a
      from_commit: 5aa78960be21d4baf0967bfe061a5e55bffdf206
      message: "feat: dim_regions (#1175)"
    "{galoy-staging}/definitions/models/dim_user_clusters.sqlx":
      file_hash: eead066cc41976cac6c98f1b6fc67cadc679dd6d
      from_commit: e911cd6619b23bf539438e3cf11e4378015d5f3d
      message: "fix: do not label clusters with less than 100 users"
    "{galoy-staging}/definitions/models/dim_user_wallets.sqlx":
      file_hash: f55d53f649649a1850c826d600031c0a5a031036
      from_commit: d1d6ea7f630d37465ab4c686eed1be0e352417c9
      message: "fix: use new key field in dim_user_wallets"
    "{galoy-staging}/definitions/models/dim_users.sqlx":
      file_hash: d4279c0a2eec1c6fe454e24d72ece763998ac3d7
      from_commit: 3bb200fdf1a474f54b8b01b82973ee3049a70a6c
      message: "feat: add sat_onchain_fee_revenue, sat_stablesats_spread_revenue (#1437)"
    "{galoy-staging}/definitions/models/fct_accounting_recent_transactions.sqlx":
      file_hash: 3cdf680e6911991e0cfa20ae458c92880fd50e2a
      from_commit: f4cc9caad68ab7b28fef41eb41e14173f46bc6dc
      message: "feat: sweeping improvements for recent transaction models (#1288)"
    "{galoy-staging}/definitions/models/fct_daily_user_summaries.sqlx":
      file_hash: 91ca029485d4b3a654af7993c182b238608ddc66
      from_commit: 63cde42090b7313b62ff59aecffe0d25fd71d3e2
      message: "fix: use stg_env_daily_user_facts in fct_daily_user_summaries (#1455)"
    "{galoy-staging}/definitions/models/fct_dealer_metrics.sqlx":
      file_hash: e5f356a812f459645d6da04087b3bc136b0505dd
      from_commit: ff9f04bfedb5f030df83bbc26c7e96c48e73329b
      message: "chore: streamline names joins unions (#1177)"
    "{galoy-staging}/definitions/models/fct_funding_yields.sqlx":
      file_hash: 08c30c70ba4e5e1c4eeeaefb68b8e741a38c6897
      from_commit: a4635d304a22347277f119ff9c207761b866ec6d
      message: "chore: transition real time tables to views (and add authorization for stablesats dataset)"
    "{galoy-staging}/definitions/models/fct_liability_reconciliation.sqlx":
      file_hash: e80bb551b04c08c2775c7849047e0b84724579f6
      from_commit: 45ba60c127e9feb4587f7c3178cd46feb8a10953
      message: "feat: unrealized pnl explain (#1178)"
    "{galoy-staging}/definitions/models/fct_lightning_liquidity_routes.sqlx":
      file_hash: d490859a7750986426a442c4d8f3084695d4a71f
      from_commit: f99df1e1c8a1cdbdf1a42159b59392a57c368811
      message: "feat: use new gossip sync dataset to finally add node aliases to lightning network models (#1016)"
    "{galoy-staging}/definitions/models/fct_okex_external_transfers.sqlx":
      file_hash: ec0a3aef293d8a53e9a67c034a3d84ad381d75db
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/models/fct_okex_internal_transfers.sqlx":
      file_hash: 85f65b54069cfabc33ebea01ba1c9cd10fb28923
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/models/fct_okex_reported_orders.sqlx":
      file_hash: 2b55388a155fe6c961bf3454a6d96bf548b21bcc
      from_commit: f58ba45a609897af892ff13c69ea7d89dac2d01c
      message: "chore: okex reported orders (#1230)"
    "{galoy-staging}/definitions/models/fct_pnl_explain.sqlx":
      file_hash: 676583fae72fdd8dd6338a3ef9925f5e6431b9f8
      from_commit: e13439047d0855264f995b9d6150ee2bd2e86246
      message: "feat: pnl explain (#890)"
    "{galoy-staging}/definitions/models/fct_stablesats_internal_okex_orders.sqlx":
      file_hash: ce3ed917e5cb3d414523e6da0cba45ca5eaa9d20
      from_commit: da187d474c3cc1b752d521de5b138f2c974e62b7
      message: "feat: okex reported orders (#1099)"
    "{galoy-staging}/definitions/models/fct_stablesats_profit_and_loss.sqlx":
      file_hash: c2edc3fa6b8d1c0cd9fc7b2c047da0e390cd693c
      from_commit: ff9f04bfedb5f030df83bbc26c7e96c48e73329b
      message: "chore: streamline names joins unions (#1177)"
    "{galoy-staging}/definitions/models/fct_unrealized_pnl_explain.sqlx":
      file_hash: 6b479d10aa2ae56b0ff85a007756402b42975ec3
      from_commit: 45ba60c127e9feb4587f7c3178cd46feb8a10953
      message: "feat: unrealized pnl explain (#1178)"
    "{galoy-staging}/definitions/models/fct_user_account_updates.sqlx":
      file_hash: 3fc1be09a2d9a2d9eae3f850e078f07651c4db08
      from_commit: c8d85eb5b04fe6c73aa978aada9a98e55ac81658
      message: "fix: do not filter first update for fct_user_account_updates"
    "{galoy-staging}/definitions/models/fct_user_app_removes.sqlx":
      file_hash: f8f58fef0a3d6db1474631c8198c399974da79c9
      from_commit: 35984d591c5cc5156505d2f80b4e59486552edd3
      message: "fix: replace JOIN with OR condition with JOIN to redundant table (#973)"
    "{galoy-staging}/definitions/models/fct_user_onboarding.sqlx":
      file_hash: 7dc9e7b12c8815e4c39fddcc05ef70fe9a5341da
      from_commit: 9a54ebba732336cb861ba3fa1a64949ee3b1901c
      message: "fix: migrate from accounts.oid to accounts.id for account_id (#918)"
    "{galoy-staging}/definitions/models/fct_user_screen_views.sqlx":
      file_hash: b0b04b1b0a023802e988db245dc873c75427778d
      from_commit: 35984d591c5cc5156505d2f80b4e59486552edd3
      message: "fix: replace JOIN with OR condition with JOIN to redundant table (#973)"
    "{galoy-staging}/definitions/models/fct_user_sessions.sqlx":
      file_hash: e41c141ce494c086c3c6efc1a7ab7092bbab3fb9
      from_commit: 35984d591c5cc5156505d2f80b4e59486552edd3
      message: "fix: replace JOIN with OR condition with JOIN to redundant table (#973)"
    "{galoy-staging}/definitions/models/fct_user_wallet_lightning_payments.sqlx":
      file_hash: b736dbd20abf0469285e16c956708c2c569988ae
      from_commit: 64a8d894a3ae3a28ed18963965bce02cb354ab75
      message: "chore: cleanup remaining issues with the pseudo_destination_alias"
    "{galoy-staging}/definitions/models/fct_user_wallet_transactions.sqlx":
      file_hash: 9390b31cce5a1d68f3bc6b48c294a750af94ebf3
      from_commit: 34ac1c11706a04a06edc421e93d9073ca0fbcf28
      message: "chore: add sat imbalance to user_wallet_transactions (#1151)"
    "{galoy-staging}/definitions/queries/query_2023_mom_kpi_growth_by_region.sqlx":
      file_hash: bc7f36cc0802da58fa48308ffe9874a9d7966ef8
      from_commit: af7a54d0af8fcb7fbccb07d50828131b5f575292
      message: "fix: count only transacting users in 2023_mom_kpi_growth query (#1127)"
    "{galoy-staging}/definitions/queries/query_aml_alerts.sqlx":
      file_hash: ad64eccad8df382d1947007febe13e52d0a8f2f6
      from_commit: 14ecc229381dca66cc3c0dec2883adc0dd0733d3
      message: "chore: clean up initial AML alerts query (#1215)"
    "{galoy-staging}/definitions/queries/query_fiu_transactions_fiat.sqlx":
      file_hash: 4f44efd307135bd72b860d1ea0aecc97d31254af
      from_commit: 4df5340eb63338844f7e83eaeeee4b5720806021
      message: "chore: queries to generate reports for transactions >$1,000 for the FIU (#1318)"
    "{galoy-staging}/definitions/queries/query_fiu_transactions_interledger.sqlx":
      file_hash: 5fe2a6b2d8b8ed5a6ec74aa025b517ca5ee28318
      from_commit: 4df5340eb63338844f7e83eaeeee4b5720806021
      message: "chore: queries to generate reports for transactions >$1,000 for the FIU (#1318)"
    "{galoy-staging}/definitions/queries/query_fiu_transactions_intraledger.sqlx":
      file_hash: c5770628f729edb4528f93574632bc667f06a02e
      from_commit: 4df5340eb63338844f7e83eaeeee4b5720806021
      message: "chore: queries to generate reports for transactions >$1,000 for the FIU (#1318)"
    "{galoy-staging}/definitions/queries/user-data-requests/query_user_data_request_2024_08_15.sqlx":
      file_hash: bd1e7bda72e2761c5ccc08af04aef661a7a377f9
      from_commit: 2c117c3fcb2da3d6d3d1e453029bdcc8ac46cd0d
      message: "chore: fix query name"
    "{galoy-staging}/definitions/sources/apikeys.js":
      file_hash: 569803f3880636d5621d6c8ae7345e54fa554b39
      from_commit: 696dada7a86f26647d50fa207dbd839c1aed8a77
      message: "feat: dimensions for api keys (#1054)"
    "{galoy-staging}/definitions/sources/blink-kyc.js":
      file_hash: 5599df2ccba69e781c735a609acb664886927793
      from_commit: 5b87cf0593e9c9674c44bee29d90d9dfcc2b2237
      message: "feat: onfido workflow runs fact table (#1038)"
    "{galoy-staging}/definitions/sources/bria.js":
      file_hash: 3952fdac73f173087d39228d69230592276b0701
      from_commit: 358579dfa411e04bba254ffaf2d33d5d89b11c01
      message: "chore: copy bria tables to dataform dataset"
    "{galoy-staging}/definitions/sources/functions.js":
      file_hash: c8b029a6b72475b2368e28d29312dbc12a83004b
      from_commit: 2ae9dda3472e2e94065777adb44a07b1f473736f
      message: "chore: incrementaly parse responses from bitfinex API"
    "{galoy-staging}/definitions/sources/galoy.js":
      file_hash: c9817f553528181eadd62cc8998bb4ba2ed6231a
      from_commit: a98706b30745737ca2091a7e5de3414d3d56c9e6
      message: "chore: user `merchants` collection instead of `maps` collection (#1160)"
    "{galoy-staging}/definitions/sources/kafka.js":
      file_hash: 7dc436ce67d381172bc9274b13b1a374e084ff27
      from_commit: 4f0cdc5d9ba7f0c51e69529cb7f3adca4b9c7d51
      message: "feat: real time circles tables (#792)"
    "{galoy-staging}/definitions/sources/kratos.js":
      file_hash: 82c139e2ef11f7b121982448f65d5c980555196d
      from_commit: c6f8d3cc138c63eadb8395011ff2d5dbe591b31b
      message: "chore: add dimension for totp two factor (#1336)"
    "{galoy-staging}/definitions/sources/stablesats.js":
      file_hash: ff65833477ee3f09baa807a7550e36965bfafe91
      from_commit: 84805250f2fd7312cadb312ce34fb6f846041787
      message: "feat: add stablesats okex transfers table"
    "{galoy-staging}/definitions/staging/stg_account_emails.sqlx":
      file_hash: f4f09e6bc90ae20efc68af41cfdc03d89fba09d5
      from_commit: 6d2211bc2424b9dff15ff5f56e13032a2aa7de91
      message: "chore: remove ordering in queries that generate disposable keys for internal ids (#1173)"
    "{galoy-staging}/definitions/staging/stg_account_ips.sqlx":
      file_hash: dc4eac6cfd35a71da18583c2e9d11fddfab193f3
      from_commit: 6d2211bc2424b9dff15ff5f56e13032a2aa7de91
      message: "chore: remove ordering in queries that generate disposable keys for internal ids (#1173)"
    "{galoy-staging}/definitions/staging/stg_account_merchants.sqlx":
      file_hash: 4f8240089f6332e8f7534793a6f88ff9331f0d53
      from_commit: 514fc84591eb022f9441bb97fa9e50ad3a713c13
      message: "feat: refactor merchant tables to allow for merchants with multiple stores (#1180)"
    "{galoy-staging}/definitions/staging/stg_account_phones.sqlx":
      file_hash: bccbde8b3af669be61ea6af5176c58c994d0fd45
      from_commit: 3f488a42d748c02dbe7e236ab0848a64ad01dac3
      message: "feat: improved phone number country code determination (#1254)"
    "{galoy-staging}/definitions/staging/stg_accounting_recent_transactions.sqlx":
      file_hash: 6235f4086b9252717a9792e953b90e71d201fe2b
      from_commit: 8a2baf7bfee0f440df2158dab6b511991f4cfabf
      message: "chore: show transaction history from 2024-09-01 (#1432)"
    "{galoy-staging}/definitions/staging/stg_accounting_recently_active_accounts.sqlx":
      file_hash: e2d35a239743865169a5836215fa9aaf47226f60
      from_commit: 157b33a43ac837e868e6d4e8086dda7966eec1e5
      message: "fix: permissions for stg_accounting_recently_active_accounts"
    "{galoy-staging}/definitions/staging/stg_accounts.sqlx":
      file_hash: 3f5f43c85d8217fd433fd1b45b416e07233418b3
      from_commit: c6f8d3cc138c63eadb8395011ff2d5dbe591b31b
      message: "chore: add dimension for totp two factor (#1336)"
    "{galoy-staging}/definitions/staging/stg_accounts_raw.sqlx":
      file_hash: aa7d5e6a97611a82630db9902a22fcdee92f09a9
      from_commit: 29646e1e7c30c651c9a8b7257d77563f5babec96
      message: "chore: fix dataform errors caused by inconsistencies between staging and bbw dbs"
    "{galoy-staging}/definitions/staging/stg_accounts_scd.js":
      file_hash: d296ca8bdbef1e834fcb5ce0d52d94b1bd927276
      from_commit: 8a2f9b105419a33cf5ffbc2d7f2bc502f120f72c
      message: "feat: generate slowly changing dimension table for users (#633)"
    "{galoy-staging}/definitions/staging/stg_accounts_scd_source.sqlx":
      file_hash: 678db1f206ef147ed91f65264dfa021b850e715e
      from_commit: 7449fc8455891a3668afe11647a902f9c1d1a1d6
      message: "chore: remove withdrawFee from `stg_accounts`"
    "{galoy-staging}/definitions/staging/stg_aml_watchlist_compare.sqlx":
      file_hash: 4853005c7bb05b02ccf370fbe6bf9a1ef464e5f7
      from_commit: afc6fbfbba126cdc3d49fe424cea51ce087f50de
      message: "feat: aml watchlist as complete views (#1343)"
    "{galoy-staging}/definitions/staging/stg_aml_watchlist_download.sqlx":
      file_hash: d1640d12e82ba965a3a7db0806f0484ee3c4db71
      from_commit: afc6fbfbba126cdc3d49fe424cea51ce087f50de
      message: "feat: aml watchlist as complete views (#1343)"
    "{galoy-staging}/definitions/staging/stg_aml_watchlist_progress.sqlx":
      file_hash: 5d22550aac9f599034a5b661466d831d9fe72042
      from_commit: afc6fbfbba126cdc3d49fe424cea51ce087f50de
      message: "feat: aml watchlist as complete views (#1343)"
    "{galoy-staging}/definitions/staging/stg_analytics_pseudo_users.sqlx":
      file_hash: c76e827cf09bed982e3792b6b7f2929d1c2470d2
      from_commit: d1ca538400a86e4444336c36d14a111877f9b14b
      message: "feat: firebase attribution tracking dimensions (#1236)"
    "{galoy-staging}/definitions/staging/stg_analytics_user_ids.sqlx":
      file_hash: 0bba4ad255ec3db42fb556aa9e656b6ea86a9233
      from_commit: 35984d591c5cc5156505d2f80b4e59486552edd3
      message: "fix: replace JOIN with OR condition with JOIN to redundant table (#973)"
    "{galoy-staging}/definitions/staging/stg_apikeys.sqlx":
      file_hash: 550bd8d714528c32c1d810499b074bb92dfd6d7c
      from_commit: 3e5ecee40fb2365850fec28e6af7542afd9771f6
      message: "fix: ignore users that have not created a key in stg_apikeys"
    "{galoy-staging}/definitions/staging/stg_bankowner_journal_entries.sqlx":
      file_hash: c75c066ea5d80ddfd013fbc4312b1640126257e2
      from_commit: 2ffdd29601fe638a8d147c4e2c949b0d6f51896c
      message: "chore: fix names of static_accounts table"
    "{galoy-staging}/definitions/staging/stg_bitfinex_book.sqlx":
      file_hash: 5a665c4434b63b08bf481033d68b2d13dc14e677
      from_commit: 2ae9dda3472e2e94065777adb44a07b1f473736f
      message: "chore: incrementaly parse responses from bitfinex API"
    "{galoy-staging}/definitions/staging/stg_bitfinex_trades.sqlx":
      file_hash: 95ed4ff4562711129b4b763f6c66fc288b38e27d
      from_commit: 2ae9dda3472e2e94065777adb44a07b1f473736f
      message: "chore: incrementaly parse responses from bitfinex API"
    "{galoy-staging}/definitions/staging/stg_bria_batch_summaries.sqlx":
      file_hash: 9356ef09f1583f17f2af77e951ffda1fc957430b
      from_commit: 73b1e3632f3f09beb5b01ed20df228fc9ba72d30
      message: "feat: onchain fee reconciliation and cpfp from bria in fct_user_wallet_transactions (#1093)"
    "{galoy-staging}/definitions/staging/stg_bria_cpfp.sqlx":
      file_hash: 7c6435fd32a2286be55b803ce4a2f2c931212ccc
      from_commit: 73b1e3632f3f09beb5b01ed20df228fc9ba72d30
      message: "feat: onchain fee reconciliation and cpfp from bria in fct_user_wallet_transactions (#1093)"
    "{galoy-staging}/definitions/staging/stg_bria_fee_reconciliations.sqlx":
      file_hash: a056335100cececdae76b2df8db2de137fffbed3
      from_commit: 07ac7e41a6ae03d302ec73cb0975edcb35b19038
      message: "chore: expose user data for declined onfido applications"
    "{galoy-staging}/definitions/staging/stg_bria_payouts.sqlx":
      file_hash: ab6092d806ef5075ec647661817365017a028a31
      from_commit: 73b1e3632f3f09beb5b01ed20df228fc9ba72d30
      message: "feat: onchain fee reconciliation and cpfp from bria in fct_user_wallet_transactions (#1093)"
    "{galoy-staging}/definitions/staging/stg_bria_transactions.sqlx":
      file_hash: d2b14142ea31d1d4083765e7eb72dd8bb88fe57c
      from_commit: 62fa71385ec6df1629d9e95add7a1a8118093846
      message: "chore: add bria ledger transactions table (#869)"
    "{galoy-staging}/definitions/staging/stg_coincap_prices.sqlx":
      file_hash: 972b4721c9414d671dbabcf392414a859f879435
      from_commit: 9f70bbaca8312aabdf2e7ae657932c04d8b52593
      message: "feat: backfilled minute prices (#1218)"
    "{galoy-staging}/definitions/staging/stg_contacts.sqlx":
      file_hash: bde834a1f490736a0aa8264160b072515131b451
      from_commit: 9807e9f763ac943d768c4131c77a0609d6a323be
      message: "feat: identify accounts that share phone numbers or email addresses (#1150)"
    "{galoy-staging}/definitions/staging/stg_countries.sqlx":
      file_hash: c1db39af5a3719e0b82ebbdff3392ff450c7314b
      from_commit: 5aa78960be21d4baf0967bfe061a5e55bffdf206
      message: "feat: dim_regions (#1175)"
    "{galoy-staging}/definitions/staging/stg_daily_user_balances.sqlx":
      file_hash: 82dd42297cf4dd10ce41d1bf3e539444fe8f1aa9
      from_commit: 87ab08d8de4bb897ae491d778c274a2ee439fbb8
      message: "chore: improved previous transaction fields (#1365)"
    "{galoy-staging}/definitions/staging/stg_daily_user_engagement.sqlx":
      file_hash: 79505181d44d73a2e81360eb1e8d580e4bf1308c
      from_commit: 35984d591c5cc5156505d2f80b4e59486552edd3
      message: "fix: replace JOIN with OR condition with JOIN to redundant table (#973)"
    "{galoy-staging}/definitions/staging/stg_daily_user_engagement_raw.sqlx":
      file_hash: bc8137f590eb21e06ad722382fc8d6908fa26390
      from_commit: f742ad008039aea6828d1f500d40059766436639
      message: "chore: enable incremental updates for stg_daily_user_engagement_raw"
    "{galoy-staging}/definitions/staging/stg_daily_user_funding_revenue.sqlx":
      file_hash: 28ba16e1b9a1bf3f54e0541924dc20f57fac9af7
      from_commit: 47ecbb48c786cb0658f9f4a88f74ff62e1d035d6
      message: "feat: user lifetime revenue dimensions (#1067)"
    "{galoy-staging}/definitions/staging/stg_daily_user_transaction_summaries.sqlx":
      file_hash: 9a0369334242952b603092df1b5de15130bf354d
      from_commit: 1fa5b39b5edca3012e966a5b35ee94f679695b0f
      message: "feat: simple AML \"alerts\" query (#1200)"
    "{galoy-staging}/definitions/staging/stg_days.sqlx":
      file_hash: ef4c3b0dea651f6129b2af6494b2b6db94dd8bed
      from_commit: bc2eff704778b26821faf4eb4dfff78dba0623d2
      message: "chore: add opening swap buy price to fct_stablesats_profit_and_loss (#510)"
    "{galoy-staging}/definitions/staging/stg_dealer_metrics.sqlx":
      file_hash: 24740cdb3765257c411219622fe8c54373aefbee
      from_commit: ff9f04bfedb5f030df83bbc26c7e96c48e73329b
      message: "chore: streamline names joins unions (#1177)"
    "{galoy-staging}/definitions/staging/stg_exchange_balance.sqlx":
      file_hash: c72a35a445f63ad902f0debbb0c6590baa90b1cb
      from_commit: 3245d65aab8bc52d943a8eec808fc5d0f9673c7d
      message: "feat: okx exchange balance (#1141)"
    "{galoy-staging}/definitions/staging/stg_exposure_liability_ratio.sqlx":
      file_hash: 4d51e43cd351a1a34a773b79959523faa19676ef
      from_commit: 18b8ec06895112c54abdfe3e980453e7849ee251
      message: "chore: update exposure liability ratio (#1232)"
    "{galoy-staging}/definitions/staging/stg_funder_journal_entries.sqlx":
      file_hash: ****************************************
      from_commit: ed9347aff49af22a244c0740a70afb46c1ee676f
      message: "chore: flag if a transaction is a quiz reward in fct_user_wallet_transactions (#413)"
    "{galoy-staging}/definitions/staging/stg_funding_yields.sqlx":
      file_hash: cf9a19b0bc99a412a3d66aa0e56bca0620805d53
      from_commit: a7a629bd146dab9e247b67c868b73c92e57e7b19
      message: "chore: simplify query"
    "{galoy-staging}/definitions/staging/stg_galoy_liability.sqlx":
      file_hash: 556b3642aee131be1dafd553f5e621541f50af7e
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_galoy_liability_unified.sqlx":
      file_hash: 7f308d2d90bd3e5f57252cf3e718e61d9b8a09cc
      from_commit: a12e3893b6f0badd84209f7d0ce06a3279056403
      message: "fix: alias (#1228)"
    "{galoy-staging}/definitions/staging/stg_journal_entries.sqlx":
      file_hash: fa6a6d1c4679ee990a584bcb5fa82277210674ff
      from_commit: bb61524dfe5ebd066ec4816ef1386e9d8df70a45
      message: "chore: add wallet transaction ordering to stg_journal_entries (#1193)"
    "{galoy-staging}/definitions/staging/stg_journal_entries_dealer_wallet_btc_outflow.sqlx":
      file_hash: 310eac668a991c67f86e2c0bc1853bf8582ddd5f
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_journal_entries_dealer_wallet_btc_outflow_unified.sqlx":
      file_hash: 3e518cae7013523702757da27deeeda8514399c5
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_journal_entries_raw.sqlx":
      file_hash: 99a21eb430fc435c5de1c372ee329bc9cb414c9a
      from_commit: 6788c3e8afbe391807360ff1bdbda0b29947ee07
      message: "chore: cleanup stg_journal_entries (#1072)"
    "{galoy-staging}/definitions/staging/stg_journal_entry_metadata.sqlx":
      file_hash: 4916ed14335df46ce5568e7f9eda02526db92c1e
      from_commit: a50e740fe92b6b8cee8efd0319da99b030254b41
      message: "chore: add table for transaction metadatas (#317)"
    "{galoy-staging}/definitions/staging/stg_kratos_identities.sqlx":
      file_hash: 09eb4cdeadb43ae4252266eeac0a288887365b2f
      from_commit: 9807e9f763ac943d768c4131c77a0609d6a323be
      message: "feat: identify accounts that share phone numbers or email addresses (#1150)"
    "{galoy-staging}/definitions/staging/stg_kratos_identity_credentials.sqlx":
      file_hash: 353a63631b1b81d2b40c156317965996ae4704b0
      from_commit: c6f8d3cc138c63eadb8395011ff2d5dbe591b31b
      message: "chore: add dimension for totp two factor (#1336)"
    "{galoy-staging}/definitions/staging/stg_liability_reconciliation.sqlx":
      file_hash: 4298706b1cd4b7476a6f23605bb03249d788b753
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_lightning_fee_reimbursements.sqlx":
      file_hash: e33e5b76604905f5fbbc9571787d84c116f9bb33
      from_commit: 73b1e3632f3f09beb5b01ed20df228fc9ba72d30
      message: "feat: onchain fee reconciliation and cpfp from bria in fct_user_wallet_transactions (#1093)"
    "{galoy-staging}/definitions/staging/stg_lightning_payments.sqlx":
      file_hash: 7584f4594da604d8cf0ae800d384e89fca638a17
      from_commit: 93b0611725f24ca495329f6070dd109bacb12754
      message: "chore: fix non-null payment status exception"
    "{galoy-staging}/definitions/staging/stg_minute_swap_prices.sqlx":
      file_hash: 80153b71cf99da1cd98d56e6c6d28df84adb0fe5
      from_commit: ccfe92bd69879b8ed3c86f8b877e4b9592233e94
      message: "fix: forgot to add stg_minute_swap_prices"
    "{galoy-staging}/definitions/staging/stg_minute_sync_spot_swap_prices.sqlx":
      file_hash: e72c5d6b7f1ca0e77cb35fb3065cfb496be6011c
      from_commit: d151c24bedcba906a2ccfa1d47453cb342a2387e
      message: "fix: generate array overflow (#1471)"
    "{galoy-staging}/definitions/staging/stg_minute_sync_spot_swap_prices_backfilled.sqlx":
      file_hash: 34d506022fa60cad9ba9c8a3430620463cee7d21
      from_commit: 7c957054ef3c7ceb68bd10a80723ca97d1b095cc
      message: "chore: remove minute price null-prices assertions (#1220)"
    "{galoy-staging}/definitions/staging/stg_ml_user_clustering_model.sqlx":
      file_hash: fa537085284be9adba6dacacd386a26435b498b8
      from_commit: a5e8c1c1ae2e426ebd52536bf836550139516d85
      message: "chore: replace dataform `resolve` with explicit `schema` in stg_ml_user_clustering_model (#1143)"
    "{galoy-staging}/definitions/staging/stg_ml_user_clustering_predictions.sqlx":
      file_hash: cb21eb5e9a5557a7b246af0ca34f3c7ae08dd6ff
      from_commit: d0d2661c8415832946fbca4151a5e5b3f718d9e5
      message: "chore: update cluster labels and optimal number after adding new features (#602)"
    "{galoy-staging}/definitions/staging/stg_ml_user_transaction_summaries.sqlx":
      file_hash: d4c84a4ceeaff4e8dd6798469385cd1f98baf29b
      from_commit: d0d2661c8415832946fbca4151a5e5b3f718d9e5
      message: "chore: update cluster labels and optimal number after adding new features (#602)"
    "{galoy-staging}/definitions/staging/stg_null_country_names.sqlx":
      file_hash: fc15572e1814cb473d034bdde79c2cbc1fbefc00
      from_commit: 3b65cfaae79d71f42941ffb23aeffb63a7018c19
      message: "fix: null country for some users (#558)"
    "{galoy-staging}/definitions/staging/stg_okex_balance_position_liability_filled.sqlx":
      file_hash: e1475227622d004f0330d4010e94770ea4112750
      from_commit: 4554cac016c12f19794d17fa2f855f502221e216
      message: "feat: dealer wallet btc outflow (#643)"
    "{galoy-staging}/definitions/staging/stg_okex_balance_position_liability_sync.sqlx":
      file_hash: 4e04566df2e249921cf3c4cf3d716e8f753ec5b5
      from_commit: d151c24bedcba906a2ccfa1d47453cb342a2387e
      message: "fix: generate array overflow (#1471)"
    "{galoy-staging}/definitions/staging/stg_okex_bid_ask.sqlx":
      file_hash: 08c11ed07ee3cb7059dfb255db4b285c593d5609
      from_commit: 9f70bbaca8312aabdf2e7ae657932c04d8b52593
      message: "feat: backfilled minute prices (#1218)"
    "{galoy-staging}/definitions/staging/stg_okex_bills.sqlx":
      file_hash: 6837ed118ee8659d3fad2e6bd0482b7fd8495895
      from_commit: ccba6724fe0a0678fb12192d08b6ac1cc9ccd92c
      message: "fix: kludge to fix okex api bug returning inconsistent billId's"
    "{galoy-staging}/definitions/staging/stg_okex_bills_archive.sqlx":
      file_hash: c678a5941b889ac2f21dbae4205f23ced31ed0a8
      from_commit: 8dc671f68e3e6891cd37a502eacde889d847a79d
      message: "feat: okx tx history rec (#1466)"
    "{galoy-staging}/definitions/staging/stg_okex_bills_corrected.sqlx":
      file_hash: 0d1aa196aa9a039968c73914798916546e082c16
      from_commit: e98b5be7d905af7979538329936b8876be01677a
      message: "chore: backfill new okx trading account history (#1347)"
    "{galoy-staging}/definitions/staging/stg_okex_external_transfers.sqlx":
      file_hash: f4d01ebeacfae5d44649081cbb0999478e2604cb
      from_commit: d151c24bedcba906a2ccfa1d47453cb342a2387e
      message: "fix: generate array overflow (#1471)"
    "{galoy-staging}/definitions/staging/stg_okex_funding_fees.sqlx":
      file_hash: 2621f34a87b3c842e5ca9557d32ee0bb11b94c4c
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_okex_internal_transfers.sqlx":
      file_hash: b88b7e30c510fe4dd933e8c7ded14581335375a5
      from_commit: d151c24bedcba906a2ccfa1d47453cb342a2387e
      message: "fix: generate array overflow (#1471)"
    "{galoy-staging}/definitions/staging/stg_okex_orders.sqlx":
      file_hash: 5dac27d5dc4ea6ed7a7b458227080205d2f172dc
      from_commit: e13439047d0855264f995b9d6150ee2bd2e86246
      message: "feat: pnl explain (#890)"
    "{galoy-staging}/definitions/staging/stg_okex_reported_orders.sqlx":
      file_hash: f96a08540225419fa33d0fdc891529e33150649a
      from_commit: f58ba45a609897af892ff13c69ea7d89dac2d01c
      message: "chore: okex reported orders (#1230)"
    "{galoy-staging}/definitions/staging/stg_okex_reported_transfers.sqlx":
      file_hash: 34d822913c146aa617343666a7f3800b73786824
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_okex_trades.sqlx":
      file_hash: 2c17486b2336793203fc4c9fec6563fb97b50f09
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_okex_transfers.sqlx":
      file_hash: efc695881df3ef4e1cf06f01cdf550f6b47ae44d
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_okx_exposure.sqlx":
      file_hash: f449f13ad1d823ec7f54c992c795f11434ce4ced
      from_commit: 19a7c7da7834ef4c3caa5b8d3a6a0e92895db465
      message: "feat: get exposure timeseries from okx bill history (#992)"
    "{galoy-staging}/definitions/staging/stg_okx_exposure_reconciliation.sqlx":
      file_hash: dd762bd7c9b252708424e755086d0648773246a6
      from_commit: 380c9414406ee87a65d4e41e63d8bad040dca567
      message: "feat: corrected and enhanced okex bills (#997)"
    "{galoy-staging}/definitions/staging/stg_okx_funding_account_history.sqlx":
      file_hash: 5513c6c00632bf0dc7661d4b8ad5acfcabf8cc79
      from_commit: 76c25fa3d35739e69cb37319ba21b24cbb2c25fd
      message: "feat: okx funding account history staging view (#1117)"
    "{galoy-staging}/definitions/staging/stg_okx_trading_account_history.sqlx":
      file_hash: f1f725d9401040a8d7b5f16d6ea2d440d8f8ec89
      from_commit: 1b69ba01b7329b0f78126853413cc53b9b053e84
      message: "feat: backfill okx trading account history (#982)"
    "{galoy-staging}/definitions/staging/stg_onchain_bank_fees.sqlx":
      file_hash: d69eb86368d19dccd019ef80e382210cdcf2552f
      from_commit: 73b1e3632f3f09beb5b01ed20df228fc9ba72d30
      message: "feat: onchain fee reconciliation and cpfp from bria in fct_user_wallet_transactions (#1093)"
    "{galoy-staging}/definitions/staging/stg_payment_request_details.sqlx":
      file_hash: 7a7efeb85860fb03d93997904faa9736719b23a3
      from_commit: 7273de3161b2b629399fe3d4bdd93abc117c0a4d
      message: "chore: restore to incremental after refresh"
    "{galoy-staging}/definitions/staging/stg_pg_stablesats_public_galoy_transactions.sqlx":
      file_hash: bc0ee50c34ac15c53b8f51670b0734b63010277a
      from_commit: 8306d33f1963fc222c7ce9c7922bda600f2fb42d
      message: "fix: kafka real-time postgres tables read (#845)"
    "{galoy-staging}/definitions/staging/stg_pg_stablesats_public_okex_orders.sqlx":
      file_hash: f6404826e8a136d73d23c65a9da6d14d781b7c0b
      from_commit: 8306d33f1963fc222c7ce9c7922bda600f2fb42d
      message: "fix: kafka real-time postgres tables read (#845)"
    "{galoy-staging}/definitions/staging/stg_pg_stablesats_public_okex_transfers.sqlx":
      file_hash: 2fd5e658e6445e1542b03c6aa859cae377ac27f6
      from_commit: 8306d33f1963fc222c7ce9c7922bda600f2fb42d
      message: "fix: kafka real-time postgres tables read (#845)"
    "{galoy-staging}/definitions/staging/stg_pg_stablesats_public_sqlx_ledger_balances.sqlx":
      file_hash: 4ce5aaa4da6a57c3610949d3f576d328552589cb
      from_commit: 8435fead9692e6732f510f6400bffb2222a4a30c
      message: "fix: real-time pg sqlx ledger balances table read (#832)"
    "{galoy-staging}/definitions/staging/stg_pg_stablesats_public_user_trades.sqlx":
      file_hash: 4401416d20809a18135ae0ae21c33d17209274c5
      from_commit: 8306d33f1963fc222c7ce9c7922bda600f2fb42d
      message: "fix: kafka real-time postgres tables read (#845)"
    "{galoy-staging}/definitions/staging/stg_phone_number_prefix_neighbors__.sqlx":
      file_hash: 5cb23ea7b3843495d0e59484963e6870aaa3bbab
      from_commit: 716c4c8f80481cb29f114efc5b46ac985c55edb8
      message: "chore: try an even larger cross join (#489)"
    "{galoy-staging}/definitions/staging/stg_pnl_explain.sqlx":
      file_hash: 2fe9b3999835ca1ed47c967e3612e93243a7d43a
      from_commit: 18b8ec06895112c54abdfe3e980453e7849ee251
      message: "chore: update exposure liability ratio (#1232)"
    "{galoy-staging}/definitions/staging/stg_pseudo_user_attribution.sqlx":
      file_hash: c6355038083cebeb734cae3bb633138933c29a67
      from_commit: f1461f7e263d6a39d1fda389ff7dc3beb18a6c2c
      message: "chore: restore incremental updates for stg_pseudo_user_attribution"
    "{galoy-staging}/definitions/staging/stg_public_lightning_nodes.sqlx":
      file_hash: d575558606efb08504d944ec5c14394d5aed5cb0
      from_commit: 64a8d894a3ae3a28ed18963965bce02cb354ab75
      message: "chore: cleanup remaining issues with the pseudo_destination_alias"
    "{galoy-staging}/definitions/staging/stg_rt_galoy_liability.sqlx":
      file_hash: 16bf3dbf85c42dc1bad396a276df971d3a20849a
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_rt_journal_entries.sqlx":
      file_hash: 0fdb3db7c4370083039a213f30d176d230bb0310
      from_commit: 9844447c53a08247b71f3acba303571a9ad44ff8
      message: "revert: fix: set materialized: false (#1468) (#1469)"
    "{galoy-staging}/definitions/staging/stg_rt_journal_entries_dealer_wallet_btc_outflow.sqlx":
      file_hash: c48b5083a450e40e796a6d3b4d6938fe1eddb4fc
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_dealer_wallet_btc_outflow.sqlx":
      file_hash: 00f6303d7dc944115428834594c3559d11250747
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_galoy_transactions.sqlx":
      file_hash: b8b4a2a748fe67711a60c3f34f731bea4b1cdaf7
      from_commit: 5b5401babda5dae2e6a6c89cbba0ff260b6cdc4b
      message: "fix: ignore realtime null memo transactions (#815)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_liability.sqlx":
      file_hash: b14a26e2e3f6fccba702d6509d5982dea2ef209b
      from_commit: 0aa6fea2065341aa7196ab8bf40f92b8c9a2144f
      message: "fix: pg realtime duplicates (#684)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_okex_orders.sqlx":
      file_hash: 812622fbb6e1308abd4cdad49dad02afd8682ee0
      from_commit: 0aa6fea2065341aa7196ab8bf40f92b8c9a2144f
      message: "fix: pg realtime duplicates (#684)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_okex_transfers.sqlx":
      file_hash: 2a73d9d0245fad839cd42e242307e83915c2795b
      from_commit: 0aa6fea2065341aa7196ab8bf40f92b8c9a2144f
      message: "fix: pg realtime duplicates (#684)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_sqlx_ledger_balances.sqlx":
      file_hash: 04a56a2b9e0548642dc99580cb5c88d94506a59b
      from_commit: 0aa6fea2065341aa7196ab8bf40f92b8c9a2144f
      message: "fix: pg realtime duplicates (#684)"
    "{galoy-staging}/definitions/staging/stg_rt_stablesats_internal_user_trades.sqlx":
      file_hash: 97c74fec052490faef6d07780a3d0c88046437b1
      from_commit: 0aa6fea2065341aa7196ab8bf40f92b8c9a2144f
      message: "fix: pg realtime duplicates (#684)"
    "{galoy-staging}/definitions/staging/stg_rt_trades.sqlx":
      file_hash: b681942cc84f899679249cb71383293daef75ec6
      from_commit: 4c78db75d4f8c1d5f3bb5485c0143966a3d6052d
      message: "chore: update stg_rt_journal_entries to follow upgrades to stg_journal_entries (#1131)"
    "{galoy-staging}/definitions/staging/stg_rt_wallets.sqlx":
      file_hash: 8aeac3a7e08e4751ff0ac104727e9bb1c67f8463
      from_commit: e14bfb409720d58fde015f092ff63aaefcadf738
      message: "chore: add view of kafka wallets table"
    "{galoy-staging}/definitions/staging/stg_screens_categories.sqlx":
      file_hash: de7295ccbabc25dcd3bb401a695adc0f12a230c8
      from_commit: ee8e1f7adb2a8d9b79c1a39ba0438b76c6ca7d6b
      message: "chore: add category for sendBitcoinCompleted (#1157)"
    "{galoy-staging}/definitions/staging/stg_shared_device_tokens.sqlx":
      file_hash: a981c3f3e561d7bb9295d5f58d48e40f28e0e42f
      from_commit: 1f3847c4e0b1a33fbbe950ac524813a8eef241b9
      message: "chore: add shares_device_token_with to dim_users (#877)"
    "{galoy-staging}/definitions/staging/stg_simple_wallet_transfers.sqlx":
      file_hash: a56713259b94f1e870f456ab068ee0d7ddfb4b65
      from_commit: c72ef96fad2be93b78ec82d43e476e18793ed0ed
      message: "chore: transition tables to views"
    "{galoy-staging}/definitions/staging/stg_stablesats_exchange_avg_open_prices.sqlx":
      file_hash: f64db796f7fc697f420e623dedc2be8891a34792
      from_commit: 79584bb314da6180f1f073251c08d8ef9103f2b9
      message: "feat: table to diagnose phone number prefix clusters (#431)"
    "{galoy-staging}/definitions/staging/stg_stablesats_exchange_funding_fees.sqlx":
      file_hash: 522d5a71ff6b145f86e3dbda3d656328eae275e9
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_stablesats_exchange_trades.sqlx":
      file_hash: 0ad4a4f5317c2b8785cb478ce0e110c440f1bdb0
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_stablesats_historical_fee_schedule.sqlx":
      file_hash: cd05371c442568038789903fff81a975ce3ad4c3
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_dealer_wallet_btc_outflow.sqlx":
      file_hash: b6634d8675b0ffb0968dd8fa35b28e9ab7d053d2
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_dealer_wallet_btc_outflow_unified.sqlx":
      file_hash: f113d85555e35e1c3017d85a3452e8609ba46206
      from_commit: 20f32beb7edd38ab97286880754bd612d83d72a4
      message: "fix: liability and outflow realtime stiching (#1227)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_galoy_transactions.sqlx":
      file_hash: 87f1be5dee3ff952cf09683171ba1d72fb523468
      from_commit: c5bd676c9bb88dcf348894e2b494e498763b4a98
      message: "feat: add stablesats galoy_transactions"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_liability.sqlx":
      file_hash: 9c8a110f62e95dc1b9c5f36fb28bc7446f2b5d2a
      from_commit: a4635d304a22347277f119ff9c207761b866ec6d
      message: "chore: transition real time tables to views (and add authorization for stablesats dataset)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_liability_unified.sqlx":
      file_hash: 1f4e4106d3518b57d5a905920ff68eca80dd2989
      from_commit: 433abdc74b046774a0d0b23605f541c5b42d768c
      message: "chore: pause wasteful stablesats rt liability query (#888)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_okex_bria_transfers.sqlx":
      file_hash: 721d54e9895bb1fc57c38058553af31a4d7e0cc0
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_okex_orders.sqlx":
      file_hash: 738041912016e40a3974bd3a4d0e969f436e7014
      from_commit: abcbafee451596f289cefada53b5fe02c6ee01ec
      message: "chore: mirror stablesats tables in dataform"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_okex_orders_unified.sqlx":
      file_hash: f1666c54d573f3c60cdb51aa51619abdef26cd14
      from_commit: 7270e12a302d866090dc7d66bc5322ad8cebe2a7
      message: "feat: stablesats batch realtime unified (#642)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_okex_transfers.sqlx":
      file_hash: 471020759e0c72c14a62ec0a12e040abba9ae3ba
      from_commit: a4635d304a22347277f119ff9c207761b866ec6d
      message: "chore: transition real time tables to views (and add authorization for stablesats dataset)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_okex_transfers_unified.sqlx":
      file_hash: 8418dc6ea755153411f87c9a9049286feafdad07
      from_commit: 7270e12a302d866090dc7d66bc5322ad8cebe2a7
      message: "feat: stablesats batch realtime unified (#642)"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_sqlx_ledger_balances.sqlx":
      file_hash: 12e705b0fe6a2697d53c4e22a30b5716b7f7b055
      from_commit: 489477720a091a92d7b9d38942b9162a22a7d973
      message: "feat: add stablesats ledger_balances"
    "{galoy-staging}/definitions/staging/stg_stablesats_internal_user_trades.sqlx":
      file_hash: a00dce8503d2bc20b1e8273ca26bb6033b54ea2c
      from_commit: abcbafee451596f289cefada53b5fe02c6ee01ec
      message: "chore: mirror stablesats tables in dataform"
    "{galoy-staging}/definitions/staging/stg_stablesats_mishedge.sqlx":
      file_hash: 8ac73ba1523ebabdc7b12bef33c2d21c268aab46
      from_commit: bcf79049ba47ceef64391a0e86a6fbdd15c115ba
      message: "fix: filter for Buy or Sell orders in stg_stablesats_exchange_trades"
    "{galoy-staging}/definitions/staging/stg_stablesats_mishedge_avg_open_prices.sqlx":
      file_hash: ed041b739da50249e27d073ac00ec64c4fa9ec8e
      from_commit: 79584bb314da6180f1f073251c08d8ef9103f2b9
      message: "feat: table to diagnose phone number prefix clusters (#431)"
    "{galoy-staging}/definitions/staging/stg_stablesats_mishedge_pnl.sqlx":
      file_hash: 16127f9420b16ee8d28855e1f8e070c48cef80de
      from_commit: cfe9b1330506cd1f85d8b4d212c417897ad337da
      message: "feat: mishedge profit and loss table"
    "{galoy-staging}/definitions/staging/stg_static_accounts.sqlx":
      file_hash: 718a1f760e6f7a4375f0d7666be0a12a27ca5643
      from_commit: 181f4c34f35229f0fd265d18d9b3dc2c4eaaaaec
      message: "chore: fix legacy \"static\" tables (#523)"
    "{galoy-staging}/definitions/staging/stg_static_values.sqlx":
      file_hash: d1450cb2360fcae7a79b7738b9eb0cc6dd2378ff
      from_commit: ****************************************
      message: "fix: use DDL for static table"
    "{galoy-staging}/definitions/staging/stg_static_wallets.sqlx":
      file_hash: 1e81a455967b376c038b4dc305c571a69891442d
      from_commit: aef65ccca33c96df585babd365f91afc448b467e
      message: "chore: assert that wallet_ids and account_ids are unique (#922)"
    "{galoy-staging}/definitions/staging/stg_trade_spreads.sqlx":
      file_hash: 49cbd0fcc94d396c5a30a23386767a5df39d12ed
      from_commit: 79584bb314da6180f1f073251c08d8ef9103f2b9
      message: "feat: table to diagnose phone number prefix clusters (#431)"
    "{galoy-staging}/definitions/staging/stg_trades.sqlx":
      file_hash: a4f6325c2ab01f7d5d61cdb096826fb5222deec3
      from_commit: 6788c3e8afbe391807360ff1bdbda0b29947ee07
      message: "chore: cleanup stg_journal_entries (#1072)"
    "{galoy-staging}/definitions/staging/stg_transaction_spot_prices.sqlx":
      file_hash: 6d2af0b38bdae038e170bc4643f1e7f4b56b73d7
      from_commit: 2637f92d49b0e093ffa55a66797f4d8bc80d66eb
      message: "fix: handle transactions that happen before the first minute price (#1238)"
    "{galoy-staging}/definitions/staging/stg_unrealized_pnl_explain.sqlx":
      file_hash: 0da82d07f3d51d310efd88c426f568794af60370
      from_commit: 6b64895f8d434601c0769b6056a156b1432bac3b
      message: "feat: stablesats report (#1235)"
    "{galoy-staging}/definitions/staging/stg_usd_wallet_states.sqlx":
      file_hash: b31576dd7cde5481c67f2a9155aa24b1b0c42c65
      from_commit: 2c9d0c733e0e87563e4487a2717cce86c1a2b114
      message: "feat: simple tests for user wallet average open prices query (#934)"
    "{galoy-staging}/definitions/staging/stg_user_accounts.sqlx":
      file_hash: 95163b87896fd6d284e3fa25649fc71eb4167f24
      from_commit: 6d2211bc2424b9dff15ff5f56e13032a2aa7de91
      message: "chore: remove ordering in queries that generate disposable keys for internal ids (#1173)"
    "{galoy-staging}/definitions/staging/stg_user_app_info.sqlx":
      file_hash: e83e01b3959c69bcfe18218b96d181449a297a5c
      from_commit: 35984d591c5cc5156505d2f80b4e59486552edd3
      message: "fix: replace JOIN with OR condition with JOIN to redundant table (#973)"
    "{galoy-staging}/definitions/staging/stg_user_app_info_raw.sqlx":
      file_hash: d5940829ea4716e25cca99aa4d330ab83bdab0c9
      from_commit: fb1c9fabc414df14cc7adb51a8b0f07e97680ff9
      message: "chore: do not track daily updates for google analytics app info (#767)"
    "{galoy-staging}/definitions/staging/stg_user_app_removes_raw.sqlx":
      file_hash: f2ab6ce2329a3db95baa0d11b32ad1e159e4143a
      from_commit: 97d5487a1020c1d8c3b75b313addcf45d50736f0
      message: "feat: user app_remove events from google analytics model (#782)"
    "{galoy-staging}/definitions/staging/stg_user_attribution.sqlx":
      file_hash: 5852ec8785bf123d99a69ae8ca7ad1f7759ec320
      from_commit: d1ca538400a86e4444336c36d14a111877f9b14b
      message: "feat: firebase attribution tracking dimensions (#1236)"
    "{galoy-staging}/definitions/staging/stg_user_days.sqlx":
      file_hash: db22c766f64541480c6fc5743a5bbd15a32c0751
      from_commit: 5a8165140fbd6f3c604bf9f6d7847e5f9355a146
      message: "chore: remove buggy incremental logic from stg_user_days"
    "{galoy-staging}/definitions/staging/stg_user_email_updates.sqlx":
      file_hash: 1d64b642d3c7205c35597b50910b4be31b5f6c4a
      from_commit: 93278c1651567939089685d0523e339f8573a056
      message: "feat: keep track of users adding their emails for authentication (#1389)"
    "{galoy-staging}/definitions/staging/stg_user_onboarding.sqlx":
      file_hash: deabaaaa3b9316e6def9843066cb25936dd77d5e
      from_commit: ac52eaac2a812726dbef29b2e8ca45658744e277
      message: "chore: additional fields to play with for circles (#747)"
    "{galoy-staging}/definitions/staging/stg_user_pnl.sqlx":
      file_hash: 9c719758a38fb947be48db7596fbcfe0c7ee60a3
      from_commit: 1b625b0037ea255b4ca165437a7b01649e78502f
      message: "fix: wrong sign on user pnl"
    "{galoy-staging}/definitions/staging/stg_user_screen_views.sqlx":
      file_hash: 173cbebc6a0dfc37f3aacd8abc8db158b79e5f1b
      from_commit: e372093f83adb0605471be80cea02d81321219d2
      message: "feat: screen views fact table (#867)"
    "{galoy-staging}/definitions/staging/stg_user_sessions.sqlx":
      file_hash: b63d623311df369466f3d0d68dd26db5c15ae308
      from_commit: d5c8538972b541f1caf543dcdadc84b8daff04ba
      message: "chore: stop rebuilding stg_user_sessions from scratch (#1196)"
    "{galoy-staging}/definitions/staging/stg_user_transaction_summaries.sqlx":
      file_hash: 0b657eb2a58860f4561924c7242172971e49ebc8
      from_commit: a806280f95572f987648efedc5c6b01587598e30
      message: "fix: typos in user_direction: Intra-user transfer (#1441)"
    "{galoy-staging}/definitions/staging/stg_user_wallet_transactions.sqlx":
      file_hash: 1c2bf5a0b19e1be77640c34d8947ac6e54b97398
      from_commit: 865051a71f7e61806c923d17d669a8dd87b19971
      message: "fix: canonical ordering for user transactions (#1205)"
    "{galoy-staging}/definitions/staging/stg_user_wallets.sqlx":
      file_hash: c0903e39f74cae6d8ff54854701152ba85d9afdc
      from_commit: 6d2211bc2424b9dff15ff5f56e13032a2aa7de91
      message: "chore: remove ordering in queries that generate disposable keys for internal ids (#1173)"
    "{galoy-staging}/definitions/staging/stg_users_raw.sqlx":
      file_hash: 5234bbd2b14044a00875d8bb66220306b506a321
      from_commit: 3f488a42d748c02dbe7e236ab0848a64ad01dac3
      message: "feat: improved phone number country code determination (#1254)"
    "{galoy-staging}/definitions/staging/stg_wallet_avg_open_prices.sqlx":
      file_hash: 67e5faff434233ed071cc4e71173f8e9cbc5c389
      from_commit: b88ab136da2bb4f6b659751b2a240d54e9177c57
      message: "chore: restore incremental updates for stg_wallet_avg_open_prices"
    "{galoy-staging}/definitions/staging/stg_wallet_states.sqlx":
      file_hash: 94d289c5a9be39a248ccc2216ba3ef43012ac990
      from_commit: 8df06f4a959873576a8106b2e1ae265629f712d0
      message: "fix: missing average open prices (#1192)"
    "{galoy-staging}/definitions/staging/stg_wallets.sqlx":
      file_hash: cf3671ff37be23a59337066e0d565c8aa9eae889
      from_commit: aef65ccca33c96df585babd365f91afc448b467e
      message: "chore: assert that wallet_ids and account_ids are unique (#922)"
    "{galoy-staging}/definitions/staging/stg_weekly_user_transaction_summaries.sqlx":
      file_hash: 6cc17d63e52149c79593d91cdadea0ab90217f83
      from_commit: 1fa5b39b5edca3012e966a5b35ee94f679695b0f
      message: "feat: simple AML \"alerts\" query (#1200)"
    "{galoy-staging}/definitions/tests/user_pnl/test_usd_wallet_states.sqlx":
      file_hash: 3cf0974ebca4808199d0b94342d610772d9bd1a2
      from_commit: 81f7a5ef1e3679d1452cb8539bde5f0473ca55b0
      message: "feat: test that user pnl calculation works with recorded_at ordering (#1203)"
    "{galoy-staging}/definitions/tests/user_pnl/test_user_pnl.sqlx":
      file_hash: c059e592aefd86d8533c866701d4bf21693c72b6
      from_commit: dc42001ff59772ec9881c89242d0c2c2e49c1966
      message: "feat: add wallet_avg_open_price test for USD to USD wallet transfers (#1190)"
    "{galoy-staging}/definitions/tests/user_pnl/test_wallet_avg_open_prices.sqlx":
      file_hash: 79352ea0debfc49ea8533b98d4efa52fdddd718d
      from_commit: 5fe721e9fbbd9880fd2c1a44428ef55e7ddc9fc2
      message: "chore: additional tweaks related to fix for user pnl query (#1209)"
    "{galoy-staging}/definitions/tests/user_pnl/test_wallet_avg_open_prices_assertion.sqlx":
      file_hash: f00aaf749652819d08094ba04f5feab457f9f36b
      from_commit: 81f7a5ef1e3679d1452cb8539bde5f0473ca55b0
      message: "feat: test that user pnl calculation works with recorded_at ordering (#1203)"
    "{galoy-staging}/definitions/tvfs/tvf_user_metadata.sqlx":
      file_hash: 90b3b9fd5a3fd39d152c5d34b5c02cfaeeff2d18
      from_commit: b93a816e4df3bf2b2d4ebce0b474b7d2fbb486b3
      message: "chore: use account_id instead of account_oid for accountips"
    "{galoy-staging}/definitions/tvfs/tvf_user_transaction_summaries.sqlx":
      file_hash: 95c8afe1adc2748c094355062366938da7ded1cb
      from_commit: 6a11404512b8faa3e1b4634837e5780843f9e40d
      message: "chore: report for an information request from the attorney general's office (#804)"
    "{galoy-staging}/definitions/udfs/udf_asof_join.sqlx":
      file_hash: c1e3dfc39d9fb2333035fedab98f5bb9d56c4df0
      from_commit: 2637f92d49b0e093ffa55a66797f4d8bc80d66eb
      message: "fix: handle transactions that happen before the first minute price (#1238)"
    "{galoy-staging}/definitions/udfs/udf_avg_open_price.sqlx":
      file_hash: bea6c71195d2559559f1638629507c9210d3aad5
      from_commit: 79584bb314da6180f1f073251c08d8ef9103f2b9
      message: "feat: table to diagnose phone number prefix clusters (#431)"
    "{galoy-staging}/definitions/udfs/udf_funding_yield.sqlx":
      file_hash: bea9eb3b7e01bb5525243cfde9c2af3e5aae1501
      from_commit: 676b33aaaabb4400be33dc3e11730fc427b02251
      message: "chore: optimize a calc in the udf process"
    "{galoy-staging}/definitions/udfs/udf_fuzzy_match_token_sort_ratio.sqlx":
      file_hash: 3f50e9303e810add7dba0c51d6d17c100dd3aefc
      from_commit: f6718777be391b8eee43d0d069e816bd154465a9
      message: "feat: fuzzy match udf dataform watchlist comparison (#1338)"
    "{galoy-staging}/definitions/udfs/udf_kafka_decimal_to_bigquery_decimal.sqlx":
      file_hash: 4e65d5ec020c0814f322868cb09fa47dbc8c8666
      from_commit: 8a35d5ef5391b66d49f3189ed3d95cdd76095074
      message: "feat: function to convert decimal types imported through kafka to bigquery DECIMAL (#660)"
    "{galoy-staging}/definitions/udfs/udf_libphonenumber.sqlx":
      file_hash: 72fd279ee5515961cfa0b18f966fe0e9c8fa0a2e
      from_commit: 3f488a42d748c02dbe7e236ab0848a64ad01dac3
      message: "feat: improved phone number country code determination (#1254)"
    "{galoy-staging}/definitions/udfs/udf_longest_common_prefix.sqlx":
      file_hash: 37f89cbc18fafbcba5c57e640d5281ecadcf72e3
      from_commit: 79584bb314da6180f1f073251c08d8ef9103f2b9
      message: "feat: table to diagnose phone number prefix clusters (#431)"
    "{galoy-staging}/definitions/udfs/udf_parse_payment_request.sqlx":
      file_hash: 856ea9b5f3b398f88958f96fff7c16f3b08f3998
      from_commit: 7215fea4476cfacd36eebbe6f8fcf8bd14f7f343
      message: "fix: js regex syntax change on bigquery (#761)"
    "{galoy-staging}/includes/constants.js":
      file_hash: dbaf12d39ae1fe8323870261c536c17e6147baea
      from_commit: 1fa5b39b5edca3012e966a5b35ee94f679695b0f
      message: "feat: simple AML \"alerts\" query (#1200)"
    "{galoy-staging}/includes/envs.js":
      file_hash: eeab87785488d54947b72231d6c439651abc112c
      from_commit: 2836e1d33c06c49b6571d55aa05ab2c6b405a926
      message: "feat: environment aware views of google analytics events (#330)"
    "{galoy-staging}/includes/queries.js":
      file_hash: 8328c2c437ceaced6a2cab2c80591392b666feb2
      from_commit: a806280f95572f987648efedc5c6b01587598e30
      message: "fix: typos in user_direction: Intra-user transfer (#1441)"
    "{galoy-staging}/package-lock.json":
      file_hash: 766ce5bd1072a36754d8f9e236a648fa1c8277ed
      from_commit: a97cb56174e6741a900ad397ad968105c012761b
      message: "fix: update package-lock.json"
    "{galoy-staging}/package.json":
      file_hash: adae3fd025d1b6d0bd8e489826a53aef0b9abd50
      from_commit: a97cb56174e6741a900ad397ad968105c012761b
      message: "fix: update package-lock.json"
propagated_from: galoy-staging

