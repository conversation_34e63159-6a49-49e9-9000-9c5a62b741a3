---
version: 71
current:
  head_commit: 0b875d7a953947bc9e808cd14d6391ed7a45a860
  propagated_head: 4c005f9cb3c4dca82f592e76e4c0a32ab9d6636c
  files:
    "{galoy-staging}/functions/bitfinex-book/Makefile":
      file_hash: bff25dffe1fb50551d7cf50ed0effd30637be3d9
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-book/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-book/main.py":
      file_hash: 69c80b45ce7b6aef083cef3ca1051b99203b3b67
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-book/requirements.txt":
      file_hash: 4006aa530c2e3eb8af4c91ab2721caebaf6703dd
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-trades/Makefile":
      file_hash: 207578df83299f8733cd01e032fd1420e8d15457
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-trades/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-trades/main.py":
      file_hash: 34d497a714e4a29da5119b0fdd439a7158294336
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/bitfinex-trades/requirements.txt":
      file_hash: 4006aa530c2e3eb8af4c91ab2721caebaf6703dd
      from_commit: cff0e1c058d59e53642d5b4e0f5f925de3e9d509
      message: "feat: cloud functions to poll Bitfinex API for order book and recent trades (#1324)"
    "{galoy-staging}/functions/coincap-price/Makefile":
      file_hash: 5e602036c968d65ff3dad75b3a703a4c47a6cb07
      from_commit: cd04a6695aed090c600446dc9f6b8f9254151cf0
      message: "feat: minute spot price cloud function"
    "{galoy-staging}/functions/coincap-price/bin/call-function.sh":
      file_hash: e058c7d0ace3039b5ad96b690857b157b60e898e
      from_commit: cd04a6695aed090c600446dc9f6b8f9254151cf0
      message: "feat: minute spot price cloud function"
    "{galoy-staging}/functions/coincap-price/index.js":
      file_hash: 94888f72191013b1ff0566337b2a5e47ab30a31b
      from_commit: cd04a6695aed090c600446dc9f6b8f9254151cf0
      message: "feat: minute spot price cloud function"
    "{galoy-staging}/functions/coincap-price/package.json":
      file_hash: 3b77a78c7e3b5d16d91dd3311992ee4e54cd1078
      from_commit: cd04a6695aed090c600446dc9f6b8f9254151cf0
      message: "feat: minute spot price cloud function"
    "{galoy-staging}/functions/coincap-price/yarn.lock":
      file_hash: 9b53dc9b30824780bf6c15ef44241badb34878ad
      from_commit: cd04a6695aed090c600446dc9f6b8f9254151cf0
      message: "feat: minute spot price cloud function"
    "{galoy-staging}/functions/gossip/Makefile":
      file_hash: 4bd31c272cc20dd908cca58026e317b09a332d07
      from_commit: 987bd9455cda37a6e685a4a965093ff35d7c9639
      message: "feat: persisting gossip data in bigquery (#325)"
    "{galoy-staging}/functions/gossip/README.md":
      file_hash: 735870097c67f558204bfa4d1c5c853e83ced926
      from_commit: 68ddd5637e8f4784c13540603c7af58945f285c2
      message: "chore: fix type in function/gossip/README.md"
    "{galoy-staging}/functions/gossip/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 987bd9455cda37a6e685a4a965093ff35d7c9639
      message: "feat: persisting gossip data in bigquery (#325)"
    "{galoy-staging}/functions/gossip/main.py":
      file_hash: c2bf7a1c20a878e2b2ab22cac5423d1b47bd64b0
      from_commit: 73fa44d913de5f94fde4e7223f61ef448ac6b8f6
      message: "feat: full gossip message archive (#995)"
    "{galoy-staging}/functions/gossip/requirements.txt":
      file_hash: 2345d65f05c127775b20fa2242eb3fd7fac5a4b3
      from_commit: 987bd9455cda37a6e685a4a965093ff35d7c9639
      message: "feat: persisting gossip data in bigquery (#325)"
    "{galoy-staging}/functions/gossip2/Makefile":
      file_hash: eacf36225c774fc9677e4cd6f75e441230c5ec86
      from_commit: 73fa44d913de5f94fde4e7223f61ef448ac6b8f6
      message: "feat: full gossip message archive (#995)"
    "{galoy-staging}/functions/gossip2/README.md":
      file_hash: 138525de82d0b024546ec3922bbe5f2674b84ed2
      from_commit: 73fa44d913de5f94fde4e7223f61ef448ac6b8f6
      message: "feat: full gossip message archive (#995)"
    "{galoy-staging}/functions/gossip2/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 73fa44d913de5f94fde4e7223f61ef448ac6b8f6
      message: "feat: full gossip message archive (#995)"
    "{galoy-staging}/functions/gossip2/main.py":
      file_hash: 71e6a89fb1ab64cf002f0996fdddb98f3176d655
      from_commit: d1e10d3d4601095c5a3392f2c05b6f84c414d30c
      message: "chore: ignore problematic future dated gossip message"
    "{galoy-staging}/functions/gossip2/requirements.txt":
      file_hash: 2345d65f05c127775b20fa2242eb3fd7fac5a4b3
      from_commit: 73fa44d913de5f94fde4e7223f61ef448ac6b8f6
      message: "feat: full gossip message archive (#995)"
    "{galoy-staging}/functions/main.tf":
      file_hash: 7cf933cdce934802b8f2775c93576f4a35c4c5c3
      from_commit: 9ecaf59297c2f5125f276d18cf528efa501d4451
      message: "chore: temporarily remove sumsub cloud functions to unblock pipeline"
    "{galoy-staging}/functions/okex-balances/Makefile":
      file_hash: ae85ec017fb6a7d42eca17d9aa61a9c17f25516f
      from_commit: 89adedb4b873c93b54d3fb59018db917c5074f6b
      message: "feat: initial commit of okx postion & balances"
    "{galoy-staging}/functions/okex-balances/README.md":
      file_hash: b644082b24460147ea4601ecde425544c710663c
      from_commit: 31d9c69e421696fe998bad300ed6124e926ad068
      message: "chore: add readme"
    "{galoy-staging}/functions/okex-balances/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 89adedb4b873c93b54d3fb59018db917c5074f6b
      message: "feat: initial commit of okx postion & balances"
    "{galoy-staging}/functions/okex-balances/main.py":
      file_hash: f7669ebc74738b732d7810644809bec38793d3a2
      from_commit: a1f67354720617f4102b827021ed32fca08380a3
      message: "feat: add upl to okex balances"
    "{galoy-staging}/functions/okex-balances/requirements.txt":
      file_hash: 15464163dd8f572bc7282b8b0bb3cf60a0fd914a
      from_commit: 53e18c791d949409bb5ded93c67b08d4989d176d
      message: "fix: ccxt version for gcp"
    "{galoy-staging}/functions/okex-bid-ask/Makefile":
      file_hash: 71f31163121f3d30316f86193fc5173fc3792153
      from_commit: 4094c2f666c89082390c0be6340754be55110b57
      message: "fix: change the limitPrice to the swapPrice using OKX API"
    "{galoy-staging}/functions/okex-bid-ask/bin/call-function.sh":
      file_hash: e058c7d0ace3039b5ad96b690857b157b60e898e
      from_commit: 4094c2f666c89082390c0be6340754be55110b57
      message: "fix: change the limitPrice to the swapPrice using OKX API"
    "{galoy-staging}/functions/okex-bid-ask/index.js":
      file_hash: d57b2a0b9022c9bdbf4372afe16388860307ea26
      from_commit: 4094c2f666c89082390c0be6340754be55110b57
      message: "fix: change the limitPrice to the swapPrice using OKX API"
    "{galoy-staging}/functions/okex-bid-ask/package.json":
      file_hash: 7f1ce9126477747c5f40f3bf5b38c9b3760a7ab5
      from_commit: 4094c2f666c89082390c0be6340754be55110b57
      message: "fix: change the limitPrice to the swapPrice using OKX API"
    "{galoy-staging}/functions/okex-bid-ask/yarn.lock":
      file_hash: 9b53dc9b30824780bf6c15ef44241badb34878ad
      from_commit: 4094c2f666c89082390c0be6340754be55110b57
      message: "fix: change the limitPrice to the swapPrice using OKX API"
    "{galoy-staging}/functions/okex-bills-archive/Makefile":
      file_hash: 2fc4bde2e7f590b2b895fe2b57bfe268ffaa25fd
      from_commit: 5452f8f4b592640bae99b4134c715fdb11e1b869
      message: "feat: create clean okex bills archive"
    "{galoy-staging}/functions/okex-bills-archive/README.md":
      file_hash: b644082b24460147ea4601ecde425544c710663c
      from_commit: 5452f8f4b592640bae99b4134c715fdb11e1b869
      message: "feat: create clean okex bills archive"
    "{galoy-staging}/functions/okex-bills-archive/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 5452f8f4b592640bae99b4134c715fdb11e1b869
      message: "feat: create clean okex bills archive"
    "{galoy-staging}/functions/okex-bills-archive/main.py":
      file_hash: 05cafaee72c808a25b82194f553986771b74767d
      from_commit: 18ca08daa11c27b7203aff3376cac62029878d60
      message: "fix: okex bills archive endpoint changes (#847)"
    "{galoy-staging}/functions/okex-bills-archive/requirements.txt":
      file_hash: 0377a58b42d4937a7bc1fdc3b56345887678ae5a
      from_commit: 9d7a37963c7c62653061290cefe57daf5b2c1708
      message: "fix: clean requirements"
    "{galoy-staging}/functions/okex-bills/Makefile":
      file_hash: 06e0be671cccfe083cc44d7c220e16528ec30952
      from_commit: 164ecbc0d4edb8ca94f63fea13fd23528e68ced8
      message: "fix: discard unknown okex bill details api response fields (#358)"
    "{galoy-staging}/functions/okex-bills/README.md":
      file_hash: b644082b24460147ea4601ecde425544c710663c
      from_commit: 1dc10a0f4e57ee80897e890c8b8c4e5b6f75e34e
      message: "fix: typo"
    "{galoy-staging}/functions/okex-bills/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: ba49298b0af749d6c76b28b94b740cd78b12b241
      message: Functions consistency and minor reformat
    "{galoy-staging}/functions/okex-bills/main.py":
      file_hash: 95f5160e978d5290784516e66087d26231609221
      from_commit: 164ecbc0d4edb8ca94f63fea13fd23528e68ced8
      message: "fix: discard unknown okex bill details api response fields (#358)"
    "{galoy-staging}/functions/okex-bills/requirements.txt":
      file_hash: 15464163dd8f572bc7282b8b0bb3cf60a0fd914a
      from_commit: 53e18c791d949409bb5ded93c67b08d4989d176d
      message: "fix: ccxt version for gcp"
    "{galoy-staging}/functions/okex-funding-rates/Makefile":
      file_hash: 2b8a84f324ba03a38a6e7d19cc47728abace19de
      from_commit: 1a65aa8c0623afd16110afe3e353ec78f9e3cba5
      message: "feat: add funding rate collection for yield calc"
    "{galoy-staging}/functions/okex-funding-rates/README.md":
      file_hash: b644082b24460147ea4601ecde425544c710663c
      from_commit: 1a65aa8c0623afd16110afe3e353ec78f9e3cba5
      message: "feat: add funding rate collection for yield calc"
    "{galoy-staging}/functions/okex-funding-rates/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 1a65aa8c0623afd16110afe3e353ec78f9e3cba5
      message: "feat: add funding rate collection for yield calc"
    "{galoy-staging}/functions/okex-funding-rates/main.py":
      file_hash: 9ec0946cd3bb5a5fcf86a061f957e5f0dfc242e5
      from_commit: 1a65aa8c0623afd16110afe3e353ec78f9e3cba5
      message: "feat: add funding rate collection for yield calc"
    "{galoy-staging}/functions/okex-funding-rates/requirements.txt":
      file_hash: 15464163dd8f572bc7282b8b0bb3cf60a0fd914a
      from_commit: 53e18c791d949409bb5ded93c67b08d4989d176d
      message: "fix: ccxt version for gcp"
    "{galoy-staging}/functions/okex-position/Makefile":
      file_hash: 1c0a3132922d27267c3943bff18b113b28d72152
      from_commit: 5bad32cb0c7d3f23b02acd5a9682b81c7b5d9bae
      message: "fix: ccxt version for okx demo"
    "{galoy-staging}/functions/okex-position/README.md":
      file_hash: b644082b24460147ea4601ecde425544c710663c
      from_commit: 31d9c69e421696fe998bad300ed6124e926ad068
      message: "chore: add readme"
    "{galoy-staging}/functions/okex-position/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 89adedb4b873c93b54d3fb59018db917c5074f6b
      message: "feat: initial commit of okx postion & balances"
    "{galoy-staging}/functions/okex-position/main.py":
      file_hash: 40da98ed0de1d70dd09eef5eb0672de565d207f5
      from_commit: f0aa6fefb550a01b2e7562f8c4ea80b1e6a50cd7
      message: "fix: okex position nullable fields"
    "{galoy-staging}/functions/okex-position/requirements.txt":
      file_hash: 15464163dd8f572bc7282b8b0bb3cf60a0fd914a
      from_commit: 53e18c791d949409bb5ded93c67b08d4989d176d
      message: "fix: ccxt version for gcp"
    "{galoy-staging}/functions/okx-funding-account-history/Makefile":
      file_hash: fd1e0580891a4907248b4675c7e770bf3c8531ed
      from_commit: c570fc4b2a43f34c0a5b66d157ae0091cf669e18
      message: "feat: okx funding account history (#1115)"
    "{galoy-staging}/functions/okx-funding-account-history/README.md":
      file_hash: 7846721923ccbdcc1ffb13008ea6b2ef613a9cbf
      from_commit: c570fc4b2a43f34c0a5b66d157ae0091cf669e18
      message: "feat: okx funding account history (#1115)"
    "{galoy-staging}/functions/okx-funding-account-history/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: c570fc4b2a43f34c0a5b66d157ae0091cf669e18
      message: "feat: okx funding account history (#1115)"
    "{galoy-staging}/functions/okx-funding-account-history/main.py":
      file_hash: 4514e496686b663a47860e40581067aa008c9744
      from_commit: b738afa4aa5509791091a864f1406fb79763e6e1
      message: "feat: okx funding account history clound function (#1130)"
    "{galoy-staging}/functions/okx-funding-account-history/requirements.txt":
      file_hash: 51fdaa682cef859396de828484c2fb48c54b9b85
      from_commit: a214f6cfdf15669c97a4407198f0263f91c2ee39
      message: "fix: requirements for all libs in use (#1139)"
    "{galoy-staging}/functions/okx-trading-account-history/Makefile":
      file_hash: adcd14b7c482b42082088f53239e668b8f3bcbc2
      from_commit: 097cef47eb5eee61a8c242b5fc53c53700e5b9ab
      message: "fix: trading account history cloud function dirname (#986)"
    "{galoy-staging}/functions/okx-trading-account-history/README.md":
      file_hash: 7846721923ccbdcc1ffb13008ea6b2ef613a9cbf
      from_commit: 097cef47eb5eee61a8c242b5fc53c53700e5b9ab
      message: "fix: trading account history cloud function dirname (#986)"
    "{galoy-staging}/functions/okx-trading-account-history/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 097cef47eb5eee61a8c242b5fc53c53700e5b9ab
      message: "fix: trading account history cloud function dirname (#986)"
    "{galoy-staging}/functions/okx-trading-account-history/main.py":
      file_hash: c253dcb416359dfd3c084b09b5cc6ecda99f5e00
      from_commit: 097cef47eb5eee61a8c242b5fc53c53700e5b9ab
      message: "fix: trading account history cloud function dirname (#986)"
    "{galoy-staging}/functions/okx-trading-account-history/requirements.txt":
      file_hash: 2b108edeabb1df8191028685266dfeb2a1bfc1a1
      from_commit: 097cef47eb5eee61a8c242b5fc53c53700e5b9ab
      message: "fix: trading account history cloud function dirname (#986)"
    "{galoy-staging}/functions/onfido-workflow-runs/Makefile":
      file_hash: ab69457722cca21ce529c71f577fcd61c11ccf38
      from_commit: cc5a0a2c622b5080a5fd618879ed5212fdec91b3
      message: "feat: poll Onfido API for latest workflow runs (#999)"
    "{galoy-staging}/functions/onfido-workflow-runs/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: cc5a0a2c622b5080a5fd618879ed5212fdec91b3
      message: "feat: poll Onfido API for latest workflow runs (#999)"
    "{galoy-staging}/functions/onfido-workflow-runs/main.py":
      file_hash: dc229cda19604f2389d5dc907c25584b8f14c4cb
      from_commit: ac35aa54d0c12c908efe4943db89ad1300dcbd6b
      message: "fix: discard new fields in onfido's API response (#1170)"
    "{galoy-staging}/functions/onfido-workflow-runs/requirements.txt":
      file_hash: 4006aa530c2e3eb8af4c91ab2721caebaf6703dd
      from_commit: cc5a0a2c622b5080a5fd618879ed5212fdec91b3
      message: "feat: poll Onfido API for latest workflow runs (#999)"
    "{galoy-staging}/functions/regulatory-report-export/Makefile":
      file_hash: 45509789de75e0a773c2b4b9229246b91c1b0afc
      from_commit: bea4ccf8b851639a19b4f24078f62c82db864fa9
      message: "feat: reg report list (#1391)"
    "{galoy-staging}/functions/regulatory-report-export/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: bea4ccf8b851639a19b4f24078f62c82db864fa9
      message: "feat: reg report list (#1391)"
    "{galoy-staging}/functions/regulatory-report-export/main.py":
      file_hash: f36bbeae8e051be4d065977bff83f544ad7aab8a
      from_commit: 02874b8ff8e278b986402bade4994cb8e8a0cc47
      message: "fix: reg report list table names (#1405)"
    "{galoy-staging}/functions/regulatory-report-export/requirements.txt":
      file_hash: 0d31071060e8c2e8735c2e28a6a42b98e1ef6f20
      from_commit: bea4ccf8b851639a19b4f24078f62c82db864fa9
      message: "feat: reg report list (#1391)"
    "{galoy-staging}/functions/secrets.tf":
      file_hash: 7aec472aa486a8a3ec8c3864cbc79b311c258082
      from_commit: 7151ff34c2fc5070c72888502bda9bf3377f7462
      message: "chore: temporarily remove sumsub secrets to unblock pipeline"
    "{galoy-staging}/functions/statuspage/Makefile":
      file_hash: 3de2dc463ad7c86fc522b8302eda12c063f3702a
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/bin/call-function.sh":
      file_hash: e058c7d0ace3039b5ad96b690857b157b60e898e
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/dist/galoy.d.ts":
      file_hash: f91e72d5b5d2fc73ccbde84b76c4469aaf88b51c
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/dist/galoy.js":
      file_hash: 51d0bf2ebf4df1ecc7cb753029f5c1c0bda29baf
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/dist/index.cjs":
      file_hash: 08d3c311673ca32b572790d8cf612a667dc9fd6d
      from_commit: a9b59403c4888a60549be069758fb3ddb40534f4
      message: "chore: selest statuspage metric id based on env + fmt"
    "{galoy-staging}/functions/statuspage/dist/index.d.ts":
      file_hash: cb0ff5c3b541f646105198ee23ac0fc3d805023e
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/dist/statuspage.d.ts":
      file_hash: e5b1ea64819c4cdb0aba5c0c45df39c355fd9187
      from_commit: a9b59403c4888a60549be069758fb3ddb40534f4
      message: "chore: selest statuspage metric id based on env + fmt"
    "{galoy-staging}/functions/statuspage/dist/statuspage.js":
      file_hash: 5083495ac92d0cb582663e2cd859988230e27b4e
      from_commit: a9b59403c4888a60549be069758fb3ddb40534f4
      message: "chore: selest statuspage metric id based on env + fmt"
    "{galoy-staging}/functions/statuspage/firebase.json":
      file_hash: 9bec626ee2ec5b9b260b1f333192d6690370ae94
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/galoy.ts":
      file_hash: 5a03128459cc7459d762abca68318893eede056f
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/index.ts":
      file_hash: d79181f150c188b97acebbe5a57e895c12ffd8df
      from_commit: a9b59403c4888a60549be069758fb3ddb40534f4
      message: "chore: selest statuspage metric id based on env + fmt"
    "{galoy-staging}/functions/statuspage/old-index.js":
      file_hash: 5834ec0033cca7025ea4db0d9335f5b9d88f1676
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/old-package.json":
      file_hash: a706c0373ac2e224e388750d2d2abf56740f5a9c
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/package.json":
      file_hash: 8053d886a685e7d604bb853235d6d47c3207b5c7
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/rollup.config.js":
      file_hash: eb11857e4a198749456279ab6d35b06924cda36a
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/statuspage.ts":
      file_hash: d59a20360e572336990b3b1d62ded409962e3135
      from_commit: a9b59403c4888a60549be069758fb3ddb40534f4
      message: "chore: selest statuspage metric id based on env + fmt"
    "{galoy-staging}/functions/statuspage/tsconfig.json":
      file_hash: 06a24afdbff2dbd294ddec6194fbce92d3f7dcf2
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/statuspage/yarn.lock":
      file_hash: 0527ac865fd45ec16ba6a0044fb02a5354723246
      from_commit: 3075f2d7a54852cb40708d62a326d79308550f42
      message: "feat: add statuspage function (wip)"
    "{galoy-staging}/functions/sumsub-applicants/Makefile":
      file_hash: 1aecdded3076e42a6ef0788ee4844280feaa2516
      from_commit: 0b80cb0de05c0b9ec3ce84f0c7255095930873fb
      message: "fix: sumsub applicant func exec region to EU (#1414)"
    "{galoy-staging}/functions/sumsub-applicants/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 2c61b7d4e4204e0042b09fb26f283684b9ef3595
      message: "feat: sumsub applicants (#1394)"
    "{galoy-staging}/functions/sumsub-applicants/main.py":
      file_hash: cb54287b0e2ddfacdaaa9ed188fc995df4d0e8ca
      from_commit: 0b80cb0de05c0b9ec3ce84f0c7255095930873fb
      message: "fix: sumsub applicant func exec region to EU (#1414)"
    "{galoy-staging}/functions/sumsub-applicants/requirements.txt":
      file_hash: c3820c7f3d5b3eb5b3fab0898632790209727312
      from_commit: 2c61b7d4e4204e0042b09fb26f283684b9ef3595
      message: "feat: sumsub applicants (#1394)"
    "{galoy-staging}/functions/twilio-billing/Makefile":
      file_hash: bdedc2bd58784580eb2ede4926ac70679df906ed
      from_commit: 18061fe8d3573b7f9e35093132ed5a25143738b5
      message: "feat: cloud function to fetch billing data from Twilio (#1352)"
    "{galoy-staging}/functions/twilio-billing/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 18061fe8d3573b7f9e35093132ed5a25143738b5
      message: "feat: cloud function to fetch billing data from Twilio (#1352)"
    "{galoy-staging}/functions/twilio-billing/main.py":
      file_hash: 455494d3f14104b523ba5da63a4304d6cfab5dec
      from_commit: d4c832248c1676034231e4b756f423f9e99f08d3
      message: "chore: reduce auto backfilling window for twilio billing function (#1360)"
    "{galoy-staging}/functions/twilio-billing/requirements.txt":
      file_hash: 4006aa530c2e3eb8af4c91ab2721caebaf6703dd
      from_commit: 18061fe8d3573b7f9e35093132ed5a25143738b5
      message: "feat: cloud function to fetch billing data from Twilio (#1352)"
    "{galoy-staging}/functions/twilio-verifications/Makefile":
      file_hash: b06000c860ba9f653ad741ac38b1ad604bc783db
      from_commit: c194113832dddf2964ed101e76fcf7ba0f15de50
      message: "chore: poll for onfido verification details"
    "{galoy-staging}/functions/twilio-verifications/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: c194113832dddf2964ed101e76fcf7ba0f15de50
      message: "chore: poll for onfido verification details"
    "{galoy-staging}/functions/twilio-verifications/main.py":
      file_hash: d9b4dae7f3da4f22b90b106e8d7c1ed480dbc8d8
      from_commit: c194113832dddf2964ed101e76fcf7ba0f15de50
      message: "chore: poll for onfido verification details"
    "{galoy-staging}/functions/twilio-verifications/requirements.txt":
      file_hash: 4006aa530c2e3eb8af4c91ab2721caebaf6703dd
      from_commit: c194113832dddf2964ed101e76fcf7ba0f15de50
      message: "chore: poll for onfido verification details"
    "{galoy-staging}/functions/watchlist-compare/Makefile":
      file_hash: 5f90dd3385ae79f19b4f4430259291493b912744
      from_commit: 856f1d831b6b260298066832be533641f919b116
      message: "feat: aml watchlist compare (#1252)"
    "{galoy-staging}/functions/watchlist-compare/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 856f1d831b6b260298066832be533641f919b116
      message: "feat: aml watchlist compare (#1252)"
    "{galoy-staging}/functions/watchlist-compare/main.py":
      file_hash: eade6d6cf40dc7c712c2a9f1b2a0adf09eb6430a
      from_commit: c3f2de1de9aed7ad1790218a8730cd7d825d8a49
      message: "feat: aml watchlist compare using offset  (#1328)"
    "{galoy-staging}/functions/watchlist-compare/requirements.txt":
      file_hash: d3d5113036dec8ffe37c5e486eea8482c890083d
      from_commit: d96f168e3f7068770d21749b17ab98af5ed5f9fb
      message: "fix: aml watchlist memory requirements (#1268)"
    "{galoy-staging}/functions/watchlist-download/Makefile":
      file_hash: 216cc2d1d8670bbaa1e966242fbd9da7d73272fd
      from_commit: 3a6e09987b29e9b5eeb079bd858a5b1cb8d39731
      message: "feat: aml watchlist download (#1251)"
    "{galoy-staging}/functions/watchlist-download/bin/call-function.sh":
      file_hash: f6dc4052b196984d7ab49c7764a4ca668134e5e8
      from_commit: 3a6e09987b29e9b5eeb079bd858a5b1cb8d39731
      message: "feat: aml watchlist download (#1251)"
    "{galoy-staging}/functions/watchlist-download/main.py":
      file_hash: 0a0b705773b8b60f06080e620c7c817d80fc9cfd
      from_commit: d96f168e3f7068770d21749b17ab98af5ed5f9fb
      message: "fix: aml watchlist memory requirements (#1268)"
    "{galoy-staging}/functions/watchlist-download/requirements.txt":
      file_hash: cf527b5d2990420dfec2100c23d5906ffed03ca0
      from_commit: d96f168e3f7068770d21749b17ab98af5ed5f9fb
      message: "fix: aml watchlist memory requirements (#1268)"
    "{galoy-staging}/imports/raw_dataset/main.tf":
      file_hash: 5b8bc19ea1699dc83526ba3d577d7f70e880f594
      from_commit: 4c005f9cb3c4dca82f592e76e4c0a32ab9d6636c
      message: "fix: change openoms email to blinkbtc.com (#1475)"
propagated_from: galoy-staging

