---
version: 32
current:
  head_commit: d6d8b26e4f31c528691a3ee04a210093f36d2eaa
  propagated_head: 4c005f9cb3c4dca82f592e76e4c0a32ab9d6636c
  files:
    "{galoy-staging}/imports/raw_dataset/main.tf":
      file_hash: 5b8bc19ea1699dc83526ba3d577d7f70e880f594
      from_commit: 4c005f9cb3c4dca82f592e76e4c0a32ab9d6636c
      message: "fix: change openoms email to blinkbtc.com (#1475)"
    "{galoy-staging}/kafka/bigquery-schemas/mongo-accounts.json":
      file_hash: 59dd9413263b54dff97e6a10983a955673cbafdb
      from_commit: 23922c205aaf8f56329a5800e63088da6473014d
      message: "chore: fix updatedByPrivilegedClientId in kafka schema for accounts collection"
    "{galoy-staging}/kafka/bigquery-schemas/mongo-medici-balances.json":
      file_hash: cdb7a50e4d1847dad6e6ebbc2e16c3c347df852f
      from_commit: df781798d8c11fa87901c3c42b0fffd4fa31c4a2
      message: "chore: wallTime is in all mongo schemas now"
    "{galoy-staging}/kafka/bigquery-schemas/mongo-medici-journals.json":
      file_hash: 6f160c928d7ec6b79f39fc9e34ecfb9f48fff822
      from_commit: df781798d8c11fa87901c3c42b0fffd4fa31c4a2
      message: "chore: wallTime is in all mongo schemas now"
    "{galoy-staging}/kafka/bigquery-schemas/mongo-medici-transaction-metadatas.json":
      file_hash: 6ba2f51e9754837c5425c43519fc71e626a3837d
      from_commit: df781798d8c11fa87901c3c42b0fffd4fa31c4a2
      message: "chore: wallTime is in all mongo schemas now"
    "{galoy-staging}/kafka/bigquery-schemas/mongo-medici-transactions.json":
      file_hash: 6b628060d30c1b4fac9e89394d9daf86cae2388b
      from_commit: 74e92bead34a9782eae48c8175f6363841281ac2
      message: "fix: add bundle_completion_state to updatedFields (#1326)"
    "{galoy-staging}/kafka/bigquery-schemas/mongo-wallets.json":
      file_hash: 33efae9840397a4d32550a4cd5d9f4b09e68fee9
      from_commit: a7f9095ef9a3a6b754e701c964c37a62bb62f849
      message: "chore: undo last commit"
    "{galoy-staging}/kafka/bigquery-schemas/pg_stablesats_public_galoy_transactions.json":
      file_hash: 856ffd7d7b642a455c2055c10c06ffd3c3cf18e7
      from_commit: cf3b6ab2cde3e1dae09acdf7fae32b621640f8ee
      message: "fix: format schemas (#674)"
    "{galoy-staging}/kafka/bigquery-schemas/pg_stablesats_public_okex_orders.json":
      file_hash: 19544df4e3304d1e10ff76c6e77eecfab1deb6c1
      from_commit: c619bd766daafbf5616a8310c1e81da11e15a5cf
      message: "chore: change okex bigquery schemas as per kafka smt (#676)"
    "{galoy-staging}/kafka/bigquery-schemas/pg_stablesats_public_okex_transfers.json":
      file_hash: 4f6b59e04dad19aee895b1535d1db24c978f76df
      from_commit: c619bd766daafbf5616a8310c1e81da11e15a5cf
      message: "chore: change okex bigquery schemas as per kafka smt (#676)"
    "{galoy-staging}/kafka/bigquery-schemas/pg_stablesats_public_sqlx_ledger_balances.json":
      file_hash: 48b6a2ad5d23e437c514fb48a3de84914d8ab841
      from_commit: cf3b6ab2cde3e1dae09acdf7fae32b621640f8ee
      message: "fix: format schemas (#674)"
    "{galoy-staging}/kafka/bigquery-schemas/pg_stablesats_public_user_trades.json":
      file_hash: 0e51b7693be6d1215bea0f0f82455a8c52c935b5
      from_commit: cf3b6ab2cde3e1dae09acdf7fae32b621640f8ee
      message: "fix: format schemas (#674)"
    "{galoy-staging}/kafka/main.tf":
      file_hash: 926342604c6f604730efaed35309998d24f14387
      from_commit: 308f6e7e59ecdfa991ae8dc1217c2305f047541d
      message: "fix: cluster kafka medici_transactions table by operationType (#1366)"
propagated_from: galoy-staging

