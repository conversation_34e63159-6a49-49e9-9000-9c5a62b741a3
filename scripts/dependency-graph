#!/bin/sh -e

echo 'digraph dataform_dependency_graph {'
echo '	rankdir="LR"'

tmp=`mktemp`
dataform compile --timeout 5m  --json > $tmp

jq -r '
	.tables |
	.[] |
	[.target.name, .type, .bigquery.partitionBy // "not partitioned"] |
	@tsv
' $tmp |
awk -F '	' '{printf "	%s [shape=none label=<<table><tr><td>%s</td></tr><tr><td>%s</td></tr><tr><td>%s</td></tr></table>>]\n", $1, $1, $2, $3}'

jq -r '
	.tables |
	.[] |
	select(.dependencyTargets != null) |
	.target.name as $parent_name |
	.dependencyTargets |
	.[] |
	[$parent_name, .name] |
	@tsv
' $tmp |
awk '{printf "	%s -> %s\n", $1, $2}'

echo '}'
