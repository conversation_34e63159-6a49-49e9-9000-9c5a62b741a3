--  bigquery: {
--    labels: {
--      execution_env: envs.current
--    }
--  },
config {
  type: "table",
  description: "$description"
}

WITH lava_bank_job_events AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_job_events`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_job_events`
--  $dollar_sign{ref({
--      name: "lava_bank_job_events",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_job_executions AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_job_executions`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_job_executions`
--  $dollar_sign{ref({
--      name: "lava_bank_job_executions",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_jobs AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_jobs`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_jobs`
--  $dollar_sign{ref({
--      name: "lava_bank_jobs",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_loan_events AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_loan_events`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_loan_events`
--  $dollar_sign{ref({
--      name: "lava_bank_loan_events",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_loan_terms AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_loan_terms`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_loan_terms`
--  $dollar_sign{ref({
--      name: "lava_bank_loan_terms",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_loans AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_loans`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_loans`
--  $dollar_sign{ref({
--      name: "lava_bank_loans",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_user_events AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_user_events`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_user_events`
--  $dollar_sign{ref({
--      name: "lava_bank_user_events",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_users AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_users`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_users`
--  $dollar_sign{ref({
--      name: "lava_bank_users",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_withdraw_events AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_withdraw_events`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_withdraw_events`
--  $dollar_sign{ref({
--      name: "lava_bank_withdraw_events",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
),
lava_bank_withdraws AS (
  SELECT *
  FROM `volcano_staging_lava_bank_raw.lava_bank_withdraws`
--  FROM `dataform_eu_$dataform_schema_suffix.lava_bank_withdraws`
--  $dollar_sign{ref({
--      name: "lava_bank_withdraws",
--      schema: envs.currentVolcanoSchema("_lava_bank_raw")
--    })}
)
-- , sumsub_identities AS (
--   SELECT *
--   FROM
--   $dollar_sign{ref("stg_sumsub_identities")}
-- )

SELECT
    $raw_rows_selection
FROM
    lava_bank_job_events
  , lava_bank_job_executions
  , lava_bank_jobs
  , lava_bank_loan_events
  , lava_bank_loan_terms
  , lava_bank_loans
  , lava_bank_user_events
  , lava_bank_users
  , lava_bank_withdraw_events
  , lava_bank_withdraws
--  , sumsub_identities
