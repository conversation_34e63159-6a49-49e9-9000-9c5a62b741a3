
This directory contains scripts used to generate regulatory reporting dataform transformation based on metadata from table [stg_regulatory_report_list](../../definitions/staging/stg_regulatory_report_list.sqlx)

First, run "make setup" or setup manually a virtual python env
Then, generate the dataform transformation for the '04.npb4_17_garantia_hipotecaria.xml' report "make run-dev"
or generate the dataform transformation for a specific report "DATASET_ID=dataform_${DATAFORM_SCHEMA_SUFFIX}  python3 main.py <report_name column in stg_regulatory_report_list table>"
or generate the dataform transformation for ALL reports "DATASET_ID=dataform_${DATAFORM_SCHEMA_SUFFIX}  python3 main.py"

The above will generate 4 dataform transformations named:
1. "stg<report_name>_raw.sqlx"
1. "stg<report_name>.sqlx"
1. "stg<report_name>_error.sqlx"
1. "stg<report_name>_with_assert.sqlx"

The "_raw" file is a template to ingest data from the backend tables and needs to be edited.

The "" file is a transform of the "_raw" without any assertions so that it does not fail the pipeline even if source data is problematic.

The "_error" file is a complementary transform to the "" displaying the problematic source data if any.

The "_with_assert" file is a transform of the "_raw" with assertions so that it fails the pipeline if any source data is problematic.

There's therefore only a need for either "_raw" AND ["", "_error"] OR "_with_assert"

The next step is to copy or move the required dataform files to the staging area: "make copy" / "make move"

Finally, the last step is to 'compile' and 'run-dev' the new dataforms to validate
and possibly iterate on the [metadata](../../definitions/staging/stg_regulatory_report_list.sqlx) definitions to refine the "_with_assert" & "_error", etc.
