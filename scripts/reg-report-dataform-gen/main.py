#!/usr/bin/env python3

import sys
import pandas as pd
from sys import stderr
from os import environ
from google.cloud import bigquery
from string import Template

class ReportList:
	def __init__(self):
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.stg_regulatory_report_list"
		print(self.table_id)

	def get_report_list_df(self):
		try:
			sql_query = f'''
			SELECT
				report_id
				, report_name
				, report_norm
				, report_filename
				, report_format

				, field_id
				, field_name
				, field_type
				, field_format
				, field_max_size
				, field_max_decimal
				, field_desc_original
				, field_desc_english
				, field_assertion
			FROM `{self.table_id}`
			ORDER BY
				report_norm
				, report_id
				, field_id
			;
			'''

			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()

			if len(df) <= 0:
				print(f"No report list data from query='{sql_query}'")
				return None
			print(f"Successfully read {len(df)} rows from {self.table_id}")
			return df
		except Exception as e:
			print(f"Could not get report list data from {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			return None

	def get_distinct_reports_list_df(self, report_name):
		try:
			sql_query = f'''
			SELECT DISTINCT report_norm, report_id, report_name, report_filename
			FROM `{self.table_id}`
			WHERE report_name = '{report_name}'
			ORDER BY report_norm, report_id
			LIMIT 1
			;
			'''

			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()

			if len(df) <= 0:
				print(f"No report list data from query='{sql_query}'")
				return None
			print(f"Successfully read {len(df)} rows from {self.table_id}")
			return df
		except Exception as e:
			print(f"Could not get report list data from {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			return None

	def get_current_report_df(self, report_norm, report_id, report_name):
		try:
			sql_query = f"""
			SELECT
				report_id
				, report_name
				, report_norm
				, report_filename
				, report_format

				, field_id
				, field_name
				, LOWER(field_type) AS field_type
				, field_format
				, field_max_size
				, field_max_decimal
				, field_desc_original
				, field_desc_english
				, field_assertion
			FROM `{self.table_id}`
			WHERE report_norm = '{report_norm}'
			AND report_id = {report_id}
			AND report_name = '{report_name}'
			ORDER BY field_id
			;
			"""

			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()

			if len(df) <= 0:
				print(f"No report list data from query='{sql_query}'")
				return None
			print(f"Successfully read {len(df)} rows from {self.table_id}")
			return df
		except Exception as e:
			print(f"Could not get report list data from {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			return None


def generateRegReportDataform(report_name):
	################################
	# get report list
	################################
	report_list = ReportList()
	# list of reports to produce and fields spec for each
	report_list_df = report_list.get_report_list_df()
	if (report_list_df is None) or len(report_list_df) <=0:
		print("Job failed")
		return

	# list of reports to iterate over
	distinct_reports_list_df = report_list.get_distinct_reports_list_df(report_name)
	if (distinct_reports_list_df is None) or len(distinct_reports_list_df) <=0:
		print("Job failed")
		return

	assertions = ''
	# assertions = '''
	#   assertions: {
	#     uniqueKey: ["account_id"],
	#     rowConditions: [
	#         "number_onboarded_all_time_outer >= number_onboarded_this_month_outer",
	#         "number_onboarded_all_time_inner >= number_onboarded_this_month_inner",
	#     ],
	#     nonNull: ["account_id"],
	#   },
	# '''

	# gen dataform reports
	for distinct_reports_row in distinct_reports_list_df.itertuples():
		current_report_df = report_list.get_current_report_df(distinct_reports_row.report_norm, distinct_reports_row.report_id, distinct_reports_row.report_name)
		raw_rows_selection = []
		error_rows_selection = []
		format_rows_selection = []
		where_clauses = []
		row_conditions = []
		for field_row in current_report_df.itertuples():
			# select
			error_rows_selection.append(f"`{field_row.field_name}`")
			if field_row.field_type == 'xsstring' or field_row.field_type == 'caracter' or field_row.field_type.startswith('char') or field_row.field_type.startswith('varchar'):
				raw_rows_selection.append(f"'TODO' AS `{field_row.field_name}`")
				format_rows_selection.append(f'LEFT(`{field_row.field_name}`, {"{0:0.0f}".format(field_row.field_max_size)}) AS `{field_row.field_name}`')
			elif field_row.field_type == 'xsdate' or field_row.field_type == 'date':
				raw_rows_selection.append(f"'20081031' AS `{field_row.field_name}`")
				# format_rows_selection.append(f"to_char(`{field_row.field_name}`, 'yyyyMMdd') AS `{field_row.field_name}`")
				format_rows_selection.append(f"FORMAT_DATE('%Y%m%d', CAST(`{field_row.field_name}` AS DATE)) AS `{field_row.field_name}`")
			elif field_row.field_type == 'xsdecimal'or field_row.field_type.startswith('numeric'):
				raw_rows_selection.append(f"7060.0 AS `{field_row.field_name}`")
				# format_rows_selection.append(f"ROUND(`{field_row.field_name}`, {'{0:0.0f}'.format(field_row.field_max_decimal)})::varchar({'{0:0.0f}'.format(field_row.field_max_size + 1)}) AS `{field_row.field_name}`")
				format_rows_selection.append(f"CAST(ROUND(`{field_row.field_name}`, {'{0:0.0f}'.format(field_row.field_max_decimal)}) AS STRING) AS `{field_row.field_name}`")
			elif field_row.field_type == 'xsint' or field_row.field_type == 'smallint':
				raw_rows_selection.append(f"7060 AS `{field_row.field_name}`")
				format_rows_selection.append(f"CAST(`{field_row.field_name}` AS STRING) AS `{field_row.field_name}`")
			else:
				raw_rows_selection.append(f"'TODO' AS `{field_row.field_name}`")
				format_rows_selection.append(f"`{field_row.field_name}` AS `{field_row.field_name}`")
			# assertions / errors
			if not pd.isna(field_row.field_assertion):
				where_clauses.append(f"NOT({field_row.field_assertion})")
				row_conditions.append(f'"{field_row.field_assertion}"')
				error_rows_selection.append(f"NOT({field_row.field_assertion}) AS `error_{field_row.field_name}`")
		# template substitution data
		format_rows_selection.append(f"CURRENT_TIMESTAMP() AS created_at")
		# all dataform use raw view as source
		table_name = f'stg_{distinct_reports_row.report_name.replace(".", "_")}_raw'
		where_clause = '\n  OR '.join(where_clauses)
		row_condition = '\n          , '.join(row_conditions)
		assertions = '''
		  assertions: {
		    rowConditions: [
							row_condition
		    ],
		  },
		'''.replace("row_condition", row_condition)
		data = {
			'dollar_sign': '$',
			'assertions': f'{assertions}',
			'where_clause': f"WHERE {where_clause}",
			'description': f"Regulatory Report '{distinct_reports_row.report_filename}' of norm '{distinct_reports_row.report_norm}'",
			'raw_rows_selection': '\n  , '.join(raw_rows_selection),
			'error_rows_selection': '\n  , '.join(error_rows_selection),
			'format_rows_selection': '\n  , '.join(format_rows_selection),
			'source_table_name': table_name,
			'dataform_schema_suffix': environ.get('DATAFORM_SCHEMA_SUFFIX'),
		}
		# template of the raw view dataform transform
		file_name = f'stg_{distinct_reports_row.report_name.replace(".", "_")}_raw.sqlx'
		with open('regulatory_report_raw_view_template.txt', 'r') as f:
			src = Template(f.read())
			file_data = src.substitute(data)
			with open(file_name, 'w') as f2:
				f2.write(file_data)

		# template of the no-assert dataform transform
		file_name = f'stg_{distinct_reports_row.report_name.replace(".", "_")}.sqlx'
		with open('regulatory_report_stg_table_template.txt', 'r') as f:
			src = Template(f.read())
			file_data = src.substitute(data)
			with open(file_name, 'w') as f2:
				f2.write(file_data)

		# template of the asserted dataform transform
		file_name = f'stg_{distinct_reports_row.report_name.replace(".", "_")}_with_assert.sqlx'
		with open('regulatory_report_stg_table_assert_template.txt', 'r') as f:
			src = Template(f.read())
			file_data = src.substitute(data)
			with open(file_name, 'w') as f2:
				f2.write(file_data)

		# template of the error dataform transform
		file_name = f'stg_{distinct_reports_row.report_name.replace(".", "_")}_error.sqlx'
		with open('regulatory_report_error_table_template.txt', 'r') as f:
			src = Template(f.read())
			file_data = src.substitute(data)
			with open(file_name, 'w') as f2:
				f2.write(file_data)

	print("Job done", file=stderr)
	return

if __name__ == "__main__":
	for arg in sys.argv[1:]:
		generateRegReportDataform(arg)
