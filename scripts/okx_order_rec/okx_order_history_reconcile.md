# replace 'sv' with your own suffix
export DATAFORM_SCHEMA_SUFFIX=sv

# replace with latest file names
export ORDER_HISTORY_FILE="Order History: 2024-11-02~2025-02-03.csv"
export TRADE_DETAILS_FILE="Trade Details: 2024-11-02~2025-02-03.csv"
export TRADING_HISTORY_FILE="OKX Trading History: 2024-11-02~2025-02-02~UTC+8~352731.csv"



# truncate old table
bq query --use_legacy_sql=false 'truncate table dataform_sv.okx_order_history';
bq query --use_legacy_sql=false 'truncate table dataform_sv.okx_order_history_trade_details';
bq query --use_legacy_sql=false 'truncate table dataform_sv.okx_trading_history';
# or drop old table
bq query --use_legacy_sql=false 'drop table dataform_sv.okx_trading_history';


# create if not exist
bq query << 'EOF'
#standardSQL
    CREATE TABLE dataform_sv.okx_order_history
    (
        Order_ID INT64,
        Order_Time TIMESTAMP,
        Instrument STRING,
        Symbol STRING,
        Lvg INT64,
        Side STRING,
        Order_Type STRING,
        Order_Amount INT64,
        Filled_Amount INT64,
        Amount_Unit STRING,
        Order_Price FLOAT64,
        Avg_Filled_Price FLOAT64,
        Price_Unit STRING,
        PNL FLOAT64,
        Fee FLOAT64,
        Fee_Unit STRING,
        Status STRING
    );
EOF
# create if not exist
bq query << 'EOF'
#standardSQL
    CREATE TABLE dataform_sv.okx_order_history_trade_details
    (
        Order_ID INT64,
        Trade_ID INT64,
        Trade_Time TIMESTAMP,
        Instrument STRING,
        Symbol STRING,
        Filled_Amount INT64,
        Filled_Amount_Unit STRING,
        Filled_Price FLOAT64,
        Fill_Price_Unit STRING,
        Trading_Volume FLOAT64,
        Trading_Volume_Unit STRING,
        taker_or_maker STRING,
        Fee FLOAT64,
        Fee_Unit STRING
    );
EOF
# create if not exist
bq query << 'EOF'
#standardSQL
    CREATE TABLE dataform_sv.okx_trading_history
    (
        id INT64,
        Order_id INT64,
        Time TIMESTAMP,
        Trade_Type STRING,
        Symbol STRING,
        Action STRING,
        Amount INT64,
        Trading_Unit STRING,
        Filled_Price FLOAT64,
        PnL FLOAT64,
        Fee FLOAT64,
        Fee_Unit STRING,
        Position_Change FLOAT64,
        Position_Balance FLOAT64,
        Balance_Change FLOAT64,
        Balance FLOAT64,
        Balance_Unit STRING
    );
EOF


# load data
bq load --format=csv --autodetect dataform_sv.okx_order_history               $ORDER_HISTORY_FILE;
bq load --format=csv --autodetect dataform_sv.okx_order_history_trade_details $TRADE_DETAILS_FILE;
bq load --format=csv --autodetect dataform_sv.okx_trading_history             $TRADING_HISTORY_FILE;
# or override columns type
bq load --skip_leading_rows=1 --source_format=CSV --autodetect dataform_sv.okx_order_history "Order History: 2024-11-02~2025-02-03.csv" Order_ID:INT64,Order_Time:TIMESTAMP,Instrument:STRING,Symbol:STRING,Lvg:INT64,Side:STRING,Order_Type:STRING,Order_Amount:INT64,Filled_Amount:INT64,Amount_Unit:STRING,Order_Price:NUMERIC,Avg_Filled_Price:NUMERIC,Price_Unit:STRING,PNL:NUMERIC,Fee:NUMERIC,Fee_Unit:STRING,Status:STRING;


# verify ddl
bq query --use_legacy_sql=false "select ddl from dataform_sv.INFORMATION_SCHEMA.TABLES where table_name = 'okx_order_history'";
bq query --use_legacy_sql=false "select ddl from dataform_sv.INFORMATION_SCHEMA.TABLES where table_name = 'okx_order_history_trade_details'";
bq query --use_legacy_sql=false "select ddl from dataform_sv.INFORMATION_SCHEMA.TABLES where table_name = 'okx_trading_history'";


# verify data
bq query --max_rows 1000 --use_legacy_sql=false 'select count(*) as num_rows from dataform_sv.okx_order_history';
bq query --max_rows 1000 --use_legacy_sql=false 'select * from dataform_sv.okx_order_history order by 1,2';
#
bq query --max_rows 1000 --use_legacy_sql=false 'select count(*) as num_rows from dataform_sv.okx_order_history_trade_details';
bq query --max_rows 1000 --use_legacy_sql=false 'select * from dataform_sv.okx_order_history_trade_details order by 1,2';
#
bq query --max_rows 1000 --use_legacy_sql=false 'select count(*) as num_rows from dataform_sv.okx_trading_history';
bq query --max_rows 1000 --use_legacy_sql=false 'select * from dataform_sv.okx_trading_history order by 1,2';


# reconcile

##
## Trades/orders in raw_okex_bills_archive / stg_okex_bills_archive
## that are not found in TRADING_HISTORY_FILE
##

##
## Trades/orders in TRADING_HISTORY_FILE
## that are not found in raw_okex_bills_archive / stg_okex_bills_archive
##
