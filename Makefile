BIN_DIR=node_modules/.bin

fmt:
	terraform fmt -recursive .
	# $(BIN_DIR)/dataform format .

install:
	dataform install

test:
	$(BIN_DIR)/dataform test --timeout 5m

compile:
	$(BIN_DIR)/dataform compile --timeout 5m

run:
	$(BIN_DIR)/dataform run --timeout 5m --schema-suffix=${DATAFORM_SCHEMA_SUFFIX} --vars=${DATAFORM_VARS}

run-dev:
	$(BIN_DIR)/dataform run --timeout 5m --schema-suffix=${DATAFORM_SCHEMA_SUFFIX} --vars=${DATAFORM_VARS} --tags=current-dev --full-refresh

install-lava:
	dataform install lava-bank

run-lava:
	$(BIN_DIR)/dataform run lava-bank --timeout 5m --schema-suffix=eu_${DATAFORM_SCHEMA_SUFFIX} --vars=${DATAFORM_VARS}

dep-graph:
	./scripts/dependency-graph > dependencies.dot
	dot -T svg dependencies.dot > dependencies.svg

clean:
	rm -f dependencies.dot
	rm -f dependencies.dot
