const reportingProject = "galoy-reporting"

const staging = {
  database: reportingProject,
  name: "galoy-staging",
  prefix: "galoy_staging",
  analytics: "analytics_220400481",
  network: "signet",
}

const bbw = {
  database: reportingProject,
  name: "galoy-bbw",
  prefix: "galoy_bbw",
  analytics: "analytics_220400481",
  network: "mainnet",
}

const paramsByName = {
  [staging.name]: staging,
  [bbw.name]: bbw,
}

module.exports = {
  all: [staging, bbw],
  current: dataform.projectConfig.vars.executionEnv,
  currentSchema: (postfix) => {
    return paramsByName[dataform.projectConfig.vars.executionEnv].prefix + postfix
  },
  project: reportingProject,
  currentAnalyticsSchema: () => {
    return paramsByName[dataform.projectConfig.vars.executionEnv].analytics
  },
  currentNetwork: () => {
    return paramsByName[dataform.projectConfig.vars.executionEnv].network
  },
}
