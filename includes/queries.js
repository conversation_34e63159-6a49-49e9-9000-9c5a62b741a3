function summarize_transactions(table, date_part, date_part_name) {
	return `
		SELECT
			DATE_TRUNC(DATE(recorded_at), ${date_part}) AS ${date_part_name},
			user_key,
			COUNTIF(
				user_direction != "Intra-user transfer"
				AND NOT is_quiz_reward
			) AS number_of_transactions,
			COUNTIF(is_quiz_reward) AS number_of_quiz_rewards,
			SUM(IF(user_direction != "Intra-user transfer", usd_user_volume, 0)) AS usd_user_volume,
			SUM(IF(user_direction = "Inbound", usd_user_volume, 0)) AS usd_inbound_user_volume,
			SUM(IF(user_direction = "Outbound", usd_user_volume, 0)) AS usd_outbound_user_volume,
			SUM(IF(user_direction != "Intra-user transfer", usd_user_volume, 0)) AS usd_bank_volume,
			SUM(IF(user_direction = "Intra-user transfer", usd_user_volume, 0)) AS usd_trade_volume,
			COALESCE(SUM(sat_onchain_fee_revenue), 0) AS sat_onchain_fee_revenue,
			COALESCE(SUM(sat_stablesats_spread_revenue), 0) AS sat_stablesats_spread_revenue,
			COALESCE(SUM(usd_onchain_fee_revenue), 0) AS usd_onchain_fee_revenue,
			COALESCE(SUM(usd_stablesats_spread_revenue), 0) AS usd_stablesats_spread_revenue,
			ARRAY_AGG(DISTINCT counter_party_user_key IGNORE NULLS) AS intraledger_counter_parties,
			SUM(IF(currency="USD", balance_change, 0)) AS cent_stablesats_balance_change,
			SUM(IF(currency="BTC", balance_change, 0)) AS sat_bitcoin_balance_change,
			MIN(previous_recorded_at) AS previous_recorded_at,

		FROM ${table}

		WHERE NOT voided
			AND NOT voider

		GROUP BY ${date_part_name}, user_key
	`;
}
module.exports = { summarize_transactions };
