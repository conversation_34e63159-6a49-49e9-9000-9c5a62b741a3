
This directory contains scripts used to
backfill the `okx_funding_account_history` table in `galoy_bbw_functions_raw` with
funding account history from okx ui.

First, manually import the data into the user's `okx_funding_account_history` table.
Then, replace "sv" with the user that created the table,
in [********-okx_funding_account_history.sqlx](./********-okx_funding_account_history.sqlx)
and merge the result into the top-level [definitions directory](../../definitions).
The procedure will check for new data each time.
