config {
  hasOutput: true,
}

INSERT INTO ${
	ref({
		name: "okx_funding_account_history",
		schema: envs.currentSchema("_functions_raw")
	})
}
(
    id,
    time,
    type,
    amount,
    before_balance,
    after_balance,
    fee,
    symbol
)
SELECT DISTINCT
    id,
    time,
    type,
    amount,
    before_balance,
    after_balance,
    fee,
    symbol
FROM `dataform_sv.okx_funding_account_history`
WHERE id > (SELECT COALESCE(MAX(id), 0) FROM ${
	ref({
		name: "okx_funding_account_history",
		schema: envs.currentSchema("_functions_raw")
	})}
);
