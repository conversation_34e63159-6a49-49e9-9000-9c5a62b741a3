
This directory contains scripts used to
backfill the `coincap_price` table in `galoy_bbw_functions_raw`
and `galoy_staging_functions_raw` with historical price data
from coincap.io.



Configure the script to fetch data from from the time you already data up to.
```
ENVIRONMENT=galoy_staging
bq query --use_legacy_sql=false "SELECT COUNT(*) as total_rows, MIN(timestamp) as earliest_timestamp, MAX(timestamp) as latest_timestamp FROM \`galoy-reporting.${ENVIRONMENT}_functions_raw.coincap_price\`"
```
* auth with the service account
```
gcloud auth application-default login
```
* Run the script [create-tmp-table.py](./create-tmp-table.py)
```
cd manual-backfills/********-coincap-price
make setup
make run
```

Then, replace "oms" if user that created the temporary bills table is different,
in [********_coincap_price.sqlx](./********_coincap_price.sqlx)
and merge the result into the top-level [definitions directory](../definitions) or run the manual backfill for `galoy_staging` and `galoy_bbw`:
```
ENVIRONMENT=galoy_staging

bq query --use_legacy_sql=false "
INSERT INTO \`galoy-reporting.${ENVIRONMENT}_functions_raw.coincap_price\`
SELECT DISTINCT *
FROM \`galoy-reporting.dataform_oms.********_coincap_price\`
WHERE timestamp > (
  SELECT MAX(timestamp) 
  FROM \`galoy-reporting.${ENVIRONMENT}_functions_raw.coincap_price\`
)"
```


Check if the backfill was successful:
```
ENVIRONMENT=galoy_staging

bq query --use_legacy_sql=false "SELECT COUNT(*) as total_rows, MIN(timestamp) as earliest_timestamp, MAX(timestamp) as latest_timestamp FROM \`galoy-reporting.${ENVIRONMENT}_functions_raw.coincap_price\`"
```
