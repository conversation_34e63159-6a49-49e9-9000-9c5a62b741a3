config {
	hasOutput: true
}

IF (
	SELECT MIN(timestamp) FROM ${ref({
			name: "coincap_price",
			schema: envs.currentSchema("_functions_raw")
		})}
	) < "2022-12-01"
THEN RETURN;
END IF;


INSERT INTO ${
	ref({
		name: "coincap_price",
		schema: envs.currentSchema("_functions_raw")
	})
}

	SELECT DISTINCT *

	FROM dataform_jireva.20230119_coincap_price

	WHERE timestamp BETWEEN (
			SELECT TIMESTAMP_SUB(first_timestamp, INTERVAL 1 HOUR)
			FROM ${ref("stg_static_values")}
		) AND (
			SELECT MIN(timestamp)
			FROM ${ref({
				name: "coincap_price",
				schema: envs.currentSchema("_functions_raw")
			})}
		);
