#!/usr/bin/env python3

import requests
import numpy as np
import pandas as pd

from os import environ
from sys import stderr
from time import sleep
from itertools import chain

from google.cloud import bigquery
from google.cloud.exceptions import NotFound

schema = [
	bigquery.SchemaField("timestamp", "TIMESTAMP", mode="NULLABLE"),
	bigquery.SchemaField("price_usd", "STRING", mode="NULLABLE"),
]

with open('./coincap.apikey', 'rb') as f:
	apikey = str(f.read()).strip()

class Spot:
	def __init__(self):
		self.n_requests = 0
		self.s = requests.Session()
		self.s.headers.update({
			'Authorization': 'Bearer ' + apikey
		})

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_day(self, day):
		self.n_requests += 1
		if self.n_requests == 200:
			sleep(30)
			self.n_requests = 0
		day = pd.Timestamp(day.astype('datetime64[D]')).timestamp()*1000
		nextdayday = day+1000*60*60*24
		r = self.s.get(
			'http://api.coincap.io/v2/assets/bitcoin/history',
			params={'interval':'m1', 'start':day, 'end':nextdayday})
		return r.json()['data']

	def get_interval(self, start, end):
		start = start.astype('datetime64[D]').astype('int')
		end = end.astype('datetime64[D]').astype('int')
		return chain.from_iterable([self.get_day(np.int64(x)) for x in range(start, end+1)])

# Interval for back fill needs to start before the first transaction
# and end after the first record in `galoy_*_functions_raw.coincap_price`:
# 	SELECT min(recorded_at) FROM dataform_galoy_bbw.fct_user_wallet_transactions
start = pd.to_datetime("2020-07-23 00:00:00 UTC").to_datetime64()
# 	SELECT min(timestamp) FROM galoy_bbw_functions_raw.coincap_price
end = pd.to_datetime("2023-01-20 15:35:00 UTC").to_datetime64()

def get_history():
	with Spot() as s:
		spot = pd.DataFrame(s.get_interval(start, end))
	spot.columns = ["priceUsd","time","circulatingSupply","date"]

	history = pd.DataFrame()
	history['timestamp'] = pd.to_datetime(spot['time'], unit='ms')
	history['price_usd'] = spot['priceUsd']

	return history[
		(history.timestamp > start) &
		(history.timestamp < end)
	].sort_values('timestamp').drop_duplicates()

if __name__ == "__main__":
	client = bigquery.Client()
	table_id =  f"galoy-reporting.dataform_{environ['DATAFORM_SCHEMA_SUFFIX']}.20230119_coincap_price"

	try:
		client.get_table(table_id)
		print("Table already exists", file=stderr)
		exit()
	except NotFound:
		pass

	table = bigquery.Table(table_id, schema=schema)
	table = client.create_table(table)
	print("Table created", file=stderr)

	df = get_history()
	print("Legacy transactions parsed", file=stderr)

	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)

	job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
	job.result()

	table = client.get_table(table_id)
	print(f"Created {table_id} with {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns", file=stderr)

	print("Job done", file=stderr)
