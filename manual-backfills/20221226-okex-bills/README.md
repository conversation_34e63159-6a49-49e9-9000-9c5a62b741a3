
This directory contains scripts used to
backfill the `okex_bills` table in `galoy_bbw_functions_raw` with
transactions from the legacy dealer database.

First, run the script [create-tmp-bills-table.py](./create-tmp-bills-table.py)
(see [<PERSON><PERSON><PERSON>](./Makefile))
to create a temporary table with the new bills.
Then, replace "jireva" with the user that created the temporary bills table,
in [20221226_okex_bills.sqlx](./20221226_okex_bills.sqlx)
and merge the result into the top-level [definitions directory](../definitions).
The procedure will run once and remove the temporary bills table.
