#!/usr/bin/env python3

import numpy as np
import pandas as pd

from os import environ
from sys import stderr
from decimal import Decimal
from datetime import datetime

from google.cloud import bigquery
from google.cloud.exceptions import NotFound

schema = [
	bigquery.Schema<PERSON>ield("id", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
	bigquery.Schema<PERSON>ield("datetime", "DATETIME", mode="REQUIRED"), # DATETIME?
	bigquery.SchemaField("account", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("referenceId", "INT64", mode="NULLABLE"),
	bigquery.SchemaField("referenceAccount", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("type", "STRING", mode="REQUIRED"),
	bigquery.<PERSON><PERSON><PERSON><PERSON><PERSON>("currency", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("symbol", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("amount", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("before", "BIGNUMERIC", mode="NULLABLE"),  # weird no?
	bigquery.SchemaField("after", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("status", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("bal", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("balChg", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("billId", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("ccy", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("execType", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("fee", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("from", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("instId", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("instType", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("mgnMode", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("notes", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("ordId", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("pnl", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("posBal", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("posBalChg", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("subType", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("sz", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("to", "INT64", mode="NULLABLE"),
	bigquery.SchemaField("ts", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("info_type", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("fee_cost", "BIGNUMERIC", mode="REQUIRED"),
	bigquery.SchemaField("fee_currency", "STRING", mode="REQUIRED")]

def legacy_transactions():
	legacy = dict()
	with open("legacy-dealer-transactions.csv") as f:
		names = next(f).rstrip().split(',')
		for name in names:
			legacy[name] = list()
		for line in f:
			fields = line.rstrip().split(',')
			for (name, field) in zip(names, fields):
				legacy[name].append(field)
	l = pd.DataFrame()
	l['id'] = pd.Series(np.int64(x) for x in legacy['id'])
	l['timestamp'] = pd.Series(np.int64(pd.to_datetime(x).timestamp()) for x in legacy['timestamp'])
	l['datetime'] = pd.Series(pd.to_datetime(x) for x in legacy['timestamp'])
	l['account'] = None
	l['referenceId'] = None
	l['referenceAccount'] = None
	l['type'] = pd.Series({
			"1": "transfer",
			"2": "trade",
			"3": "delivery",
			"4": "auto token conversion",
			"5": "liquidation",
			"6": "margin transfer",
			"7": "interest deduction",
			"8": "fee",
			"9": "adl",
			"10": "clawback",
			"11": "system token conversion",
			"12": "strategy transfer",
			"13": "ddh",
			"14": "block trade",
			"15": "quick margin",
			"18": "profit sharing",
			"22": "repay",
		}.get(x, x) for x in legacy['bill_type_id'])
	l['currency'] = legacy['currency']
	l['symbol'] = ""
	l['amount'] = Decimal(-1)
	l['before'] = Decimal(-1)
	l['after'] = Decimal(-1)
	l['status'] = "ok"
	l['bal'] = pd.Series(Decimal(x) for x in legacy['balance'])
	l['balChg'] = pd.Series(Decimal(x) for x in legacy['balance_change'])
	l['billId'] = pd.Series(np.int64(x) for x in legacy['bill_id'])
	l['ccy'] = ""
	l['execType'] = legacy['execution_type']
	l['fee'] = pd.Series(Decimal(x) for x in legacy['fee'])
	l['from'] = np.int64(-1)
	l['instId'] = legacy['instrument_id']
	l['instType'] = legacy['instrument_type']
	l['mgnMode'] = legacy['margin_mode']
	l['notes'] = pd.Series(x if x!='""' else "" for x in legacy['notes'])
	l['ordId'] = pd.Series(np.uint64(x) if x!='""' else None for x in legacy['order_id'])
	l['pnl'] = pd.Series(Decimal(x) for x in legacy['pnl'])
	l['posBal'] = Decimal(-1)
	l['posBalChg'] = Decimal(-1)
	l['subType'] = pd.Series(np.int64(x) for x in legacy['bill_subtype_id'])
	l['sz'] = pd.Series(np.int64(x) if x.isdigit() else None for x in legacy['quantity'])
	l['to'] = np.int64(-1)
	l['ts'] = l['timestamp']
	l['info_type'] = np.int64(-1)
	l['fee_cost'] = Decimal(-1)
	l['fee_currency'] = ""

	return l

if __name__ == "__main__":
	client = bigquery.Client()
	table_id =  f"galoy-reporting.dataform_{environ['DATAFORM_SCHEMA_SUFFIX']}.20221226_okex_bills"

	try:
		client.get_table(table_id)
		print("Table already exists", file=stderr)
		exit()
	except NotFound:
		pass

	table = bigquery.Table(table_id, schema=schema)
	table = client.create_table(table)
	print("Table created", file=stderr)

	df = legacy_transactions()
	print("Legacy transactions parsed", file=stderr)

	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)

	job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
	job.result()

	table = client.get_table(table_id)
	print(f"Created {table_id} with {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns", file=stderr)

	print("Job done", file=stderr)
