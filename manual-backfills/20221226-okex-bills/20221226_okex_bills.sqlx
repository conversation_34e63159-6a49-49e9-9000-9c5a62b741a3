config {
  hasOutput: true
}

IF "${self()}" != "`galoy-reporting.dataform_galoy_bbw.20221226_okex_bills`"
THEN RETURN;
END IF;

IF (SELECT COUNT(*) FROM galoy_bbw_functions_raw.okex_bills) > 500
THEN RETURN;
END IF;

MERGE INTO galoy_bbw_functions_raw.okex_bills t
	USING (SELECT DISTINCT * FROM dataform_jireva.20221226_okex_bills) s
	ON t.billId = s.billId
	WHEN NOT MATCHED THEN INSERT ROW;

DROP TABLE dataform_jireva.20221226_okex_bills;
