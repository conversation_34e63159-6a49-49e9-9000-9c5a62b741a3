
This directory contains scripts used to
backfill the `okex_funding_rates` table in `galoy_bbw_functions_raw` with
funding rates from the legacy dealer database.

First, run the script [create-tmp-funding-rates.py](./create-tmp-funding-rates.py)
(see [Make<PERSON><PERSON>](./Makefile))
to create a temporary table with the new rates.
Then, replace "jireva" with the user that created the temporary rates table,
in [20230410_okex_funding_rates.sqlx](./20230410_okex_funding_rates.sqlx)
and merge the result into the top-level [definitions directory](../definitions).
The procedure will run once and remove the temporary rates table.
