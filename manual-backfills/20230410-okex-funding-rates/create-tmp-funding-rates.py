#!/usr/bin/env python3

import numpy as np
import pandas as pd

from os import environ
from sys import stderr
from decimal import Decimal
from datetime import datetime

from google.cloud import bigquery
from google.cloud.exceptions import NotFound

schema = [
    bigquery.SchemaField("funding_time", "INT64", mode="REQUIRED"),
    bigquery.SchemaField("instrument_id", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("funding_rate", "BIGNUMERIC", mode="REQUIRED")]


def legacy_funding_rates():
    df = pd.read_csv("legacy-dealer-funding-rates.csv")
    df['funding_time'] = df.funding_time.astype(np.uint64)
    df['instrument_id'] = df.instrument_id.astype(str)
    df['funding_rate'] = df.funding_rate.astype(str).map(Decimal)
    return df


if __name__ == "__main__":
    client = bigquery.Client()
    table_id = f"galoy-reporting.dataform_{environ['DATAFORM_SCHEMA_SUFFIX']}.20230410_okex_funding_rates"

    try:
        client.get_table(table_id)
        print("Table already exists", file=stderr)
        exit()
    except NotFound:
        pass

    table = bigquery.Table(table_id, schema=schema)
    table = client.create_table(table)
    print("Table created", file=stderr)

    df = legacy_funding_rates()
    print("Legacy funding rates parsed", file=stderr)

    job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)

    job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
    job.result()

    table = client.get_table(table_id)
    print(
        f"Created {table_id} with {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns", file=stderr)

    print("Job done", file=stderr)
