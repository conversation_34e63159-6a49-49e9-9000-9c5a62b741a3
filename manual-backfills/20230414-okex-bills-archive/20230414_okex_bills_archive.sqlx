config {
  hasOutput: true,
}

IF (SELECT MIN(timestamp) FROM ${
	ref({
		name: "okex_bills_archive",
		schema: envs.currentSchema("_functions_raw")
	})
}) < 1675180800000
THEN RETURN;
END IF;

INSERT INTO ${
	ref({
		name: "okex_bills_archive",
		schema: envs.currentSchema("_functions_raw")
	})
}
(
	timestamp,
	bill_id,
	type_code,
	sub_type_code,
	balance,
	balance_change,
	fee,
	execution_type,
	order_id,
	pnl,
	size,
	price,
	margin_mode,
	instrument_type,
	instrument_id,
	currency
)
SELECT DISTINCT
	UNIX_MILLIS(_timestamp) AS timestamp,
	_bill_id AS bill_id,
	_bill_type_id AS type_code,
	_bill_subtype_id AS sub_type_code,
	CAST(_balance AS BIGNUMERIC) AS balance,
	CAST(_balance_change AS BIGNUMERIC) AS balance_change,
	CAST(_fee AS BIGNUMERIC) AS fee,
	_execution_type AS execution_type,
	_order_id AS order_id,
	CAST(_pnl AS BIGNUMERIC) AS pnl,
	CAST(_quantity AS BIGNUMERIC) AS size,
	CASE 
		WHEN _bill_type_id = 2 THEN CAST(ROUND(_quantity / ABS(_fee) * CASE WHEN _execution_type = 'T' THEN 0.05 ELSE 0.02 END, 2) AS BIGNUMERIC)
		ELSE 0
	END AS price,
	CASE WHEN _bill_type_id = 1 THEN "" ELSE _margin_mode END AS margin_mode,
	CASE WHEN _bill_type_id = 1 THEN "" ELSE _instrument_type END AS instrument_type,
	CASE WHEN _bill_type_id = 1 THEN "" ELSE _instrument_id END AS instrument_id,
	_currency AS currency
FROM dataform_sv.20230414_okex_bills_archive
WHERE _timestamp < TIMESTAMP_MILLIS(1675180800000);
