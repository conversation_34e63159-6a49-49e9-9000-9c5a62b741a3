
This directory contains scripts used to
backfill the `okex_bills_archive` table in `galoy_bbw_functions_raw` with
bills archive from the legacy dealer database.

First, manually import the legacy dealer extract into a temporary table.
Then, replace "sv" with the user that created the temporary rates table,
in [20230414_okex_bills_archive.sqlx](./20230414_okex_bills_archive.sqlx)
and merge the result into the top-level [definitions directory](../../definitions).
The procedure will run once and then the temporary table can be removed.
