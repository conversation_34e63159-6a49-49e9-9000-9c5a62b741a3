import json
from os import environ
from sys import stderr
from google.cloud import bigquery
from google.cloud.exceptions import NotFound

schema = [
	bigquery.SchemaField("last_update", "TIMESTAMP", mode="REQUIRED"),
	bigquery.Schema<PERSON>ield("pub_key", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("alias", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("color", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("addresses", "STRUCT", mode="REPEATED", fields=[
		bigquery.SchemaField("network", "STRING", mode="REQUIRED"),
		bigquery.SchemaField("addr", "STRING", mode="REQUIRED"),
	]),
	bigquery.SchemaField("features", "STRUCT", mode="REPEATED", fields=[
		bigquery.Schema<PERSON>ield("bit", "INT64", mode="REQUIRED"),
		bigquery.<PERSON>hema<PERSON>ield("name", "STRING", mode="NULLABLE"),
		bigquery.SchemaField("is_required", "BOOL", mode="REQUIRED"),
		bigquery.SchemaField("is_known", "BOOL", mode="REQUIRED"),
	]),
]

if __name__ == "__main__":
	with open("describegraph.json") as f:
		nodes_raw = json.load(f)['nodes']
	nodes = [
			{k:v for k,v in n.items() if k!="features"}
			| {"features" : [v|{"bit":k} for k,v in n["features"].items()]}
		for n in nodes_raw]

	client = bigquery.Client()
	table_id =  f"galoy-reporting.dataform_{environ['DATAFORM_SCHEMA_SUFFIX']}.20230209_nodes"

	try:
		client.get_table(table_id)
		print("Table already exists", file=stderr)
		exit()
	except NotFound:
		pass

	table = bigquery.Table(table_id, schema=schema)
	table = client.create_table(table)
	print("Table created", file=stderr)

	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)

	job = client.load_table_from_json(nodes, table_id, job_config=job_config)
	job.result()

	table = client.get_table(table_id)
	print(f"Created {table_id} with {len(nodes)} {'rows' if len(nodes)>1 else 'row'} with {len(nodes[0])} columns", file=stderr)

	print("Job done", file=stderr)
