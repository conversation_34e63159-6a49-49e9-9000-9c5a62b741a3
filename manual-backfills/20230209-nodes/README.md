
This directory contains scripts used to
backfill the `nodes` table in `galoy_bbw_functions_raw`
and `galoy_staging_functions_raw` with routing graph data.

First, run the script [create-tmp-table.py](./create-tmp-table.py)
(see [Makefile](./Makefile))
to create a temporary table with the new data.
Then, replace "jireva" with the user that created the temporary bills table,
in [stg_nodes.sqlx](./stg_nodes.sqlx)
and merge the result into the top-level [definitions directory](../definitions).
The procedure will only run once.
