config {
  hasOutput: true,
}

INSERT INTO ${
	ref({
		name: "okx_trading_account_history",
		schema: envs.currentSchema("_functions_raw")
	})
}
(
	id,
	order_id,
	time,
	trade_type,
	instrument,
	action,
	amount,
	trading_unit,
	fill_price,
	pnl,
	fee,
	position_change,
	position_balance,
	balance_change,
	balance,
	balance_unit
)
SELECT
	id,
	order_id,
	time,
	trade_type,
	instrument,
	action,
	amount,
	trading_unit,
	fill_price,
	pnl,
	fee,
	position_change,
	position_balance,
	balance_change,
	balance,
	balance_unit
FROM `dataform_sv.okx_trading_account_history`
WHERE id not in (SELECT id FROM ${
	ref({
		name: "okx_trading_account_history",
		schema: envs.currentSchema("_functions_raw")
	})}
);
