#!/usr/bin/env python3

import requests
import numpy as np
import pandas as pd

from os import environ
from sys import stderr
from decimal import Decimal
from time import sleep
from itertools import chain

from google.cloud import bigquery
from google.cloud.exceptions import NotFound

schema = [
	bigquery.Schema<PERSON>ield("ts", "TIMESTAMP", mode="NULLABLE"),
	bigquery.SchemaField("last", "NUMER<PERSON>", mode="NULLABLE"),
	bigquery.SchemaField("lastSz", "NUMERIC", mode="NULLABLE"),
	bigquery.SchemaField("bidPx", "NUMERIC", mode="NULLABLE"),
	bigquery.SchemaField("bidSz", "NUMERIC", mode="NULLABLE"),
	bigquery.SchemaField("askPx", "NUMERIC", mode="NULLABLE"),
	bigquery.SchemaField("askSz", "NUMERIC", mode="NULL<PERSON>LE"),
	bigquery.<PERSON><PERSON><PERSON><PERSON><PERSON>("instType", "STRING", mode="NULLABLE"),
	bigquery.<PERSON>hema<PERSON>ield("instId", "STRING", mode="NULLABLE"),
]

class Swap:
	def __init__(self):
		self.s = requests.Session()

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_page(self, after):
		"https://www.okx.com/docs-v5/en/#rest-api-market-data-get-candlesticks-history"
		r = self.s.get(
			'https://www.okx.com/api/v5/market/history-candles',
			params={'instId':'BTC-USD-SWAP', 'after':after}, timeout=5)
		return r.json()['data']

	def _get_interval(self, start, end):
		start = pd.Timestamp(start).timestamp()
		end = pd.Timestamp(end).timestamp()
		counter = 0
		while True:
			print(f"{pd.to_datetime(start, unit='s')}", file=stderr)
			counter += 1
			if counter == 10:
				sleep(1.1)
				counter = 0
			start += 100*60
			yield self.get_page(int(start*1000))
			if start > end:
				break

	def get_interval(self, start, end):
		return chain.from_iterable(self._get_interval(start, end))

# Interval for back fill needs to start before the first Stablesats transaction
# and end before the first record in `galoy_bbw_functions_raw.okex_bid_ask`:
# 	SELECT min(ts) FROM galoy_bbw_functions_raw.okex_bid_ask
start = pd.to_datetime("2022-06-01 00:00:00 UTC")
end = pd.to_datetime("2022-12-22 15:02:12 UTC")

def get_history():
	with Swap() as s:
		swap = pd.DataFrame(s.get_interval(start, end))
	swap.columns = ["ts","o","h","l","c","vol","volCcy", "volCcyQuote", "confirm"]

	history = pd.DataFrame()
	history['ts'] = pd.to_datetime(swap['ts'], unit='ms')
	history['last'] = None
	history['lastSz'] = None
	history['bidPx'] = pd.Series(Decimal(x) for x in swap['c'])
	history['bidSz'] = None
	history['askPx'] = pd.Series(Decimal(x) for x in swap['c'])
	history['askSz'] = None
	history['instType'] = 'SWAP'
	history['instId'] = 'BTC-USD-SWAP'

	return history[
		(history.ts > start.to_datetime64()) &
		(history.ts < end.to_datetime64())
	].sort_values('ts').drop_duplicates()

if __name__ == "__main__":
	client = bigquery.Client()
	table_id =  f"galoy-reporting.dataform_{environ['DATAFORM_SCHEMA_SUFFIX']}.20230109_okex_bid_ask"

	try:
		client.get_table(table_id)
		print("Table already exists", file=stderr)
		exit()
	except NotFound:
		pass

	table = bigquery.Table(table_id, schema=schema)
	table = client.create_table(table)
	print("Table created", file=stderr)

	df = get_history()
	print("Legacy transactions parsed", file=stderr)

	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)

	job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
	job.result()

	table = client.get_table(table_id)
	print(f"Created {table_id} with {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns", file=stderr)

	print("Job done", file=stderr)
