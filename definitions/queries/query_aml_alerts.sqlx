config {
	type: "table",
}

WITH ranked as (

	SELECT user_key, week_of,
		txs.usd_user_volume,
		txs.number_of_transactions,
		created_at,
		status,
		level,
		account_id,
		phone_number_country_name AS country_by_phone_number,
		device_country AS country_by_ip,
		ROW_NUMBER() OVER (
			PARTITION BY week_of
			ORDER BY txs.usd_user_volume DESC
		) AS weekly_rank,
		ROW_NUMBER() OVER (
			PARTITION BY week_of, phone_number_country_name
			ORDER BY txs.usd_user_volume DESC
		) AS weekly_rank_by_country,
		ROW_NUMBER() OVER (
			PARTITION BY week_of, phone_number_country_name = device_country
			ORDER BY txs.usd_user_volume DESC
		) AS weekly_rank_by_mismatched_countries,
		ROW_NUMBER() OVER (
			PARTITION BY week_of, level
			ORDER BY txs.usd_user_volume DESC
		) AS weekly_rank_by_level,

	FROM dataform_galoy_bbw.stg_weekly_user_transaction_summaries AS txs
		JOIN dataform_galoy_bbw.dim_users USING (user_key)
		JOIN (
			SELECT user_key, account_id
			FROM dataform_galoy_bbw.stg_user_accounts
		) USING (user_key)

	WHERE week_of > DATE_TRUNC("2024-02-01", WEEK)
		AND week_of < DATE_TRUNC(CURRENT_DATE(), WEEK)
		AND NOT is_payroll
		AND NOT galoy_team


), unified AS (

	SELECT *, "Top 5 by volume worldwide" AS flag
	FROM ranked
	WHERE weekly_rank <= 5

	UNION ALL

	SELECT *, "Top 5 by volume El Salvador" AS flag
	FROM ranked
	WHERE country_by_phone_number = "El Salvador" AND weekly_rank_by_country <= 5

	UNION ALL

	SELECT *, "Top 5 mismatched jurisdictions" AS flag
	FROM ranked
	WHERE country_by_phone_number != country_by_ip AND weekly_rank_by_mismatched_countries <= 5

	UNION ALL

	SELECT *, "Top 5 level 1" AS flag
	FROM ranked
	WHERE level = 1 AND weekly_rank_by_level <= 5

	UNION ALL

	SELECT *, "Top 5 level 0" AS flag
	FROM ranked
	WHERE level = 0 AND weekly_rank_by_level <= 5

)

SELECT week_of,
	STRING_AGG(DISTINCT flag, "\n" ORDER BY flag) AS flags,
	ANY_VALUE(account_id) AS account_id,
	ANY_VALUE(username) AS username,
	DATE(ANY_VALUE(created_at)) AS created_at,
	ANY_VALUE(status) AS status,
	ANY_VALUE(level) AS level,
	ANY_VALUE(country_by_phone_number) AS country_by_phone_number,
	ANY_VALUE(country_by_ip) AS country_by_ip,
	COALESCE(ANY_VALUE(onfido_approved), FALSE) AS onfido_approved,
	CAST(ANY_VALUE(unified.usd_user_volume) AS INTEGER) AS usd_week_volume,
	ANY_VALUE(unified.number_of_transactions) AS week_number_of_transactions,
	ROUND(ANY_VALUE(sat_bitcoin_balance) / ${constants.SATS_PER_BITCOIN}, 2) AS btc_bitcoin_balance,
	CAST(ANY_VALUE(usd_stablesats_balance) AS INTEGER) AS usd_stablesats_balance,

FROM unified
	JOIN dataform_galoy_bbw.stg_user_transaction_summaries USING (user_key)
	LEFT JOIN (
		SELECT account_id,
			LOGICAL_OR(workflow_status="approved") AS onfido_approved,
		FROM dataform_galoy_bbw.stg_onfido_workflow_runs
			JOIN dataform_galoy_bbw.stg_onfido_identities USING (applicant_id)
		GROUP BY account_id
	) USING (account_id)
	LEFT JOIN (
		SELECT username, id AS account_id
		FROM ${ref({name: "accounts", schema: envs.currentSchema("_galoy_raw")})}
	) USING (account_id)

GROUP BY week_of, user_key

ORDER BY week_of, flags, usd_week_volume
