config {
	type: "table",
	description: "based on an example report provided by the FIU.	Includes Blink fiat Sell orders from El Salvador users.",
}

SELECT
	account_id AS payer_account_id,
	applicant_id AS payer_applicant_id,

	TIMESTAMP_TRUNC(recorded_at, MINUTE) AS transaction_time,
	currency AS transaction_currency,
	ROUND(sat_user_volume/*********, 8) AS btc_amount,
	ROUND(usd_user_volume, 2) AS usd_amount,
	journal_id AS transaction_id,
	memo,

FROM ${ref("stg_user_wallet_transactions")}
	JOIN ${ref("stg_user_accounts")} USING(user_key)
	JOIN (
		SELECT account_id,
			ANY_VALUE(applicant_id HAVING MAX created_at) AS applicant_id
		FROM ${ref("stg_onfido_identities")}
		GROUP BY account_id
	) USING(account_id)
	JOIN (
		SELECT user_wallet_transaction_key
		FROM ${ref("stg_blink_fiat_transactions")}
	) USING (user_wallet_transaction_key)

WHERE recorded_at > "2023-11-23"
	AND usd_user_volume >= 1000

ORDER BY transaction_time
