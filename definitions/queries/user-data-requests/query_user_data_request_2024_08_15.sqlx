config {
	tags: ["current-dev"],
	type: "table",
}

pre_operations {
	DECLARE accounts ARRAY<STRING>;
	SET accounts = ARRAY((
		SELECT DISTINCT id,
		FROM UNNEST(["bitcoinamigo"
			, "pumpum"
			, "celm88"
		]) AS manual_username
		JOIN ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})} ON LOWER(manual_username) = LOWER(username)
	));
}

SELECT *

FROM ${ref("tvf_user_metadata")} (accounts)
	JOIN ${ref("tvf_user_transaction_summaries")} (accounts) USING (account_id)
