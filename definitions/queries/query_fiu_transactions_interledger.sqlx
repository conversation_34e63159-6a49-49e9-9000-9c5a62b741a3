config {
	type: "table",
	description: "based on an example report provided by the FIU.	Includes all onchain and lighting transactions from El Salvador users, as well as intra-ledger transactions where the payer is an El Salvador user.",
}

SELECT
	ANY_VALUE(account_id HAVING MIN balance_change) AS payer_account_id,
	ANY_VALUE(applicant_id HAVING MIN balance_change) AS payer_applicant_id,
	-- The FIU example has no examples for lightning transactions
	-- (onchain addresses are used as a proxy for the counter party)
	-- Lightning does not have anything like this so we just use the `journal_entry_id`
	CASE
		WHEN LOGICAL_AND(settlement_method = "Onchain")
		THEN ANY_VALUE(payee_addresses HAVING MIN balance_change)[SAFE_ORDINAL(1)]
		ELSE ANY_VALUE(journal_entry_id HAVING MAX balance_change)
	END AS counter_party_id,

	TIMESTAMP_TRUNC(ANY_VALUE(recorded_at), MINUTE) AS transaction_time,
	IF(LOGICAL_AND(currency = "USD"), "USD", "BTC") AS transaction_currency,
	ROUND(ANY_VALUE(sat_user_volume)/*********, 8) AS btc_amount,
	ROUND(ANY_VALUE(usd_user_volume), 2) AS usd_amount,
	journal_id AS transaction_id,
	ANY_VALUE(memo HAVING MIN balance_change) AS memo,

FROM ${ref("stg_user_wallet_transactions")}
	JOIN ${ref("stg_user_accounts")} USING(user_key)
	JOIN (
		SELECT account_id,
			ANY_VALUE(applicant_id HAVING MAX created_at) AS applicant_id
		FROM ${ref("stg_onfido_identities")}
		GROUP by account_id
	) USING(account_id)

WHERE recorded_at > "2023-11-23"
	AND NOT voided
	AND NOT voider

GROUP BY journal_id

HAVING ANY_VALUE(phone_number_country_name HAVING MIN balance_change) = "El Salvador"
	AND LOGICAL_OR(usd_user_volume >= 1000)
	-- Exclude intra-ledger transactions between El Salvador users
	AND IF(ANY_VALUE(bank_direction)="Inter-user transfer", LOGICAL_OR(phone_number_country_name != "El Salvador"), TRUE)
	-- Exclude blink fiat transactions:
	AND LOGICAL_AND(user_key != (
		SELECT user_key
		FROM ${ref("stg_user_accounts")}
		WHERE account_id = "03a8fb5a-c855-42b8-8de5-ff8a6d704505"
	))

ORDER BY transaction_time
