config {
	type: "view",
}

WITH days AS (

	SELECT
		DATE(DATE_TRUNC(day, MONTH)) AS m,
		user_key,
		txs.usd_stablesats_funding_revenue,
		usd_onchain_fee_revenue,
		usd_stablesats_spread_revenue,
		days_since_created_at,
		phone_number_country_name,
		device_continent,
		device_country,
		txs.transacted,

	FROM dataform_galoy_bbw.fct_daily_user_summaries AS txs
		JOIN dataform_galoy_bbw.dim_users USING (user_key)

	WHERE day BETWEEN "2022-12-01" AND "2023-12-31"
		AND NOT is_quiz_reward_farmer
		AND NOT is_possible_circles_farmer
		AND NOT is_payroll
		AND NOT galoy_team

), ww AS (

	SELECT m,
		COUNT(DISTINCT IF(transacted, user_key, NULL)) AS transacting_users,
		SUM(
			usd_stablesats_funding_revenue
			+ usd_onchain_fee_revenue
			+ usd_stablesats_spread_revenue
		) AS usd_revenue,
		COUNT(DISTINCT IF(days_since_created_at=0, user_key, null)) AS new_users,
		"All Users" AS region,

	FROM days

	GROUP BY m

), es AS (

	SELECT m,
		COUNT(DISTINCT IF(transacted, user_key, NULL)) AS transacting_users,
		SUM(
			usd_stablesats_funding_revenue
			+ usd_onchain_fee_revenue
			+ usd_stablesats_spread_revenue
		) AS usd_revenue,
		COUNT(DISTINCT IF(days_since_created_at=0, user_key, null)) AS new_users,
		IF(phone_number_country_name = "El Salvador", "El Salvador", "Outside El Salvador") AS region,

	FROM days

	GROUP BY m, region

), other AS (


	SELECT m,
		COUNT(DISTINCT IF(transacted, user_key, NULL)) AS transacting_users,
		SUM(
			usd_stablesats_funding_revenue
			+ usd_onchain_fee_revenue
			+ usd_stablesats_spread_revenue
		) AS usd_revenue,
		COUNT(DISTINCT IF(days_since_created_at=0, user_key, null)) AS new_users,
		CASE
			WHEN device_continent = "Africa" then "Africa"
			WHEN device_continent = "Americas"
				AND device_country NOT IN ("Canada", "United States")
				THEN "LatAm"
		END AS region,

	FROM days

	GROUP BY m, region

	HAVING region IS NOT NULL

), ms AS (

	SELECT * FROM es
	UNION ALL
	SELECT * FROM other
	UNION ALL
	SELECT * FROM ww

), lms AS (

	SELECT *,
		LAG(transacting_users) OVER (PARTITION BY region ORDER BY m) AS lm_transacting_users,
		LAG(usd_revenue) OVER (PARTITION BY region ORDER BY m) AS lm_usd_revenue,
		LAG(new_users) OVER (PARTITION BY region ORDER BY m) AS lm_new_users,

	FROM ms

), moms AS (

	SELECT *,
		(transacting_users - lm_transacting_users)/lm_transacting_users AS mom_transacting_users,
		(usd_revenue - lm_usd_revenue)/lm_usd_revenue AS mom_usd_revenue,
		(new_users - lm_new_users)/lm_new_users AS mom_new_users,

	FROM lms

	WHERE m >= "2023-01-01"

)

SELECT region,
	FORMAT("%0.2f%%", 100*AVG(mom_transacting_users)) as mom_transacting_users,
	FORMAT("%0.2f%%", 100*AVG(mom_usd_revenue)) as mom_usd_revenue,
	FORMAT("%0.2f%%", 100*AVG(mom_new_users)) as mom_new_users,

FROM moms

GROUP BY region
