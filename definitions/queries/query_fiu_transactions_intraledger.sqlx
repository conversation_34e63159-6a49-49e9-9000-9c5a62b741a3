config {
	type: "table",
	description: "based on an example report provided by the FIU.  Includes intra-ledger transactions where both counter parties are El Salvador users.",
}

SELECT
	ANY_VALUE(account_id HAVING MIN balance_change) AS payer_account_id,
	ANY_VALUE(applicant_id HAVING MIN balance_change) AS payer_applicant_id,
	ANY_VALUE(account_id HAVING MAX balance_change) AS payee_account_id,
	ANY_VALUE(applicant_id HAVING MAX balance_change) AS payee_applicant_id,

	TIMESTAMP_TRUNC(ANY_VALUE(recorded_at), MINUTE) AS transaction_time,
	STRING_AGG(currency, "→" ORDER BY balance_change ASC) AS transaction_currency,
	ROUND(ANY_VALUE(sat_user_volume)/*********, 8) AS btc_amount,
	ROUND(ANY_VALUE(usd_user_volume), 2) AS usd_amount,
	journal_id AS transaction_id,
	ANY_VALUE(memo HAVING MIN balance_change) AS memo,

FROM ${ref("stg_user_wallet_transactions")}
	JOIN ${ref("stg_user_accounts")} USING(user_key)
	JOIN (
		SELECT account_id,
			ANY_VALUE(applicant_id HAVING MAX created_at) AS applicant_id
		FROM ${ref("stg_onfido_identities")}
		GROUP BY account_id
	) USING(account_id)

WHERE recorded_at > "2023-11-23"
	AND bank_direction = "Inter-user transfer"
	AND NOT voided
	AND NOT voider

GROUP BY journal_id

HAVING COUNT(DISTINCT account_id) = 2
	AND COUNT(DISTINCT journal_entry_id) = 2
	AND LOGICAL_AND(phone_number_country_name = "El Salvador")
	AND LOGICAL_OR(usd_user_volume >= 1000)

ORDER BY transaction_time
