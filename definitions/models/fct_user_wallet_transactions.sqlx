config {
	type: "table",
	bigquery: {
		clusterBy: ["recorded_at"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	transaction_key,
	user_wallet_key,
	user_key,
	recorded_at,
	settlement_method,
	bank_direction,
	user_direction,
	wallet_direction,
	currency,
	sat_wallet_volume,
	usd_wallet_volume,
	sat_user_volume,
	usd_user_volume,
	sat_bank_volume,
	usd_bank_volume,
	sat_onchain_fee_revenue,
	usd_onchain_fee_revenue,
	sat_stablesats_spread_revenue,
	usd_stablesats_spread_revenue,
	is_quiz_reward,
	sat_protocol_fee_estimate,
	sat_protocol_fee_paid,
	usd_protocol_fee_estimate,
	usd_protocol_fee_paid,
	counter_party_user_key,
	sat_bria_fee_reconciliation,
	sat_bria_cpfp,
	sat_imbalance,
	env_specific.*

FROM ${ref("stg_user_wallet_transactions")}
	LEFT JOIN ${ref("stg_env_user_wallet_transaction_facts")} AS env_specific USING (user_wallet_transaction_key)

WHERE NOT voided
	AND NOT voider
	AND NOT is_quiz_reward
	AND NOT is_fee_reimbursement
