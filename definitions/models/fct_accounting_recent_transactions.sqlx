config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH account_names AS (

	SELECT DISTINCT account_id, account_name

	FROM ${ref("stg_accounting_recently_active_accounts")}

)

SELECT l.*,
	lnames.account_name,
	r.account_id AS counter_party_account_id,
	rnames.account_name AS counter_party_name,

FROM ${ref("stg_accounting_recent_transactions")} AS l
	LEFT JOIN account_names AS lnames USING(account_id)
	LEFT JOIN (
		SELECT journal_id, account_id
		FROM ${ref("stg_accounting_recent_transactions")}
	) AS r
		ON l.journal_id = r.journal_id
		AND l.account_id != r.account_id
	LEFT JOIN account_names AS rnames ON  r.account_id = rnames.account_id


ORDER BY recorded_at, accounts DESC
