config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH features_table AS (

	SELECT centroid_id, ARRAY_AGG((feature, numerical_value)) AS features

	FROM ML.CENTROIDS(MODEL ${ref("stg_ml_user_clustering_model")} , STRUCT(true AS standardize))

	GROUP BY centroid_id

), prediction_summaries AS (

	SELECT centroid_id,
		COUNT(DISTINCT user_key) AS n_user_keys,
		ANY_VALUE(median_centroid_distance) AS median_distance

	FROM ${ref("stg_ml_user_clustering_predictions")}

	WHERE centroid_distance_rank < 0.99

	GROUP BY centroid_id

), centroids AS (

	SELECT centroid_id, features,
		COALESCE(n_user_keys, 0) AS n_user_keys,
		median_distance

	FROM features_table
		LEFT JOIN prediction_summaries USING (centroid_id)

), labels AS (

	SELECT "Loop out" AS label,
		[
			("p_onchain_out_transactions", 1.0),
			("p_lightning_in_transactions", 1.0),
			("p_onchain_out_user_volume", 1.0),
			("p_lightning_in_user_volume", 1.0),
			("p_onchain_in_transactions", -1.0),
			("p_lightning_out_transactions", -1.0),
			("p_onchain_in_user_volume", -1.0),
			("p_lightning_out_user_volume", -1.0)
		] AS prototype

	UNION ALL

	SELECT "Lightning" AS label,
		[
			("p_lightning_out_transactions", 2.0),
			("p_lightning_in_transactions", 2.0),
			("p_lightning_out_user_volume", 2.0),
			("p_lightning_in_user_volume", 2.0)
		] AS prototype

	UNION ALL

	SELECT "Onchain" AS label,
		[
			("p_onchain_out_transactions", 2.0),
			("p_onchain_in_transactions", 2.0),
			("p_onchain_out_user_volume", 2.0),
			("p_onchain_in_user_volume", 2.0)
		] AS prototype

	UNION ALL

	SELECT "Trader" AS label,
		[
			("p_intra_transactions", 2.0),
			("p_intra_user_volume", 2.0)
		] AS prototype

	UNION ALL

	SELECT "Intra-ledger" as label,
		[
			("p_inter_in_transactions", 2.0),
			("p_inter_in_user_volume", 2.0),
			("p_inter_out_transactions", 2.0),
			("p_inter_out_user_volume", 2.0)
		] AS prototype


),  distances AS (

	SELECT centroid_id, label,
		ML.DISTANCE(features, prototype) AS distance

	FROM centroids, labels

	WHERE n_user_keys > 100

), ranked AS (

	SELECT *,
		ROW_NUMBER() OVER (PARTITION BY centroid_id ORDER BY distance) AS label_rank,
		ROW_NUMBER() OVER (PARTITION BY label ORDER BY distance) AS centroid_rank

	FROM distances

), labeled AS (

	SELECT centroid_id, label

	FROM ranked

	WHERE label_rank = 1 AND centroid_rank = 1

)

SELECT 0 AS cluster_id, "Unclustered" AS labeled, NULL AS number_of_users, NULL AS median_distance_from_centroid

UNION ALL

SELECT
	centroid_id AS cluster_id,
	COALESCE(label, IF(n_user_keys < 100,
		"Unclustered",
		"Unlabeled cluster " ||
			CAST(ROW_NUMBER() OVER (PARTITION BY label IS NULL ORDER BY n_user_keys DESC) AS STRING)
	)) AS label,
	n_user_keys AS number_of_users,
	median_distance AS median_distance_from_centroid

FROM centroids
	LEFT JOIN labeled USING (centroid_id)
