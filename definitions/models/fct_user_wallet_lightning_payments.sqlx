config {
	type: "table",
	assertions: {
		uniqueKey: ["payment_hash"],
		rowConditions: [
			// Consistency assetions:

			// All payments here have isCompleteRecord set to true, they should not be pending in the ledger.
			// "NOT pending",

			// Full consistency:
			// "IF(voided, payment_status=\"failed\", payment_status=\"settled\")",

			// Ensure inconsistency does not lead to loss of funds from now on
			"IF(voided, protocol_created_at < \"2022-05-22\" OR payment_status=\"failed\", TRUE)",
		]
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH ledger AS (

	SELECT
		transaction_hash as payment_hash,
		COUNT(DISTINCT transaction_key) AS number_of_user_attempts,
		LOGICAL_AND(voided) AS voided,
		LOGICAL_OR(pending) AS pending

	FROM ${ref("stg_user_wallet_transactions")}

	WHERE settlement_method = "Lightning"
		AND bank_direction = "Outbound"
		AND NOT voider

	GROUP BY transaction_hash

), lightning_payments AS (

	SELECT
		ledger.*,
		payment_status,
		sent_from_pubkey,
		msat_amount AS msat_protocol_amount,
		lnpayments.created_at AS protocol_created_at,
		confirmed_details.confirmedAt AS protocol_confirmed_at,
		confirmed_details.milliSatsFee AS msat_protocol_fee,
		ARRAY_LENGTH(attempts) as number_of_protocol_attempts,
		COALESCE(
			confirmed_details.destination,
			ARRAY(
				SELECT (route.hops [SAFE_ORDINAL(ARRAY_LENGTH(route.hops))]).public_key
				FROM UNNEST(attempts)
				WHERE ARRAY_LENGTH(route.hops) > 0
				LIMIT 1
			) [SAFE_ORDINAL(1)]
		) AS destination,
		ARRAY_LENGTH(confirmed_details.hopPubkeys) AS number_of_hops,
		(
			SELECT MIN(ARRAY_LENGTH(route.hops)) FROM UNNEST(attempts)
		) AS min_number_of_hops,
		(
			SELECT MAX(ARRAY_LENGTH(route.hops)) FROM UNNEST(attempts)
		) AS max_number_of_hops,
		pseudo_destination,
		ARRAY(
			SELECT alias
			FROM UNNEST(pseudo_destination) AS pubkey
				JOIN ${ref("stg_public_lightning_nodes")} USING(pubkey)
		) AS pseudo_destination_alias,

	FROM ${ref("stg_lightning_payments")} AS lnpayments
		INNER JOIN ledger USING (payment_hash)
		LEFT JOIN ${ref("stg_payment_request_details")} USING (payment_hash)

)

SELECT * REPLACE (
		CASE
			WHEN pubkey IS NULL AND ARRAY_LENGTH(pseudo_destination) > 0
			THEN TO_JSON_STRING(pseudo_destination)
			ELSE pubkey
		END AS pseudo_destination,
		CASE
			WHEN pubkey IS NULL AND ARRAY_LENGTH(pseudo_destination_alias) > 0
			THEN TO_JSON_STRING(ARRAY(
				SELECT DISTINCT alias FROM UNNEST(pseudo_destination_alias) AS alias ORDER BY alias
			))
			ELSE COALESCE(alias, SUBSTR(destination, 1, 12))
		END AS pseudo_destination_alias
	)

FROM lightning_payments
	LEFT JOIN ${ref("stg_public_lightning_nodes")} ON destination = pubkey
