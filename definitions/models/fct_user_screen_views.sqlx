config {
	type: "table",
	assertions: {
		nonNull: ["recorded_at", "user_key"]
	},
	bigquery: {
		clusterBy: ["recorded_at", "user_key"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT user_key, session_id, session_number, recorded_at, session_event_number,
	engagement_time_msec / 1000 AS engagement_time_sec,
	COALESCE(
		screen,
		screen_class,
		"Unnamed"
	) AS screen_name,
	COALESCE(category, "Unclassified") AS screen_category,

FROM ${ref("stg_user_screen_views")}
	INNER JOIN ${ref("stg_analytics_user_ids")} USING(user_id)
	LEFT JOIN ${ref("stg_screens_categories")} USING (screen)
