config {
	type: "table",
	assertions: {
		uniqueKey: ["day", "user_key"],
		nonNull: [
			"day",
			"user_key",
			"number_of_transactions",
			"usd_user_volume",
			"usd_bank_volume",
			"usd_onchain_fee_revenue",
			"usd_stablesats_spread_revenue",
			"sat_onchain_fee_revenue",
			"sat_stablesats_spread_revenue",
			"engagement_time_sec",
			"sat_bitcoin_close_balance",
			"usd_bitcoin_close_balance",
			"sat_stablesats_close_balance",
			"usd_stablesats_close_balance",
			"usd_stablesats_funding_revenue",
			"lifetime_usd_stablesats_funding_revenue",
			"days_since_created_at",
			"transacted",
			"transacted_past_7_days",
			"transacted_past_30_days",
			"transacted_past_90_days",
			"will_transact_tomorrow",
			"opened_app",
			"opened_app_past_7_days",
			"opened_app_past_30_days",
			"opened_app_past_90_days",
			"transacted_7_days_ago",
			"transacted_30_days_ago",
			"transacted_90_days_ago",
			"transacted_past_7_days_7_days_ago",
			"transacted_past_30_days_30_days_ago",
			"transacted_past_90_days_90_days_ago",
			"number_of_users_onboarded",
			"onboarded_past_7_days",
			"onboarded_past_30_days",
			"onboarded_past_90_days",
			"lifetime_usd_onchain_fee_revenue",
			"lifetime_usd_stablesats_spread_revenue",
			"lifetime_sat_onchain_fee_revenue",
			"lifetime_sat_stablesats_spread_revenue",
			"created_this_month",
			"created_last_month",
			"transacted_this_month",
			"transacted_last_month",
			"opened_app_this_month",
			"opened_app_last_month",
			"opened_app_past_180_days",
		],
	},
	bigquery: {
		clusterBy: ["day"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH onboarding AS (

	SELECT onboarded_by_user_key AS user_key,
		DATE(recorded_at) as day,
		COUNT(DISTINCT user_key) AS number_onboarded,

	FROM ${ref("stg_user_onboarding")}

	WHERE seconds_after_created_at < (60*60*24*7)

	GROUP BY onboarded_by_user_key, day

), calendar_months AS (

	SELECT user_key AS _user_key,
		DATE_TRUNC(day, MONTH) AS month,
		SUM(number_of_transactions) > 0 AS transacted_this_month,
		SUM(engagement_time_sec) > 0 AS opened_app_this_month,

	FROM ${ref("stg_daily_user_transaction_summaries")}
		FULL JOIN ${ref("stg_daily_user_engagement")} USING (day, user_key)

	GROUP BY _user_key, month

), previous_calendar_months AS (

	SELECT *

	FROM calendar_months FULL JOIN (
		SELECT _user_key,
			DATE_ADD(month, INTERVAL 1 MONTH) AS month,
			transacted_this_month AS transacted_last_month,
			opened_app_this_month AS opened_app_last_month,
		FROM calendar_months
	) USING (_user_key, month)

)

SELECT day, user_key,
	COALESCE(number_of_transactions, 0) AS number_of_transactions,
	COALESCE(usd_user_volume, 0) AS usd_user_volume,
	COALESCE(usd_bank_volume, 0) AS usd_bank_volume,
	COALESCE(usd_onchain_fee_revenue, 0) AS usd_onchain_fee_revenue,
	COALESCE(usd_stablesats_spread_revenue, 0) AS usd_stablesats_spread_revenue,
	COALESCE(sat_onchain_fee_revenue, 0) AS sat_onchain_fee_revenue,
	COALESCE(sat_stablesats_spread_revenue, 0) AS sat_stablesats_spread_revenue,
	COALESCE(engagement_time_sec, 0) AS engagement_time_sec,
	COALESCE(sat_bitcoin_close_balance, 0) AS sat_bitcoin_close_balance,
	COALESCE(sat_bitcoin_close_balance / spot_close_sats_per_cent / 100, 0) AS usd_bitcoin_close_balance,
	COALESCE(cent_stablesats_close_balance * spot_close_sats_per_cent, 0) AS sat_stablesats_close_balance,
	COALESCE(cent_stablesats_close_balance / 100, 0) AS usd_stablesats_close_balance,
	usd_stablesats_funding_revenue,
	SUM(usd_stablesats_funding_revenue) OVER (c) AS lifetime_usd_stablesats_funding_revenue,
	DATE_DIFF(day, created_at, DAY) AS days_since_created_at,
	DATE_DIFF(day, created_at, WEEK) AS weeks_since_created_at,
	DATE_DIFF(day, created_at, MONTH) AS months_since_created_at,
	DATE_DIFF(
		day,
		MIN(IF(number_of_transactions > 0, day, NULL)) OVER (PARTITION BY user_key),
		DAY
	) AS days_since_first_transaction,
	DATE_DIFF(
		day,
		LAST_VALUE(IF(number_of_transactions> 0, day, NULL) IGNORE NULLS) OVER (
			PARTITION BY user_key
			ORDER BY day
			ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING),
		DAY
	) AS days_since_last_transaction,
	COALESCE(number_of_transactions > 0, FALSE) AS transacted,
	COALESCE(SUM(number_of_transactions) OVER (w) > 0, FALSE) AS transacted_past_7_days,
	COALESCE(SUM(number_of_transactions) OVER (m) > 0, FALSE) AS transacted_past_30_days,
	COALESCE(SUM(number_of_transactions) OVER (q) > 0, FALSE) AS transacted_past_90_days,
	COALESCE(LEAD(number_of_transactions) OVER (u) > 0, FALSE) AS will_transact_tomorrow,
	COALESCE(engagement_time_sec > 0, FALSE) AS opened_app,
	COALESCE(SUM(engagement_time_sec) OVER (w) > 0, FALSE) AS opened_app_past_7_days,
	COALESCE(SUM(engagement_time_sec) OVER (m) > 0, FALSE) AS opened_app_past_30_days,
	COALESCE(SUM(engagement_time_sec) OVER (q) > 0, FALSE) AS opened_app_past_90_days,
	COALESCE(LAG(number_of_transactions, 7, 0) OVER(u) > 0, FALSE) AS transacted_7_days_ago,
	COALESCE(LAG(number_of_transactions, 30, 0) OVER(u) > 0, FALSE) AS transacted_30_days_ago,
	COALESCE(LAG(number_of_transactions, 90, 0) OVER(u) > 0, FALSE) AS transacted_90_days_ago,
	COALESCE(SUM(number_of_transactions) OVER (lw) > 0, FALSE) AS transacted_past_7_days_7_days_ago,
	COALESCE(SUM(number_of_transactions) OVER (lm) > 0, FALSE) AS transacted_past_30_days_30_days_ago,
	COALESCE(SUM(number_of_transactions) OVER (lq) > 0, FALSE) AS transacted_past_90_days_90_days_ago,
	COALESCE(number_onboarded, 0) AS number_of_users_onboarded,
	COALESCE(SUM(number_onboarded) OVER (w), 0) AS onboarded_past_7_days,
	COALESCE(SUM(number_onboarded) OVER (m), 0) AS onboarded_past_30_days,
	COALESCE(SUM(number_onboarded) OVER (q), 0) AS onboarded_past_90_days,
	COALESCE(SUM(usd_onchain_fee_revenue) OVER (c), 0) AS lifetime_usd_onchain_fee_revenue,
	COALESCE(SUM(usd_stablesats_spread_revenue) OVER (c), 0) AS lifetime_usd_stablesats_spread_revenue,
	COALESCE(SUM(sat_onchain_fee_revenue) OVER (c), 0) AS lifetime_sat_onchain_fee_revenue,
	COALESCE(SUM(sat_stablesats_spread_revenue) OVER (c), 0) AS lifetime_sat_stablesats_spread_revenue,
	DATE_TRUNC(DATE(created_at), MONTH) = DATE_TRUNC(day, MONTH) AS created_this_month,
	DATE_TRUNC(DATE(created_at), MONTH) = DATE_SUB(DATE_TRUNC(day, MONTH), INTERVAL 1 MONTH) AS created_last_month,
	COALESCE(transacted_this_month, FALSE) AS transacted_this_month,
	COALESCE(transacted_last_month, FALSE) AS transacted_last_month,
	COALESCE(opened_app_this_month, FALSE) AS opened_app_this_month,
	COALESCE(opened_app_last_month, FALSE) AS opened_app_last_month,
	COALESCE(SUM(engagement_time_sec) OVER (qq) > 0, FALSE) AS opened_app_past_180_days,
	EXTRACT(DAYOFWEEK FROM day) AS day_of_week,
	DATE_ADD(day, INTERVAL 2 DAY) AS day_import_lag_shifted,
	env_specific.* EXCEPT(day, user_key),

FROM ${ref("stg_daily_user_funding_revenue")}
	LEFT JOIN ${ref("stg_daily_user_transaction_summaries")} USING (day, user_key)
	LEFT JOIN ${ref("stg_daily_user_engagement")} USING (day, user_key)
	LEFT JOIN onboarding USING (day, user_key)
	LEFT JOIN previous_calendar_months
		ON DATE_TRUNC(day, MONTH) = month
		AND user_key = _user_key
	LEFT JOIN ${ref("stg_env_daily_user_facts")} AS env_specific USING (day, user_key)

WINDOW w AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 7 PRECEDING AND CURRENT ROW
), m AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 30 PRECEDING AND CURRENT ROW
), q AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 90 PRECEDING AND CURRENT ROW
), qq AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 180 PRECEDING AND CURRENT ROW
), lw AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 14 PRECEDING AND 7 PRECEDING
), lm AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 60 PRECEDING AND 30 PRECEDING
), lq AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN 180 PRECEDING AND 90 PRECEDING
), c AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
), u AS (
	PARTITION BY user_key
	ORDER BY day
)
