config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current,
		},
	},
}

WITH user_trades AS (

	SELECT
		DATE(recorded_at) as day,
		SUM(dealer_sat_deposit) AS dealer_sat_deposit,
		SUM(dealer_cent_deposit) AS dealer_cent_deposit,
		SUM(sat_user_pnl) AS sat_user_pnl,
		SUM(sat_stablesats_spread) AS sat_stablesats_spread

	FROM ${ref("stg_trade_spreads")}
		LEFT JOIN ${ref("stg_user_pnl")} USING (journal_id)

	GROUP BY day

), funding AS (

	SELECT
		DATE(recorded_at) as day,
		SUM(sat_funding_pnl) AS sat_funding_pnl

	FROM ${ref("stg_stablesats_exchange_funding_fees")}

	GROUP BY day

), trades AS (

	SELECT
		DATE(recorded_at) as day,
		SUM(sat_fee) AS sat_trade_fee,
		SUM(sat_pnl) AS sat_trade_pnl,
		SUM(hedge_cent_deposit) AS hedge_cent_deposit,
		ARRAY_AGG(avg_open_price ORDER BY recorded_at DESC LIMIT 1)[SAFE_ORDINAL(1)] as avg_open_sats_per_cent

	FROM ${ref("stg_stablesats_exchange_trades")}
		LEFT JOIN ${ref("stg_stablesats_exchange_avg_open_prices")} USING (bill_id)

	GROUP BY day

), mishedge AS (

	SELECT
		DATE(recorded_at) as day,
		SUM(sat_long_pnl) as sat_long_pnl,
		SUM(sat_short_pnl) as sat_short_pnl

	FROM ${ref("stg_stablesats_mishedge_pnl")}

	GROUP BY day

), stablesats_internal AS (

	SELECT
		DATE(recorded_at) AS day,
		ANY_VALUE(usd_balance HAVING MAX recorded_at)*100 AS stablesats_cent_balance,
		ANY_VALUE(btc_balance HAVING MAX recorded_at)********** AS stablesats_sat_balance

	FROM ${ref("stg_stablesats_internal_liability_unified")}

	GROUP BY day

), dealer_account AS (

	SELECT
		DATE(recorded_at) as day,
		SUM(IF(currency="BTC", deposit, 0)) as dealer_account_sat_deposit,
		SUM(IF(currency="USD", deposit, 0)) as dealer_account_cent_deposit

	FROM ${ref("stg_journal_entries")}
		LEFT JOIN ${ref("stg_static_wallets")} using (wallet_id, currency)

	WHERE account_name = "dealer"

	GROUP BY day

), exchange_account AS (

	SELECT
		DATE(TIMESTAMP_MILLIS(timestamp)) as day,
		ANY_VALUE(trading_btc_total_balance + funding_btc_total_balance HAVING MAX timestamp)********** AS sat_exchange_balance

	FROM ${ref({
			name: "okex_balances",
			schema: envs.currentSchema("_functions_raw")
		})}

	GROUP BY day

)

SELECT day,
	dealer_sat_deposit,
	dealer_cent_deposit,
	sat_user_pnl,
	sat_stablesats_spread,
	sat_funding_pnl,
	sat_trade_fee,
	sat_trade_pnl,
	hedge_cent_deposit,
	sat_long_pnl,
	sat_short_pnl,
	SUM(dealer_sat_deposit) OVER (w) AS dealer_sat_balance,
	SUM(dealer_cent_deposit) OVER (w) AS dealer_cent_balance,
	SUM(sat_user_pnl) OVER (w) AS sat_user_pnl_balance,
	SUM(sat_stablesats_spread) OVER (w) AS sat_stablesats_spread_balance,
	SUM(sat_funding_pnl) OVER (w) AS sat_funding_balance,
	SUM(sat_trade_fee) OVER (w) AS sat_trade_fee_balance,
	SUM(sat_trade_pnl) OVER (w) AS sat_trade_balance,
	SUM(hedge_cent_deposit) OVER (w) AS hedge_cent_balance,
	SUM(sat_long_pnl) OVER (w) AS sat_long_mishedge_balance,
	SUM(sat_short_pnl) OVER (w) AS sat_short_mishedge_balance,
	swap_buy_open_sats_per_cent,
	swap_buy_close_sats_per_cent,
	LAST_VALUE(avg_open_sats_per_cent IGNORE NULLS) OVER (w) AS avg_open_sats_per_cent,
	stablesats_sat_balance,
	stablesats_cent_balance,
	dealer_account_sat_deposit,
	dealer_account_cent_deposit,
	SUM(dealer_account_sat_deposit) OVER (w) AS dealer_account_sat_balance,
	SUM(dealer_account_cent_deposit) OVER (w) AS dealer_account_cent_balance,
	sat_exchange_balance

FROM ${ref("stg_days")}
	LEFT JOIN user_trades USING(day)
	LEFT JOIN funding USING(day)
	LEFT JOIN trades USING(day)
	LEFT JOIN mishedge USING(day)
	LEFT JOIN stablesats_internal USING(day)
	LEFT JOIN dealer_account USING(day)
	LEFT JOIN exchange_account USING(day)

WHERE day < CURRENT_DATE()

WINDOW w AS (
	ORDER BY day
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
