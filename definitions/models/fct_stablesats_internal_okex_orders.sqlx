config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Stablesats okex orders",
}

SELECT
  TIMESTAMP_TRUNC(created_at, MINUTE, "UTC") AS minute,
  CASE WHEN action = 'buy' THEN size ELSE -size END AS nb_contracts,
  action,
  CONCAT(lost, '_', state) AS lost_state
FROM ${ref("stg_stablesats_internal_okex_orders_unified")}
WHERE action IN ('buy', 'sell')
