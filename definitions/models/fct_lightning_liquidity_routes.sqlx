config {
	type: "table",
	assertions: {
		nonNull: ["payment_hash"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH user_payments AS (

	SELECT DISTINCT
		transaction_hash

	FROM ${ref("stg_user_wallet_transactions")}

	WHERE settlement_method = "Lightning"
		AND bank_direction = "Outbound"
		AND NOT voider

), confirmed_attempts AS (

	SELECT payment_hash, sent_from_pubkey,
		payments.created_at,
		transaction_hash IS NOT NULL AS is_user_payment,
		confirmed_details.destination,
		attempt.confirmed_at,
		attempt.route.hops,
		ROW_NUMBER() OVER () AS attempt_id,

	FROM ${ref("stg_lightning_payments")} AS payments
		LEFT JOIN user_payments ON payment_hash=transaction_hash
		CROSS JOIN UNNEST(attempts) AS attempt


	WHERE payment_status = "settled"
		AND attempt.is_confirmed
		AND NOT attempt.is_failed
		AND NOT attempt.is_pending

)

SELECT payment_hash, created_at, confirmed_at, is_user_payment, attempt_id,
	ARRAY_LENGTH(hops) AS number_of_hops,
	hop_number,
	CAST(hop.fee_mtokens AS INT64) AS msat_fee,
	CAST(hop.forward_mtokens AS INT64) AS msat_amount,
	public_key,
	COALESCE(alias, SUBSTR(public_key, 1, 12)) AS alias,
	LEAD(public_key) OVER (
		PARTITION BY attempt_id
		ORDER BY hop_number
	) AS next_public_key,
	LEAD(COALESCE(alias, SUBSTR(public_key, 1, 12))) OVER (
		PARTITION BY attempt_id
		ORDER BY hop_number
	) AS next_alias,

FROM confirmed_attempts
	CROSS JOIN UNNEST(hops) AS hop WITH OFFSET AS hop_number
	LEFT JOIN ${ref("stg_public_lightning_nodes")} ON public_key = pubkey
