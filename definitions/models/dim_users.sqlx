config {
	type: "table",
	assertions: {
		uniqueKey: ["user_key"],
		nonNull: ["user_key"],
	},
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		}
	},
}


WITH clusters AS (

	SELECT user_key, centroid_id

	FROM ${ref("stg_ml_user_clustering_predictions")}

	WHERE centroid_distance_rank < 0.99
		AND overall_distance_rank < 0.99

), api_keys AS (

	SELECT kratos_user_id,
		MIN(api_id_created_at) AS api_since,
		COUNT(DISTINCT key_id) AS live_keys,
		LOGICAL_OR(read_only) AS live_read_only_key,

	FROM ${ref("stg_apikeys")}

	WHERE NOT revoked

	GROUP BY kratos_user_id

), engagement AS (

	SELECT user_key,
		COUNT(DISTINCT day) AS days_engaged,
		SUM(engagement_time_sec) AS engagement_time_sec,

	FROM ${ref("stg_daily_user_engagement")}

	GROUP BY user_key

)

SELECT
	created_at,
	merchant,
	phone_number_country_name,
	phone_number_country_code,
	COALESCE(days_transacting, 0) AS days_transacting,
	COALESCE(weeks_transacting, 0) AS weeks_transacting,
	COALESCE(months_transacting, 0) AS months_transacting,
	COALESCE(usd_user_volume, 0) AS lifetime_usd_user_volume,
	COALESCE(usd_stablesats_spread_revenue, 0) AS lifetime_usd_stablesats_spread_revenue,
	COALESCE(usd_onchain_fee_revenue, 0) AS lifetime_usd_onchain_fee_revenue,
	COALESCE(sat_stablesats_spread_revenue, 0) AS lifetime_sat_stablesats_spread_revenue,
	COALESCE(sat_onchain_fee_revenue, 0) AS lifetime_sat_onchain_fee_revenue,
	COALESCE(number_of_transactions, 0) AS lifetime_number_of_transactions,
	COALESCE(number_of_intraledger_counter_parties, 0) AS lifetime_number_of_intraledger_counter_parties,
	COALESCE(number_of_quiz_rewards > 0 AND number_of_outbound_transactions > 0 AND number_of_inbound_transactions = 0, FALSE) AS is_quiz_reward_farmer,
	usd_stablesats_balance,
	usd_bitcoin_balance,
	CASE
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) IS NULL THEN "Never transacted"
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) <= 1 THEN "Less than $1"
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) < 10 THEN "Between $1 and $10"
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) < 100 THEN "Between $10 and $100"
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) < 1000 THEN "Between $100 and $1,000"
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) < 10000 THEN "Between $1,000 and $10,000"
		WHEN SAFE_DIVIDE(usd_user_volume, weeks_transacting) < 100000 THEN "Between $10,000 and $100,000"
		ELSE "More than $100,000"
	END AS avg_weekly_usd_user_volume_bin,
	CASE
		WHEN days_transacting IS NULL THEN "Never transacted"
		WHEN days_transacting <= 1 THEN "1 day or less"
		WHEN days_transacting <= 7 THEN "Between 1 day and 1 week"
		WHEN days_transacting <= 30 THEN "Between 1 week and 1 month"
		WHEN days_transacting <= 90 THEN "Between 1 and 3 months"
		WHEN days_transacting <= 183 THEN "Between 3 and 6 months"
		WHEN days_transacting <= 365 THEN "Between 6 months and 1 year"
		ELSE "More than 1 year"
	END AS time_transacting_bin,
	CASE
		WHEN SAFE_DIVIDE(number_of_intraledger_counter_parties, weeks_transacting) IS NULL THEN "Never transacted"
		WHEN SAFE_DIVIDE(number_of_intraledger_counter_parties, weeks_transacting) = 0 THEN "0"
		WHEN SAFE_DIVIDE(number_of_intraledger_counter_parties, weeks_transacting) = 1 THEN "1"
		WHEN SAFE_DIVIDE(number_of_intraledger_counter_parties, weeks_transacting) <= 3 THEN "Between 1 and 3"
		WHEN SAFE_DIVIDE(number_of_intraledger_counter_parties, weeks_transacting) <= 6 THEN "Between 3 and 6"
		WHEN SAFE_DIVIDE(number_of_intraledger_counter_parties, weeks_transacting) <= 12 THEN "Between 6 and 12"
		ELSE "More than 12"
	END AS avg_weekly_number_of_intraledger_counter_parties_bin,
	CASE
		WHEN usd_stablesats_balance IS NULL THEN "Never transacted"
		WHEN usd_stablesats_balance < 0.1 THEN "Less than $0.10"
		WHEN usd_stablesats_balance < 1 THEN "Between $0.10 and $1"
		WHEN usd_stablesats_balance < 10 THEN "Between $1 and $10"
		WHEN usd_stablesats_balance < 100 THEN "Between $10 and $100"
		WHEN usd_stablesats_balance < 1000 THEN "Between $100 and $1,000"
		WHEN usd_stablesats_balance < 10000 THEN "Between $1,000 and $10,000"
		ELSE "More than $10,000"
	END AS usd_stablesats_balance_bin,
	CASE
		WHEN usd_bitcoin_balance IS NULL THEN "Never transacted"
		WHEN usd_bitcoin_balance < 0.1 THEN "Less than $0.10"
		WHEN usd_bitcoin_balance < 1 THEN "Between $0.10 and $1"
		WHEN usd_bitcoin_balance < 10 THEN "Between $1 and $10"
		WHEN usd_bitcoin_balance < 100 THEN "Between $10 and $100"
		WHEN usd_bitcoin_balance < 1000 THEN "Between $100 and $1,000"
		WHEN usd_bitcoin_balance < 10000 THEN "Between $1,000 and $10,000"
		ELSE "More than $10,000"
	END AS usd_bitcoin_balance_bin,
	CASE
		WHEN COALESCE(usd_bitcoin_balance, usd_stablesats_balance) IS NULL THEN "Never transacted"
		WHEN (usd_bitcoin_balance + usd_stablesats_balance)  < 0.1 THEN "Less than $0.10"
		WHEN (usd_bitcoin_balance + usd_stablesats_balance) < 1 THEN "Between $0.10 and $1"
		WHEN (usd_bitcoin_balance + usd_stablesats_balance) < 10 THEN "Between $1 and $10"
		WHEN (usd_bitcoin_balance + usd_stablesats_balance) < 100 THEN "Between $10 and $100"
		WHEN (usd_bitcoin_balance + usd_stablesats_balance) < 1000 THEN "Between $100 and $1,000"
		WHEN (usd_bitcoin_balance + usd_stablesats_balance) < 10000 THEN "Between $1,000 and $10,000"
		ELSE "More than $10,000"
	END AS usd_total_balance_bin,
	display_currency,
	currency as default_wallet_currency,
	COALESCE(centroid_id, 0) AS cluster_id,
	CASE
		WHEN created_at BETWEEN "2023-05-08 20:00:00 UTC" AND "2023-05-18 19:00:00 UTC"
		THEN "Random default wallet currency"
	END AS experiment,
	CASE
		WHEN created_at BETWEEN "2023-05-08 20:00:00 UTC" AND "2023-05-18 19:00:00 UTC"
			AND RIGHT(kratos_user_id, 1) IN ('0','2','4','6','8','a','c','e')
		THEN "BTC"
		WHEN created_at BETWEEN "2023-05-08 20:00:00 UTC" AND "2023-05-18 19:00:00 UTC"
			AND RIGHT(kratos_user_id, 1) NOT IN ('0','2','4','6','8','a','c','e')
		THEN "USD"
	END AS experiment_group,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_brand, "New account")
		ELSE COALESCE(device_brand, "No GA data")
	END AS device_brand,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_model, "New account")
		ELSE COALESCE(device_model, "No GA data")
	END AS device_model,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_os, "New account")
		ELSE COALESCE(device_os, "No GA data")
	END AS device_os,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_os_version, "New account")
		ELSE COALESCE(device_os_version, "No GA data")
	END AS device_os_version,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_language, "New account")
		ELSE COALESCE(device_language, "No GA data")
	END AS device_language,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_continent, "New account")
		ELSE COALESCE(device_continent, "No GA data")
	END AS device_continent,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_sub_continent, "New account")
		ELSE COALESCE(device_sub_continent, "No GA data")
	END AS device_sub_continent,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_country, "New account")
		ELSE COALESCE(device_country, "No GA data")
	END AS device_country,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_city, "New account")
		ELSE COALESCE(device_city, "No GA data")
	END AS device_city,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_metro, "New account")
		ELSE COALESCE(device_metro, "No GA data")
	END AS device_metro,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(app_version, "New account")
		ELSE COALESCE(app_version, "No GA data")
	END AS app_version,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(app_install_source, "New account")
		ELSE COALESCE(app_install_source, "No GA data")
	END AS app_install_source,
	level,
	--deleted_phone OR deleted_device_id IS NOT NULL AS deleted,
	status,
	COALESCE(number_of_transactions > 0, FALSE) AS has_transacted,
	shares_device_token_with,
	CASE
		WHEN phone_set IS NULL OR email_set IS NULL THEN "Unknown"
		WHEN phone_set AND NOT email_set THEN "Phone"
		WHEN phone_set AND email_set THEN "Phone or email"
		WHEN NOT phone_set AND email_set THEN "Email"
		WHEN level = 1 THEN "Phone"
		WHEN level = 0 THEN "Device"
		ELSE "Unknown"
	END AS authentication,
	api_since,
	live_keys,
	live_read_only_key,
	COALESCE(usd_stablesats_funding_revenue, 0) AS lifetime_usd_stablesats_funding_revenue,
	COALESCE(number_of_quiz_rewards, 0) AS lifetime_number_of_quiz_rewards,
	COALESCE(used_stablesats, FALSE) AS used_stablesats,
	COALESCE(first_settlement_method, "Never transacted") AS first_settlement_method,
	COALESCE(first_usd_user_volume, 0) AS first_usd_user_volume,
	CASE
		WHEN level = 0 OR device_id IS NOT NULL
		THEN 0
		ELSE 1
	END AS initial_level,
	COALESCE(days_engaged, 0) AS days_engaged,
	COALESCE(engagement_time_sec, 0) AS engagement_time_sec,
	CASE
		WHEN days_engaged IS NULL THEN "No GA data"
		WHEN days_engaged <= 1 THEN "1 day or less"
		WHEN days_engaged <= 7 THEN "Between 1 day and 1 week"
		WHEN days_engaged <= 30 THEN "Between 1 week and 1 month"
		WHEN days_engaged <= 90 THEN "Between 1 and 3 months"
		WHEN days_engaged <= 183 THEN "Between 3 and 6 months"
		WHEN days_engaged <= 365 THEN "Between 6 months and 1 year"
		ELSE "More than 1 year"
	END AS time_engaged_bin,
	COALESCE(attribution_medium, "Unknown") AS attribution_medium,
	COALESCE(attribution_source, "Unknown") AS attribution_source,
	COALESCE(attribution_term, "Unknown") AS attribution_term,
	COALESCE(attribution_campaign, "Unknown") AS attribution_campaign,
	COALESCE(attribution_content, "Unknown") AS attribution_content,
	totp,
	env_specific.*

FROM ${ref("stg_user_accounts")} AS stg_user_accounts
	LEFT JOIN ${ref("stg_user_transaction_summaries")} USING (user_key)
	LEFT JOIN clusters USING (user_key)
	LEFT JOIN ${ref("stg_user_app_info")} USING (user_key)
	LEFT JOIN ${ref("stg_shared_device_tokens")} USING (user_key)
	LEFT JOIN ${ref("stg_solette_accounts")} AS solette_accounts USING (account_id)
	LEFT JOIN ${ref("report_solette_counter_parties")} solette_counter_parties USING (user_key)
	LEFT JOIN ${ref("stg_env_user_dimensions")} AS env_specific USING (user_key)
	LEFT JOIN (
		SELECT kratos_user_id, email_set, phone_set,
		FROM ${ref("stg_kratos_identities")}
	) USING (kratos_user_id)
	LEFT JOIN ${ref("stg_wallets")} ON default_wallet_id = wallet_id
	LEFT JOIN api_keys USING (kratos_user_id)
	LEFT JOIN engagement USING (user_key)
	LEFT JOIN ${ref("stg_user_attribution")} USING (user_key)
