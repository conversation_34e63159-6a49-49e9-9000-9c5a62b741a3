config {
	type: "table",
	assertions: {
		nonNull: ["user_key","event_name","valid_from","valid_to"],
	},
	bigquery: {
		clusterBy: ["valid_from"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH scd AS (

	SELECT * REPLACE(
		COALESCE(scd_valid_to, CURRENT_TIMESTAMP()) AS scd_valid_to
	)

	FROM ${ref("stg_accounts_scd")}


), usernames AS (

	SELECT user_id,
		"Set username" AS event_name,
		CAST(NULL AS STRING) AS event_value,
		MIN(scd_valid_from) AS valid_from,
		CURRENT_TIMESTAMP() AS valid_to

	FROM scd

	WHERE username_set

	GROUP BY user_id

), levels AS (

	SELECT
		user_id,
		"Level" AS event_name,
		CAST(level AS STRING) AS event_value,
		COALESCE(IF(level=0, ANY_VALUE(created_at), NULL), MIN(scd_valid_from)) AS valid_from,
		MAX(scd_valid_to) AS valid_to

	FROM scd

	GROUP BY user_id, level

), added_phone AS (

	SELECT user_id,
		"Set phone number to upgrade" AS event_name,
		CAST(NULL AS STRING) AS event_value,
		MIN(scd_valid_from) as valid_from,
		CURRENT_TIMESTAMP() AS valid_to

	FROM scd

	GROUP BY user_id

	HAVING MIN(level) = 0 AND MAX(level) > 0

), default_wallet_currency_changes AS (

	SELECT user_id, updated_at, scd_valid_from, scd_valid_to, default_wallet_currency,
		CASE
			WHEN default_wallet_currency = LAG(default_wallet_currency) OVER (
				PARTITION BY user_id
				ORDER BY updated_at
			) THEN 0
			ELSE 1
		END AS changed

	FROM scd

), default_wallet_currency_groups AS (

	SELECT user_id, updated_at, scd_valid_from, scd_valid_to, default_wallet_currency,
		SUM(changed) OVER (
			PARTITION BY user_id
			ORDER BY updated_at
		) AS grp

	FROM default_wallet_currency_changes

), default_wallet_currencies AS (

	SELECT user_id,
		"Set default wallet currency" AS event_name,
		ANY_VALUE(default_wallet_currency) AS event_value,
		MIN(scd_valid_from) AS valid_from,
		MAX(scd_valid_to) AS valid_to

	FROM default_wallet_currency_groups

	WHERE ( grp != 1 OR default_wallet_currency != "BTC" )

	GROUP BY user_id, grp

), display_currency_changes AS (

	SELECT user_id, updated_at, scd_valid_from, scd_valid_to, display_currency,
		CASE
			WHEN display_currency = LAG(display_currency) OVER (
				PARTITION BY user_id
				ORDER BY updated_at
			) THEN 0
			ELSE 1
		END AS changed

	FROM scd

), display_currency_groups AS (

	SELECT user_id, updated_at, scd_valid_from, scd_valid_to, display_currency,
		SUM(changed) OVER (
			PARTITION BY user_id
			ORDER BY updated_at
		) AS grp

	FROM display_currency_changes

), display_currencies AS (

	SELECT user_id,
		"Set display currency" AS event_name,
		ANY_VALUE(display_currency) AS event_value,
		MIN(scd_valid_from) AS valid_from,
		MAX(scd_valid_to) AS valid_to

	FROM display_currency_groups

	WHERE ( grp != 1 OR display_currency != "USD" )

	GROUP BY user_id, grp

), language_changes AS (

	SELECT user_id, updated_at, scd_valid_from, scd_valid_to, language,
		CASE
			WHEN language = LAG(language) OVER (
				PARTITION BY user_id
				ORDER BY updated_at
			) THEN 0
			ELSE 1
		END AS changed

	FROM scd

), language_groups AS (

	SELECT user_id, updated_at, scd_valid_from, scd_valid_to, language,
		SUM(changed) OVER (
			PARTITION BY user_id
			ORDER BY updated_at
		) AS grp

	FROM language_changes

), languages AS (

	SELECT user_id,
		"Set language" AS event_name,
		ANY_VALUE(language) AS event_value,
		MIN(scd_valid_from) AS valid_from,
		MAX(scd_valid_to) AS valid_to

	FROM language_groups

	WHERE ( grp != 1 )

	GROUP BY user_id, grp

), unified AS (

	SELECT * FROM usernames
	UNION ALL
	SELECT * FROM levels
	UNION ALL
	SELECT * FROM default_wallet_currencies
	UNION ALL
	SELECT * FROM display_currencies
	UNION ALL
	SELECT * FROM languages

)

SELECT user_key, valid_from, valid_to, event_name, event_value,
	DATE_DIFF(valid_to, valid_from, DAY) AS days_valid

FROM unified
	JOIN ${ref("stg_user_accounts")} using (USER_ID)
