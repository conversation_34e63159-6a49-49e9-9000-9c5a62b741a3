config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH transaction_summaries AS (

	SELECT user_key,
		SUM(usd_user_volume) AS usd_user_volume_30_day,
		SUM(usd_inbound_user_volume) AS usd_inbound_user_volume_30_day,
		SUM(usd_outbound_user_volume) AS usd_outbound_user_volume_30_day,
		SUM(usd_stablesats_spread_revenue) AS usd_stablesats_spread_revenue_30_day,
		SUM(usd_onchain_fee_revenue) AS usd_onchain_fee_revenue_30_day,
		SUM(number_of_transactions) AS transactions_30_day,
		ARRAY_CONCAT_AGG(intraledger_counter_parties) AS intraledger_counter_parties_30_day,

	FROM ${ref("stg_daily_user_transaction_summaries")}
		JOIN ${ref("stg_user_accounts")} USING (user_key)

	WHERE day >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
		AND merchant

	GROUP BY user_key

)

SELECT * EXCEPT(user_key)

FROM (
	SELECT
		ROW_NUMBER() OVER () AS merchant_key,
		merchant_name,
		store_name,
		created_at,
		phone_number_country_name,
		phone_number_country_code,
		store_latitude,
		store_longitude,
		usd_user_volume_30_day,
		usd_stablesats_spread_revenue_30_day,
		usd_onchain_fee_revenue_30_day,
		transactions_30_day,
		(
			SELECT COUNT(DISTINCT counter_party)
			FROM UNNEST(intraledger_counter_parties_30_day) AS counter_party
		) AS number_of_intraledger_counter_parties_30_day,
		currency AS default_wallet_currency,
		env_specific.*

	FROM ${ref("stg_account_merchants")}
		LEFT JOIN ${ref("stg_user_accounts")} USING (account_id)
		LEFT JOIN transaction_summaries AS txs USING (user_key)
		LEFT JOIN ${ref("stg_env_user_dimensions")} AS env_specific USING (user_key)
		LEFT JOIN ${ref("stg_user_wallets")} ON default_wallet_id = wallet_id

)
