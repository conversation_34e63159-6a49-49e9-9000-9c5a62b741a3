config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT "LATAM" AS region, "Argentina" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Bolivia" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Brazil" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Chile" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Colombia" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Costa Rica" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Cuba" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Dominican Republic" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Ecuador" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "El Salvador" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "French Guiana" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Guadeloupe" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Guatemala" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Haiti" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Honduras" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Martinique" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Mexico" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Nicaragua" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Panama" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Paraguay" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Peru" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Puerto Rico" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Saint Barthelemy" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Saint Martin" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Uruguay" AS country_name, UNION DISTINCT
SELECT "LATAM" AS region, "Venezuela" AS country_name, UNION DISTINCT

SELECT "Africa" AS region, "Nigeria" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Ethiopia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Egypt" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Democratic Republic of the Congo" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Tanzania" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "South Africa" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Kenya" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Uganda" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Sudan" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Algeria" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Morocco" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Angola" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Ghana" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Mozambique" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Madagascar" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Ivory Coast" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Cameroon" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Niger" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Mali" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Burkina Faso" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Malawi" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Zambia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Chad" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Somalia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Senegal" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Zimbabwe" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Guinea" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Rwanda" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Benin" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Burundi" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Tunisia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "South Sudan" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Togo" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Sierra Leone" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Libya" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Republic of the Congo" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Central African Republic" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Liberia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Mauritania" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Eritrea" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Gambia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Botswana" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Namibia" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Gabon" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Lesotho" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Guinea-Bissau" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Equatorial Guinea" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Mauritius" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Eswatini" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Djibouti" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Comoros" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Cape Verde" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Western Sahara" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Sao Tome and Principe" AS country_name, UNION DISTINCT
SELECT "Africa" AS region, "Seychelles" AS country_name,
