config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH pnl_explain_sum AS (

  SELECT
    minute,

    funding_fee_sats,
    trading_fee_sats,
    trading_pnl_sats,
    user_pnl_sats,
    deposit_fee_sats,
    withdrawal_fee_sats,

    funding_fee_mishedge_explain_sats,
    funding_fee_residual_explain_sats,
    trading_fee_mishedge_explain_sats,
    trading_fee_residual_explain_sats,
    trading_pnl_mishedge_explain_sats,
    trading_pnl_slippage_explain_sats,
    trading_pnl_residual_explain_sats,
    net_pnl_user_fees_explain_sats,
    net_pnl_unexplain_sats,

    net_pnl_sats,

    SUM(funding_fee_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS funding_fee_sats_cumsum,
    SUM(trading_fee_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_fee_sats_cumsum,
    SUM(trading_pnl_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_pnl_sats_cumsum,
    SUM(user_pnl_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS user_pnl_sats_cumsum,
    SUM(deposit_fee_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS deposit_fee_sats_cumsum,
    SUM(withdrawal_fee_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS withdrawal_fee_sats_cumsum,

    SUM(funding_fee_mishedge_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS funding_fee_mishedge_explain_sats_cumsum,
    SUM(funding_fee_residual_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS funding_fee_residual_explain_sats_cumsum,
    SUM(trading_fee_mishedge_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_fee_mishedge_explain_sats_cumsum,
    SUM(trading_fee_residual_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_fee_residual_explain_sats_cumsum,
    SUM(trading_pnl_mishedge_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_pnl_mishedge_explain_sats_cumsum,
    SUM(trading_pnl_slippage_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_pnl_slippage_explain_sats_cumsum,
    SUM(trading_pnl_residual_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trading_pnl_residual_explain_sats_cumsum,
    SUM(net_pnl_user_fees_explain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS net_pnl_user_fees_explain_sats_cumsum,
    SUM(net_pnl_unexplain_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS net_pnl_unexplain_sats_cumsum,

    SUM(net_pnl_sats) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS net_pnl_sats_cumsum,

    usd_per_btc_mid_price,
    exposure_usd,
    liability_usd,
    exposure_liability_ratio,
  FROM ${ref("stg_pnl_explain")}

)

SELECT
  minute,

  funding_fee_sats,
  trading_fee_sats,
  trading_pnl_sats,
  user_pnl_sats,
  deposit_fee_sats,
  withdrawal_fee_sats,

  funding_fee_sats_cumsum,
  trading_fee_sats_cumsum,
  trading_pnl_sats_cumsum,
  user_pnl_sats_cumsum,
  deposit_fee_sats_cumsum,
  withdrawal_fee_sats_cumsum,

  funding_fee_mishedge_explain_sats,
  funding_fee_residual_explain_sats,
  trading_fee_mishedge_explain_sats,
  trading_fee_residual_explain_sats,
  trading_pnl_mishedge_explain_sats,
  trading_pnl_slippage_explain_sats,
  trading_pnl_residual_explain_sats,
  net_pnl_user_fees_explain_sats,
  net_pnl_unexplain_sats,

  net_pnl_sats,

  funding_fee_mishedge_explain_sats_cumsum,
  funding_fee_residual_explain_sats_cumsum,
  trading_fee_mishedge_explain_sats_cumsum,
  trading_fee_residual_explain_sats_cumsum,
  trading_pnl_mishedge_explain_sats_cumsum,
  trading_pnl_slippage_explain_sats_cumsum,
  trading_pnl_residual_explain_sats_cumsum,
  net_pnl_user_fees_explain_sats_cumsum,
  net_pnl_unexplain_sats_cumsum,

  net_pnl_sats_cumsum,

  usd_per_btc_mid_price,
  exposure_usd,
  liability_usd,
  exposure_liability_ratio,

FROM pnl_explain_sum
