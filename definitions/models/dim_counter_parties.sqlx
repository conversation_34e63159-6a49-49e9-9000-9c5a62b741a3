config {
	type: "table",
	assertions: {
		uniqueKey: ["counter_party_user_key"],
		nonNull: ["counter_party_user_key"],
	},
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		}
	},
}


SELECT
	created_at,
	merchant,
	phone_number_country_name,
	phone_number_country_code,
	env_specific.* EXCEPT(user_key),
	user_key AS counter_party_user_key,

FROM ${ref("stg_user_accounts")} AS stg_user_accounts
	LEFT JOIN ${ref("stg_env_user_dimensions")} AS env_specific USING (user_key)
