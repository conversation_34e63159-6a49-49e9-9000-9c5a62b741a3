config {
	type: "assertion",
	description: "Asserts that stg_wallet_avg_open_prices is well-formed"
}

SELECT journal_id, wallet_id,
	"Duplicate journal_id" AS reason
FROM ${ref("stg_wallet_avg_open_prices")}
GROUP BY journal_id, wallet_id
HAVING COUNT(*) != 1

UNION ALL

SELECT journal_id, wallet_id,
	"Null avg_open_sats_per_cent" AS reason
FROM ${ref("stg_wallet_avg_open_prices")}
WHERE avg_open_sats_per_cent IS NULL

UNION ALL

SELECT journal_id, wallet_id,
	"Missing avg_open_sats_per_cent" AS reason
FROM ${ref("stg_journal_entries")}
	INNER JOIN ${ref("stg_user_wallets")} USING(wallet_id, currency)
	LEFT JOIN ${ref("stg_wallet_avg_open_prices")} USING (journal_id, wallet_id)
WHERE currency = "USD"
	AND recorded_at > "2022-01-01"
	AND wallet_id != (
		SELECT usd_wallet_id
		FROM ${ref("stg_static_accounts")}
		WHERE account_name = "dealer"
		LIMIT 1
	)
	AND deposit != 0
	AND avg_open_sats_per_cent IS NULL
