config {
	type: "assertion",
	disabled: true,
}

WITH last_timestamps AS (

	SELECT MAX(created_at) AS last_timestamp, "bria payouts" as table

	FROM ${ref({
		name: "bria_payouts",
		schema: envs.currentSchema("_bria_raw")
	})}

	UNION ALL

	SELECT MAX(created_at) AS last_timestamp, "api keys" as table

	FROM ${ref({
		name: "identity_api_keys",
		schema: envs.currentSchema("_apikeys_raw")
	})}

	UNION ALL

	SELECT MAX(created_at) AS last_timestamp, "user trades" as table

	FROM ${ref({
		name: "user_trades",
		schema: envs.currentSchema("_stablesats_raw")
	})}

	UNION ALL

	SELECT TIMESTAMP(MAX(created_at)) AS last_timestamp, "kratos identities" as table

	FROM ${ref({
		name: "identities",
		schema: envs.currentSchema("_kratos_raw")
	})}


)

SELECT *

FROM last_timestamps

WHERE last_timestamp < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
	AND "${envs.current}" != "galoy-staging"
