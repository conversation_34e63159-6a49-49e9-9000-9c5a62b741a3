config {
  type: "assertion",
  description: "Checks that there's exactly one entry for the bank owner on every onchain payment transaction."
}

SELECT
  journal_id
FROM
  ${ref("stg_bankowner_journal_entries")} entries
WHERE
  deposit > 0
  AND type = "onchain_payment"
GROUP BY
  journal_id
HAVING
  COUNT(*) != 1

UNION ALL

SELECT
  journal_id
FROM
  ${ref("stg_bankowner_journal_entries")} entries
WHERE
  deposit > 0
  AND type = "onchain_receipt"
GROUP BY
  journal_id
HAVING
  COUNT(*) > 1
