config {
	type: "assertion",
}

SELECT wallet_id, journal_id, avg_open_sats_per_cent,
FROM ${ref("test_wallet_avg_open_prices")}

EXCEPT DISTINCT

(
	SELECT "Trivial: a" AS wallet_id, "Trivial: a1" AS journal_id, 10000.0 AS avg_open_sats_per_cent
	UNION ALL
	SELECT "Trivial: a", "Trivial: a2", 20000.0
	UNION ALL
	SELECT "Trivial: a", "Trivial: a3", 20000.0
	UNION ALL
	SELECT "Trivial: a", "Trivial: a4", 20000.0
	UNION ALL
	SELECT "Transfer: a", "Transfer: a1", 10000.0
	UNION ALL
	SELECT "Transfer: a", "Transfer: ab1", 10000.0
	UNION ALL
	SELECT "Transfer: b", "Transfer: ab1", 10000.0
	UNION ALL
	SELECT "Transfer: b", "Transfer: b1", 10000.0
	UNION ALL
	SELECT "Ordering: a", "Ordering: a1", 10000.0
	UNION ALL
	SELECT "Ordering: a", "Ordering: a2", 20000.0
	UNION ALL
	SELECT "Ordering: a", "Ordering: a3", 30000.0
	UNION ALL
	SELECT "Ordering: b", "Ordering: b1", 10000.0
	UNION ALL
	SELECT "Ordering: b", "Ordering: b2", 20000.0
	UNION ALL
	SELECT "Ordering: b", "Ordering: b3", 30000.0
)
