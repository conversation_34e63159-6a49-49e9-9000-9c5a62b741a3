config {
	type: "test",
	dataset: "stg_user_pnl"
}

input "stg_wallet_avg_open_prices" {
	SELECT *
	FROM dataform_galoy_staging.test_wallet_avg_open_prices
}
input "stg_usd_wallet_states" {
	SELECT *
	FROM dataform_galoy_staging.test_usd_wallet_states
}

SELECT "Trivial: a" AS wallet_id, "Trivial: a3" AS journal_id, -100000000 AS sat_user_pnl
UNION ALL
SELECT "Trivial: a", "Trivial: a4", 100000000
UNION ALL
SELECT "Transfer: b", "Transfer: b1", 5000000
