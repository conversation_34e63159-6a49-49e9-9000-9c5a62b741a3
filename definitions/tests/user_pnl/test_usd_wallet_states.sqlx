config {
	type: "table",
}

-- Trivial
SELECT "Trivial: a" AS wallet_id, TIMESTAMP_SECONDS(1) AS recorded_at, "Trivial: a1" AS journal_id, 10000.0 AS sats_per_cent, 10000 AS deposit, TRUE AS was_trade, 10000 AS balance, CAST(NULL AS STRING) AS from_wallet_id, CAST(NULL AS STRING) AS to_wallet_id,
UNION ALL
SELECT "Trivial: a", TIMESTAMP_SECONDS(2), "Trivial: a2", 30000.0, 10000, TRUE, 20000, NULL, NULL
UNION ALL
SELECT "Trivial: a", TIMESTAMP_SECONDS(3), "Trivial: a3", 30000.0, -10000, TRUE, 10000, NULL, NULL
UNION ALL
SELECT "Trivial: a", TIMESTAMP_SECONDS(4), "Trivial: a4", 10000.0, -10000, TRUE, 0, NULL, NULL

-- Transfer
UNION ALL
SELECT "Transfer: a", TIMESTAMP_SECONDS(1), "Transfer: a1", 10000.0, 10000, TRUE, 10000, NULL, NULL
UNION ALL
SELECT "Transfer: a", TIMESTAMP_SECONDS(2), "Transfer: ab1", NULL, -1000, FALSE, 9000, NULL, "Transfer: b"
UNION ALL
SELECT "Transfer: b", TIMESTAMP_SECONDS(2), "Transfer: ab1", NULL, 1000, FALSE, 1000, "Transfer: a", NULL
UNION ALL
SELECT "Transfer: b", TIMESTAMP_SECONDS(3), "Transfer: b1", 5000.0, -1000, TRUE, 0, NULL, NULL

-- Ordering
UNION ALL
SELECT "Ordering: a", TIMESTAMP_SECONDS(1), "Ordering: a1", 10000.0, 10000, TRUE, 10000, NULL, NULL
UNION ALL
SELECT "Ordering: a", TIMESTAMP_SECONDS(2), "Ordering: a2", 30000.0, 10000, TRUE, 20000, NULL, NULL
UNION ALL
SELECT "Ordering: a", TIMESTAMP_SECONDS(3), "Ordering: a3", 40000.0, 20000, TRUE, 40000, NULL, NULL
UNION ALL
SELECT "Ordering: b", TIMESTAMP_SECONDS(1), "Ordering: b1", 10000.0, 10000, TRUE, 10000, NULL, NULL
UNION ALL
SELECT "Ordering: b", TIMESTAMP_SECONDS(1), "Ordering: b2", 30000.0, 10000, TRUE, 20000, NULL, NULL
UNION ALL
SELECT "Ordering: b", TIMESTAMP_SECONDS(1), "Ordering: b3", 40000.0, 20000, TRUE, 40000, NULL, NULL
