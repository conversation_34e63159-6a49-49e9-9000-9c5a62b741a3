config {
	type: "operations",
	hasOutput: true,
	description: "Parses a bolt11 payment request"
}

CREATE OR REPLACE FUNCTION ${self()} (
	pr STRING
)

RETURNS STRUCT<
	currency STRING,
	amount INT64,
	timestamp INT64,
	payment_hash STRING,
	payment_secret BOOL,
	description STRING,
	additional_metadata BOOL,
	payee STRING,
	description_hash BOOL,
	expiry INT64,
	min_final_cltv_expiry_delta INT64,
	fallback_address BOOL,
	routing_hints ARRAY<
		STRUCT<route ARRAY<STRUCT<
			pubkey STRING,
			short_channel_id STRING,
			fee_proportional_millionths INT64,
			fee_base_msat INT64,
			cltv_expiry_delta INT64
		>>>
	>,
	features STRUCT<
		option_data_loss_protect STRING,
		initial_routing_sync STRING,
		option_upfront_shutdown_script STRING,
		gossip_queries STRING,
		var_onion_optin STRING,
		gossip_queries_ex STRING,
		option_static_remotekey STRING,
		payment_secret STRING,
		basic_mpp STRING,
		option_support_large_channel STRING,
		extra_bits STRUCT<
			start_bit INT64,
			bits ARRAY<BOOL>,
			has_required BOOL
		>
	>,
	unknown_types ARRAY<INT64>
>

LANGUAGE js AS r"""

	const DIVISORS = {
		m: BigInt(1e3),
		u: BigInt(1e6),
		n: BigInt(1e9),
		p: BigInt(1e12)
	};
	const MAX_MILLISATS = BigInt('2100000000000000000');
	const MILLISATS_PER_BTC = BigInt(1e11);
	function hrpToMillisat(hrpString) {
		let divisor, value;
		if (hrpString.slice(-1).match(/^[munp]$/)) {
			divisor = hrpString.slice(-1);
			value = hrpString.slice(0, -1);
		} else if (hrpString.slice(-1).match(/^[^munp0-9]$/)) {
			throw new Error('Not a valid multiplier for the amount');
		} else {
			value = hrpString;
		}

		if (!value.match(/^[0-9]+$/)) {
			throw new Error('Not a valid human readable amount:' + hrpString);
		}

		const valueBN = BigInt(value);

		const millisatoshisBN = divisor
			? (valueBN * MILLISATS_PER_BTC) / DIVISORS[divisor]
			: valueBN * MILLISATS_PER_BTC;

		if (
			(divisor === 'p' && !(valueBN % BigInt(10) === BigInt(0))) ||
			millisatoshisBN > MAX_MILLISATS
		) {
			throw new Error('Amount is outside of valid range');
		}

		return millisatoshisBN.toString()
	}

	const CHARSET = 'qpzry9x8gf2tvdw0s3jn54khce6mua7l';
	function bech32_decode (bech32) {
		return fiveBitArray = Array.from(bech32).map((char) => CHARSET.indexOf(char));
	}

	function bech32_toBytes (fiveBitArray) {
		const eightBitArray = [];
		let out_index = 0;
		let accumulator = 0;
		let bits = 0;
		for (let in_index = 0; in_index < fiveBitArray.length; in_index++) {
			accumulator <<= 5
			accumulator |= fiveBitArray[in_index];
			bits += 5;
			if (bits >= 8) {
				eightBitArray.push((accumulator >> (bits - 8)) & 0xFF);
				accumulator &= (1 << bits) - 1;
				bits -= 8;
			}
		}
		return new Uint8Array(eightBitArray)
	}

	function wordsToIntBE(words) {
		return words.reverse().reduce((total, item, index) => {
			return total + item * Math.pow(32, index)
		}, 0)
	}

	function hex_encode (bytes) {
		return [...new Uint8Array (bytes)].map(x => x.toString(16).padStart(2, "0")).join('');
	}

	var utf8_encode = (function () {
		var charCache = new Array(128);  // Preallocate the cache for the common single byte chars
		var charFromCodePt = String.fromCodePoint || String.fromCharCode;
		var result = [];

		return function (array) {
			var codePt, byte1;
			var buffLen = array.length;

			result.length = 0;

			for (var i = 0; i < buffLen;) {
				byte1 = array[i++];

				if (byte1 <= 0x7F) {
					codePt = byte1;
				} else if (byte1 <= 0xDF) {
					codePt = ((byte1 & 0x1F) << 6) | (array[i++] & 0x3F);
				} else if (byte1 <= 0xEF) {
					codePt = ((byte1 & 0x0F) << 12) | ((array[i++] & 0x3F) << 6) | (array[i++] & 0x3F);
				} else if (String.fromCodePoint) {
					codePt = ((byte1 & 0x07) << 18) | ((array[i++] & 0x3F) << 12) | ((array[i++] & 0x3F) << 6) | (array[i++] & 0x3F);
				} else {
					codePt = 63;	// Cannot convert four byte code points, so use "?" instead
					i += 3;
				}

				result.push(charCache[codePt] || (charCache[codePt] = charFromCodePt(codePt)));
			}

			return result.join('');
		};
	})();

	function routingInfoParser(words) {
		const routes = []
		let pubkey,
			shortChannelId,
			feeBaseMSats,
			feeProportionalMillionths,
			cltvExpiryDelta
		let routesBuffer = bech32_toBytes(words)
		while (routesBuffer.length > 0) {
			pubkey = hex_encode(routesBuffer.slice(0, 33)) // 33 bytes
			shortChannelId = hex_encode(routesBuffer.slice(33, 41)) // 8 bytes
			feeBaseMSats = parseInt(hex_encode(routesBuffer.slice(41, 45)), 16) // 4 bytes
			feeProportionalMillionths = parseInt(
				hex_encode(routesBuffer.slice(45, 49)),
				16
			) // 4 bytes
			cltvExpiryDelta = parseInt(hex_encode(routesBuffer.slice(49, 51)), 16) // 2 bytes

			routesBuffer = routesBuffer.slice(51)

			routes.push({
				pubkey,
				short_channel_id: shortChannelId,
				fee_base_msat: feeBaseMSats,
				fee_proportional_millionths: feeProportionalMillionths,
				cltv_expiry_delta: cltvExpiryDelta
			})
		}
		return routes
	}

	const FEATUREBIT_ORDER = [
		'option_data_loss_protect',
		'initial_routing_sync',
		'option_upfront_shutdown_script',
		'gossip_queries',
		'var_onion_optin',
		'gossip_queries_ex',
		'option_static_remotekey',
		'payment_secret',
		'basic_mpp',
		'option_support_large_channel'
	]
	function featureBitsParser(words) {
		const bools = words
			.slice()
			.reverse()
			.map(word => [
				!!(word & 0b1),
				!!(word & 0b10),
				!!(word & 0b100),
				!!(word & 0b1000),
				!!(word & 0b10000)
			])
			.reduce((finalArr, itemArr) => finalArr.concat(itemArr), [])
		while (bools.length < FEATUREBIT_ORDER.length * 2) {
			bools.push(false)
		}

		const featureBits = {}

		FEATUREBIT_ORDER.forEach((featureName, index) => {
			let status
			if (bools[index * 2]) {
				status = 'required'
			} else if (bools[index * 2 + 1]) {
				status = 'supported'
			} else {
				status = 'unsupported'
			}
			featureBits[featureName] = status
		})

		const extraBits = bools.slice(FEATUREBIT_ORDER.length * 2)
		featureBits.extra_bits = {
			start_bit: FEATUREBIT_ORDER.length * 2,
			bits: extraBits,
			has_required: extraBits.reduce(
				(result, bit, index) =>
					index % 2 !== 0 ? result || false : result || bit,
				false
			)
		}

		return featureBits
	}

	pr = pr.toLowerCase()

	let result = {
		unknown_types: [],
		routing_hints: [],
		expiry: 3600,
		min_final_cltv_expiry_delta: 18,
	};

	const pos = pr.lastIndexOf('1');
	var amount = "";
	if (pr.slice(2,6) === 'bcrt') {
		result.currency="bcrt",
		amount = pr.slice(6, pos);
	} else if (pr.slice(2,4) === 'bc') {
		result.currency="bc",
		amount = pr.slice(4, pos);
	} else if (pr.slice(2,4) === 'sb') {
		result.currency="sb",
		amount = pr.slice(4, pos);
	} else if (pr.slice(2,5) === 'tbs') {
		result.currency="tbs",
		amount = pr.slice(5, pos);
	} else if (pr.slice(2,4) === 'tb') {
		result.currency="tb",
		amount = pr.slice(4, pos);
	} else {
		throw new Error('Invalid currency');
	}
	if (amount != '') {
		result.amount = hrpToMillisat(amount);
	}

	// trim off the hrp and checksum:
	var words = bech32_decode(pr.slice(pos + 1, -6));
	const sigWords = words.slice(-104);
	words = words.slice(0, -104);

	result.timestamp = wordsToIntBE(words.slice(0, 7));
	words = words.slice(7);

	while (words.length > 0) {
		const tag = words[0];
		words = words.slice(1);

		const len = wordsToIntBE(words.slice(0, 2));
		words = words.slice(2);

		const val = words.slice(0, len);
		words = words.slice(len);

		switch (tag) {
		case 1:
			result.payment_hash = hex_encode(bech32_toBytes(val));
			break;
		case 16:
			result.payment_secret = true;
			break;
		case 13:
			result.description = utf8_encode(bech32_toBytes(val));
			break;
		case 27:
			result.additional_metadata = true;
			break;
		case 19:
			result.payee = hex_encode(bech32_toBytes(val));
			break;
		case 23:
			result.description_hash = true;
			break;
		case 6:
			result.expiry = wordsToIntBE(val);
			break;
		case 24:
			result.min_final_cltv_expiry_delta = wordsToIntBE(val);
			break;
		case 9:
			// TODO: parse following https://github.com/jireva/lightning-payencode/blob/99eec9ccfea6f05d21fef6057366ab4417d29084/lnaddr.py#L102
			result.fallback_address = true;
			break;
		case 3:
			result.routing_hints.push({route: routingInfoParser(val)});
			break;
		case 5:
			result.features = featureBitsParser(val);
			break;
		default:
			result.unknown_types.push(tag)
		}
	}

	return result;

"""
