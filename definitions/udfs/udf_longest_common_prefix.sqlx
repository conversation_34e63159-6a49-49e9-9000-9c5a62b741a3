config {
	type: "operations",
	hasOutput: true,
	description: "Calculates the length of the longest common prefix of two strings",
}

CREATE OR REPLACE FUNCTION
	${self()} (a STRING, b STRING) RETURNS INT64
LANGUAGE js AS r"""

	if ( a == null || b == null ) {
		return null;
	}
	let prefixLength = 0;
	for (let i = 0; i < Math.min(a.length, b.length); i++) {
		if (a[i] !== b[i]) {
			break;
		}
		prefixLength++;
	}
	return prefixLength;

"""
