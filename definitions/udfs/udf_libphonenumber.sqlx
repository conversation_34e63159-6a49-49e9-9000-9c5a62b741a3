config {
	type: "operations",
	hasOutput: true,
	description: "see https://gitlab.com/catamphetamine/libphonenumber-js"
}

CREATE OR REPLACE FUNCTION ${self()} (
	phone STRING
)

RETURNS STRUCT<
	number STRING,
	countryCallingCode STRING,
	nationalNumber STRING,
	country STRING,
	ext STRING,
	carrierCode STRING
>

LANGUAGE js OPTIONS (
	library=["gs://galoy_reporting_external_code/libphonenumber-max.js"]
) AS r"""
	try {
		return libphonenumber.parsePhoneNumber(phone, null);
	} catch(e) {
		return null;
	}
"""
