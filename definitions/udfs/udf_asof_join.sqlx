config {
  type: "operations",
  hasOutput: true,
}

CREATE OR REPLACE FUNCTION ${self()}(
	x ARRAY<TIMESTAMP>, y ARRAY<TIMESTAMP>
) RETURNS ARRAY<STRUCT<x TIMESTAMP, y TIMESTAMP>> LANGUAGE js AS """
    function js_timestamp_sort(a, b) { return a - b }
    x.sort(js_timestamp_sort);
    y.sort(js_timestamp_sort);
    var epoch = new Date(1970, 0, 1);
    var results = [];
    var j = 0;
    for (var i = 0; i < y.length; i++) {
	if (y[i] <= x[0]) {
            results.push({x: x[0], y: y[i]});
            continue;
	}
        var curr_res = {x: epoch, y: epoch};
        for (; j < x.length; j++) {
            if (x[j] <= y[i]) {
                curr_res.x = x[j];
                curr_res.y = y[i];
            } else {
                break;
            }
        }
        if (curr_res.x !== epoch) {
            results.push(curr_res);
            j--;
        }
    }

    return results;
"""
