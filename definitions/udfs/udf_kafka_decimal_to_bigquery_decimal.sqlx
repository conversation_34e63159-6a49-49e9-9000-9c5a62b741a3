config {
	type: "operations",
	hasOutput: true,
	description: "Converts decimal types imported through kafka to bigquery DECIMAL."
}

CREATE OR REPLACE FUNCTION
	${self()} (scale INT64, value BYTES) RETURNS NUMERIC
AS (
  (
    SELECT
      SUM(
        CASE
          WHEN i = 0 THEN CAST(IF(n < 128, n, n - 256) * POW(256, LENGTH(value) - i - 1) AS NUMERIC)
          ELSE CAST(n * POW(256, LENGTH(value) - i - 1) AS NUMERIC)
        END
      ) / CAST(POW(10, scale) AS NUMERIC)
    FROM UNNEST(TO_CODE_POINTS(value)) AS n WITH OFFSET i
  )
);
