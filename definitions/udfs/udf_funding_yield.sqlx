config {
  hasOutput: true,
  description: "Computes funding yield from funding rates"
}

CREATE OR REPLACE FUNCTION ${self()}(funding_rates ARRAY<FLOAT64>)
RETURNS FLOAT64
LANGUAGE js AS r"""
    var funding_yield = 1.0;
    for (var i = 0; i < funding_rates.length; i++) {
        funding_yield = funding_yield * (1.0 + funding_rates[i]);
    }

    const funding_rates_per_year = 1095
    var annual_funding_yield = Math.pow((funding_yield), funding_rates_per_year / funding_rates.length) - 1.0

    return annual_funding_yield;
"""
