const tables = ["okex_bills", "okex_bills_archive", "okex_bid_ask", "coincap_price", "okex_balances", "okex_position", "okex_funding_rates", "gossip_announcements", "gossip_updates", "gossip_timestamp", "okx_trading_account_history", "okx_funding_account_history", "gossip_messages", "onfido_workflow_runs", "watchlist", "watchlist_progress", "watchlist_compare", "bitfinex_book", "bitfinex_trades"]

envs.all.forEach((env) => {
  tables.forEach((table) => {
    declare({
      database: env.database,
      schema: `${env.prefix}_functions_raw`,
      name: table,
      tags: ["functions", env.name]
    })
  })
})
