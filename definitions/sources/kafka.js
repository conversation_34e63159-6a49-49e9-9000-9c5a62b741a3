const tables = [
  "mongodb_galoy_accounts",
  "mongodb_galoy_medici_transactions",
  "mongodb_galoy_wallets",
  "pg_stablesats_public_galoy_transactions",
  "pg_stablesats_public_okex_orders",
  "pg_stablesats_public_okex_transfers",
  "pg_stablesats_public_sqlx_ledger_balances",
  "pg_stablesats_public_user_trades"
]

envs.all.forEach((env) => {
  tables.forEach((table) => {
    declare({
      database: env.database,
      schema: `${env.prefix}_kafka_raw`,
      name: table,
      tags: ["kafka", env.name]
    })
  })
})
