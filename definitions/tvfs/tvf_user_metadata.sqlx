config {
	type: "operations",
	hasOutput: true,
	description: "Exposes censored metadata for an array of account_ids"
}

CREATE OR REPLACE TABLE FUNCTION
	${self()} (account_ids ARRAY<STRING>)
AS (

	WITH account_ips AS (
		SELECT account_id,
			ARRAY_AGG(STRUCT(
				ip AS address,
				lastConnection AS last_connection
			) ORDER BY lastConnection DESC) AS ips,

		FROM UNNEST(account_ids) AS account_id
			JOIN ${ref("stg_user_accounts")} USING (account_id)
			JOIN ${ref({
				name: "accountips",
				schema: envs.currentSchema("_galoy_raw")
			})} ON accountId = account_id

		GROUP BY account_id
	)

	SELECT
		account_id,
		level,
		DATE(created_at) AS created_at,
		username,
		phone,
		(
			SELECT status
			FROM UNNEST(statusHistory)
			ORDER BY updatedAt DESC
			LIMIT 1
		) AS status,
		ips,

	FROM UNNEST(account_ids) AS account_id
		JOIN ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})} accounts ON accounts.id = account_id
		LEFT JOIN ${ref({
			name: "users",
			schema: envs.currentSchema("_galoy_raw")
		})} users ON accounts.kratosUserId = users.userId
		LEFT JOIN account_ips USING (account_id)
);
