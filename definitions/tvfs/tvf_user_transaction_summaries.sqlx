config {
	type: "operations",
	hasOutput: true,
	description: "Computes a transaction summary table for an array of account_ids"
}

CREATE OR REPLACE TABLE FUNCTION
	${self()} (account_ids ARRAY<STRING>)
AS (

	WITH by_account_and_type AS (

		SELECT account_id, settlement_method, user_direction,
			SUM(usd_user_volume) AS usd_user_volume,
			COUNT(DISTINCT user_wallet_transaction_key) AS number_of_transactions,
			COUNT(DISTINCT counter_party_user_key) AS number_of_users_interacted_with,
			ARRAY_AGG(DISTINCT counter_party_user_key IGNORE NULLS) AS counter_party_user_keys,
			MIN(recorded_at) AS first_transaction,
			MAX(recorded_at) AS most_recent_transaction,

		FROM UNNEST(account_ids) AS account_id
			JOIN ${ref("stg_user_accounts")} USING (account_id)
			JOIN ${ref("stg_user_wallet_transactions")} USING (user_key)

		GROUP BY account_id, settlement_method, user_direction

	), by_account AS (

		SELECT account_id,
			ROUND(SUM(usd_user_volume), 2) AS usd_user_volume,
			SUM(number_of_transactions) AS number_of_transactions,
			ARRAY_CONCAT_AGG(counter_party_user_keys) AS counter_party_user_keys,
			ARRAY_AGG(
				STRUCT(
					settlement_method,
					user_direction,
					ROUND(usd_user_volume, 2) AS usd_volume,
					number_of_transactions,
					number_of_users_interacted_with
				)
				ORDER BY user_direction, settlement_method
			) AS transaction_break_down,
			MIN(first_transaction) AS first_transaction,
			MAX(most_recent_transaction) AS most_recent_transaction,

		FROM by_account_and_type

		GROUP BY account_id

	), balances AS (

		SELECT account_id,
			SUM(IF(currency="BTC", balance_change, 0)) / ********* AS bitcoin_balance,
			SUM(IF(currency="USD", balance_change, 0)) / 100 AS stablesats_balance,

		FROM UNNEST(account_ids) AS account_id
			JOIN ${ref("stg_user_accounts")} USING (account_id)
			JOIN ${ref("stg_user_wallet_transactions")} USING (user_key)

		GROUP BY account_id

	)

	SELECT * EXCEPT(counter_party_user_keys),
		(
			SELECT COUNT(DISTINCT counter_party_user_key)
			FROM UNNEST(counter_party_user_keys) AS counter_party_user_key
		) AS number_of_users_interacted_with

	FROM by_account
		LEFT JOIN balances USING (account_id)

);
