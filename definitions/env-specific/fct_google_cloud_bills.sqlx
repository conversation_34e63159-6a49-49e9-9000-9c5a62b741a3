config {
	type: "incremental",
	bigquery: {
		clusterBy: ["usage_start_time"],
		labels: {
			execution_env: envs.current
		}
	},
	description: "https://cloud.google.com/billing/docs/how-to/export-data-bigquery-tables/standard-usage",
}

pre_operations {
	DECLARE partitiontime_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(partition_time) FROM ${self()}`,
			`SELECT TIMESTAMP("2024-05-01")`
		)}
	);
}

SELECT usage_start_time,
	invoice.month AS invoice_month,
	cost_type,
	service.description AS service,
	sku.description AS sku,
	TIMESTAMP_DIFF(usage_end_time, usage_start_time, HOUR) AS usage_hours,
	project.name AS project,
	SAFE_DIVIDE(cost, currency_conversion_rate) AS usd_cost_before_credits,
	usage.pricing_unit AS usage_unit,
	usage.amount_in_pricing_units	AS usage_amount,
	SAFE_DIVIDE(IFNULL((SELECT SUM(amount) FROM UNNEST(credits)), 0), currency_conversion_rate) AS credit_amount,
	(SELECT STRING_AGG(DISTINCT COALESCE(full_name, name)) FROM UNNEST(credits)) AS credit_name,
	SAFE_DIVIDE((CAST(cost AS NUMERIC)) + IFNULL((
		SELECT SUM(CAST(amount AS NUMERIC))
		FROM UNNEST(credits)
	), 0), CAST(currency_conversion_rate AS NUMERIC)) AS usd_cost,
	_PARTITIONTIME AS partition_time,

FROM `cloud_billing.gcp_billing_export_v1_0147EF_64A84A_D3EA60`

WHERE _PARTITIONTIME > partitiontime_from
