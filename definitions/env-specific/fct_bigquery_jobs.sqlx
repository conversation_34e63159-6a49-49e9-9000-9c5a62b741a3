config {
	type: "incremental",
	bigquery: {
		clusterBy: ["creation_time"],
		labels: {
			execution_env: envs.current
		}
	},
}

pre_operations {
	DECLARE creation_time_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(creation_time) FROM ${self()}`,
			`SELECT TIMESTAMP("2024-06-13")`
		)}
	);
}

SELECT creation_time, job_type, statement_type, total_bytes_billed,
	REGEXP_EXTRACT(user_email, r'^[a-zA-Z0-9_.+-]+') AS user_name,
	IF(STARTS_WITH(destination_table.dataset_id, "_"), NULL, destination_table.dataset_id) AS destination_dataset,
	IF(STARTS_WITH(destination_table.table_id, "anon"), NULL,  destination_table.dataset_id) AS destination_table,
	ARRAY_TO_STRING(ARRAY(
		SELECT DISTINCT table_id
		FROM UNNEST(referenced_tables)
		ORDER BY table_id
	), " ") as referenced_tables,
	ROW_NUMBER() OVER() AS job_key,

FROM `region-us.INFORMATION_SCHEMA.JOBS`

WHERE creation_time > creation_time_from
	AND project_id = "galoy-reporting"
	AND state = "DONE"
