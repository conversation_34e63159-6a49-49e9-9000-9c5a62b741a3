config {
	tags: ["bbw"],
	type: "table",
}

js {
	const sell_memo_regex = "(?:Receive|Transfer) \\$([0-9.]+) to ";
}

SELECT user_wallet_transaction_key, user_key, recorded_at,
	"Sell" AS blink_fiat_direction,
	usd_user_volume - CAST(REGEXP_EXTRACT(memo, r'${sell_memo_regex}') AS NUMERIC) AS usd_blink_fiat_revenue,

FROM ${ref("stg_user_wallet_transactions")}

WHERE counter_party_user_key IN (
		SELECT user_key
		FROM ${ref("stg_user_accounts")}
		WHERE account_id IN (
			"eca4bcf6-4c08-4865-923e-ad6e1caa256b",
			"03a8fb5a-c855-42b8-8de5-ff8a6d704505"
		)
	)
	AND settlement_method="Intra-ledger"
	AND bank_direction="Inter-user transfer"
	AND balance_change < 0
	AND REGEXP_CONTAINS(memo, r'${sell_memo_regex}')
