config {
	tags: ["bbw"],
	type: "table",
	bigquery: {
		clusterBy: ["day"],
		labels: {
			execution_env: envs.current
		}
	},
}

SELECT user_pseudo_id, day,
	STARTS_WITH(first_page, "https://www.blink.sv/get") AS get,
	device_category,
	store_click_through IS NOT NULL
		OR (STARTS_WITH(first_page, "https://www.blink.sv/get") AND device_os IN ("Android", "iOS"))
		AS store_click_through,
	CASE
		WHEN store_click_through IS NOT NULL AND store_click_through = "play.google.com"
		THEN "Play store"
		WHEN store_click_through IS NOT NULL AND store_click_through = "apps.apple.com"
		THEN "App store"
		WHEN store_click_through IS NOT NULL AND store_click_through = "appgallery.huawei.com"
		THEN "Huawei store"
		WHEN STARTS_WITH(first_page, "https://www.blink.sv/get") AND device_os = "Android"
		THEN "Play store"
		WHEN STARTS_WITH(first_page, "https://www.blink.sv/get") AND device_os = "iOS"
		THEN "App store"
	END AS store,
	device_country,
	traffic_name,
	traffic_medium,
	traffic_source,
	first_page,
	device_os,
	engagement_time_msec/1000 AS engagement_time_sec,
	DATE_ADD(day, INTERVAL 2 DAY) AS day_import_lag_shifted,

FROM ${ref("stg_daily_blinksv_first_visits")}
