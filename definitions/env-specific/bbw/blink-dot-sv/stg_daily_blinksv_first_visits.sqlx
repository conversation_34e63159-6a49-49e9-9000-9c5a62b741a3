config {
	tags: ["bbw"],
	type: "incremental",
	bigquery: {
		clusterBy: ["day"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["day"]
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20231001"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT user_pseudo_id,
	MAX(_TABLE_SUFFIX) AS table_suffix,
	DATE(TIMESTAMP_MICROS(event_timestamp)) AS day,
	COUNTIF(event_name = 'page_view') AS page_view_count,
	SUM(IF(event_name="user_engagement", (
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key="engagement_time_msec"
	), 0)) AS engagement_time_msec,
	MAX(IF(event_name="first_visit", (
		SELECT value.string_value
		FROM UNNEST(event_params)
		WHERE key="page_location"
	), NULL)) AS first_page,
	COUNT(DISTINCT (
		SELECT value.string_value
		FROM UNNEST(event_params)
		WHERE key="ga_session_id"
	)) AS session_count,
	MAX(IF(event_name="click", (
		SELECT value.string_value
		FROM UNNEST(event_params)
		WHERE key="link_domain"
			AND value.string_value IN ("apps.apple.com","play.google.com","appgallery.huawei.com")
	), NULL)) AS store_click_through,
	ANY_VALUE(traffic_source.name) AS traffic_name,
	ANY_VALUE(traffic_source.medium) AS traffic_medium,
	ANY_VALUE(traffic_source.source) AS traffic_source,
	ANY_VALUE(device.category) AS device_category,
	ANY_VALUE(device.operating_system) AS device_os,
	ANY_VALUE(device.language) AS device_language,
	ANY_VALUE(geo.continent) AS device_continent,
	ANY_VALUE(geo.sub_continent) AS device_sub_continent,
	ANY_VALUE(geo.country) AS device_country,
	ANY_VALUE(geo.city) AS device_city,
	ANY_VALUE(geo.metro) AS device_metro,

FROM `${envs.project}.analytics_359146539.events_*`

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)

GROUP BY user_pseudo_id, day

HAVING LOGICAL_OR(event_name = "first_visit")
