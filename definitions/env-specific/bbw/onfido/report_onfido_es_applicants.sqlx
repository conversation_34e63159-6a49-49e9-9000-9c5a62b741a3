config {
	type: "table",
	description: "Requested by <PERSON> to follow up with individual users on the status of their applications."
}

SELECT account_id,
	MAX(onfido.created_at) AS started_at,
	ARRAY_AGG(distinct applicant_id) AS applicant_ids,
	MAX(JSON_VALUE(traits, '$.email')) AS email,
	CASE
		WHEN	LOGICAL_AND(JSON_VALUE(traits, '$.email') IS NULL)
		THEN MAX(raw_users.phone)
	END AS phone,

FROM ${ref("fct_onfido")} AS onfido
	JOIN ${ref("stg_user_accounts")} USING (user_key)
	JOIN ${ref("stg_onfido_identities")} USING (account_id)
	JOIN ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})} AS raw_users ON userId = kratos_user_id
	JOIN ${ref({
		name: "identities",
		schema: envs.currentSchema("_kratos_raw")
	})} AS kratos ON kratos.id = kratos_user_id


WHERE onfido.workflow_status = "declined"
	AND phone_number_country_name = "El Salvador"

GROUP BY account_id
