config {
	tags: "bbw",
	type: "table",
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["workflow_run_id"],
		nonNull: ["applicant_id", "created_at", "workflow_status", "created_at", "workflow_id", "workflow_version_id", "workflow_run_id"],
		rowConditions: [`(complete AND workflow_status IN ("abandoned", "approved", "declined", "error")) OR (NOT complete AND workflow_status NOT IN ("abandoned", "approved", "declined", "error"))`],
	},
}

WITH deduplicated AS (

	SELECT DISTINCT applicant_id, created_at, status, updated_at, workflow_id, workflow_version_id,
			id AS workflow_run_id,
			status IN ("abandoned", "approved", "declined", "error") AS complete,
			LEAD(updated_at) OVER (PARTITION BY id ORDER BY updated_at) AS lead_updated_at,

	FROM ${ref({
			name: "onfido_workflow_runs",
			schema: envs.currentSchema("_functions_raw")
		})}

)

SELECT workflow_run_id,
	MIN(created_at) AS created_at,
	LOGICAL_OR(complete) AS complete,
	ANY_VALUE(workflow_id HAVING MAX updated_at) AS workflow_id,
	ANY_VALUE(workflow_version_id HAVING MAX updated_at) AS workflow_version_id,
	ANY_VALUE(status HAVING MAX updated_at) AS workflow_status,
	ANY_VALUE(applicant_id HAVING MAX updated_at) AS applicant_id,
	CASE
		WHEN LOGICAL_OR(complete)
		THEN TIMESTAMP_DIFF(MAX(updated_at), MIN(created_at), SECOND)
	END AS seconds_to_complete,
	CASE
		WHEN NOT LOGICAL_OR(complete)
		THEN TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), MIN(created_at), SECOND)
	END AS seconds_incomplete,

FROM deduplicated

GROUP BY workflow_run_id
