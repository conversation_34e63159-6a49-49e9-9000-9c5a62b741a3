config {
	tags: "bbw",
	type: "table",
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["workflow_run_id"],
		nonNull: ["user_key", "created_at", "workflow_status", "created_at", "workflow_id", "workflow_version_id", "workflow_run_id"],
		rowConditions: [`(complete AND workflow_status IN ("abandoned", "approved", "declined", "error")) OR (NOT complete AND workflow_status NOT IN ("abandoned", "approved", "declined", "error"))`],
	},
}

SELECT user_key, run.workflow_run_id,
	run.created_at, complete, workflow_id, workflow_version_id, workflow_status,
	seconds_to_complete, seconds_incomplete,

FROM ${ref("stg_onfido_workflow_runs")} AS run
	JOIN ${ref("stg_onfido_identities")} USING (applicant_id)
	JOIN ${ref("stg_user_accounts")} USING (account_id)
