config {
	type: "table",
}

SELECT notes, account_id, recorded_at, settlement_method,
	bank_direction AS direction,
	ROUND(sat_user_volume/*********, 8) AS amount_btc,
	CASE
		WHEN settlement_method = "Onchain" AND bank_direction = "Outbound"
		THEN payee_addresses[SAFE_ORDINAL(1)]
	END AS counterparty_onchain_address,
	CASE
		WHEN settlement_method = "Lightning"
		THEN transaction_hash
	END AS payment_hash,
	journal_id AS transaction_id,
	CASE
		WHEN settlement_method = "Intra-ledger"
		THEN solette_counter_party_account_id
	 END AS intraledger_counterparty

FROM (
		SELECT "External request" AS notes, account_id FROM ${ref("stg_ago20231026_phones")}
		UNION ALL
		SELECT "Internal investigation" AS notes, account_id FROM ${ref("stg_solette_accounts")}
	)
	JOIN ${ref("stg_user_accounts")} USING (account_id)
	JOIN ${ref("stg_user_wallet_transactions")} USING (user_key)
	LEFT JOIN (
		SELECT user_key AS counter_party_user_key, account_id AS counter_party_account_id
		FROM ${ref("stg_user_accounts")}
	) USING (counter_party_user_key)
	LEFT JOIN (
		SELECT account_id AS solette_counter_party_account_id FROM ${ref("stg_ago20231026_phones")}
		UNION ALL
		SELECT account_id AS solette_counter_party_account_id FROM ${ref("stg_solette_accounts")}
	) ON counter_party_account_id = solette_counter_party_account_id

WHERE recorded_at < "2023-10-01"

ORDER BY notes, account_id, recorded_at
