config {
	tags: ["bbw"],
	type: "table",
}

WITH victim_phones AS (

	SELECT 1 AS list, 1 AS number, "79421390" AS victim_phone UNION ALL
	SELECT 1, 2, "71496869" UNION ALL
	SELECT 1, 3, "73392771" UNION ALL
	SELECT 1, 4, "75990646" UNION ALL
	SELECT 1, 5, "75907036" UNION ALL
	SELECT 1, 6, "74504626" UNION ALL

	SELECT 1, 8, "75532548" UNION ALL
	SELECT 1, 9, "62043204" UNION ALL
	SELECT 1, 10, "87385854" UNION ALL
	SELECT 1, 11, "74977206" UNION ALL
	SELECT 1, 12, "97338909" UNION ALL
	SELECT 1, 13, "79446935" UNION ALL
	SELECT 1, 14, "75402797" UNION ALL
	SELECT 1, 15, "68404130" UNION ALL
	SELECT 1, 16, "60666611" UNION ALL
	SELECT 1, 17, "61904646" UNION ALL
	SELECT 1, 18, "50493304418" UNION ALL
	SELECT 1, 19, "9149200959" UNION ALL
	SELECT 1, 20, "9147706469" UNION ALL
	SELECT 1, 21, "69752438" UNION ALL
	SELECT 1, 22, "68334425" UNION ALL
	SELECT 1, 23, "9723490" UNION ALL
	SELECT 1, 24, "33042005" UNION ALL
	SELECT 1, 25, "73552415" UNION ALL
	SELECT 1, 26, "77889808" UNION ALL
	SELECT 1, 27, "71833289" UNION ALL
	SELECT 1, 28, "70106576" UNION ALL
	SELECT 1, 29, "79860644" UNION ALL
	SELECT 1, 30, "76960164" UNION ALL
	SELECT 1, 31, "78505210" UNION ALL
	SELECT 1, 32, "61883942" UNION ALL
	SELECT 1, 33, "79316460" UNION ALL
	SELECT 1, 34, "77162059" UNION ALL
	SELECT 1, 35, "74782860" UNION ALL
	SELECT 1, 36, "79423403" UNION ALL
	SELECT 1, 37, "60792594" UNION ALL
	SELECT 1, 38, "70847447" UNION ALL
	SELECT 1, 39, "74707683" UNION ALL
	SELECT 1, 40, "79650398" UNION ALL
	SELECT 1, 41, "79091993" UNION ALL
	SELECT 1, 42, "79092993" UNION ALL
	SELECT 1, 43, "88850920" UNION ALL
	SELECT 1, 44, "70898791" UNION ALL
	SELECT 1, 45, "70092106" UNION ALL
	SELECT 1, 46, "76673374" UNION ALL
	SELECT 1, 47, "70876930" UNION ALL
	SELECT 1, 48, "76264363" UNION ALL
	SELECT 1, 49, "61292812" UNION ALL
	SELECT 1, 50, "50372891317" UNION ALL
	SELECT 1, 51, "61387075" UNION ALL
	SELECT 1, 52, "97940895" UNION ALL
	SELECT 1, 53, "78654624" UNION ALL
	SELECT 1, 54, "78038312" UNION ALL
	SELECT 1, 55, "78546482" UNION ALL
	SELECT 1, 56, "78038312" UNION ALL
	SELECT 1, 57, "75084867" UNION ALL
	SELECT 1, 58, "61975816" UNION ALL
	SELECT 1, 59, "79700126" UNION ALL
	SELECT 1, 60, "61721191" UNION ALL
	SELECT 1, 61, "50432376857" UNION ALL

	SELECT 1, 63, "72105042" UNION ALL
	SELECT 1, 64, "70866105" UNION ALL
	SELECT 1, 65, "77349372" UNION ALL
	SELECT 1, 66, "77409629" UNION ALL
	SELECT 1, 67, "75581075" UNION ALL
	SELECT 1, 68, "60282340" UNION ALL
	SELECT 1, 69, "77090622" UNION ALL
	SELECT 1, 70, "78874838" UNION ALL
	SELECT 1, 71, "73677159" UNION ALL
	SELECT 1, 72, "78589044" UNION ALL
	SELECT 1, 73, "78050096" UNION ALL
	SELECT 1, 74, "69743527" UNION ALL
	SELECT 1, 75, "77936818" UNION ALL
	SELECT 1, 76, "72733672" UNION ALL
	SELECT 1, 77, "71324851" UNION ALL
	SELECT 1, 78, "87524311" UNION ALL
	SELECT 1, 79, "76039217" UNION ALL
	SELECT 1, 80, "79623816" UNION ALL
	SELECT 1, 81, "79030685" UNION ALL
	SELECT 1, 82, "75982495" UNION ALL
	SELECT 1, 83, "50488884082" UNION ALL
	SELECT 1, 84, "70616997" UNION ALL
	SELECT 1, 85, "70675471" UNION ALL
	SELECT 1, 86, "70881740" UNION ALL
	SELECT 1, 87, "73794617" UNION ALL
	SELECT 1, 88, "78994248" UNION ALL
	SELECT 1, 89, "60722226" UNION ALL
	SELECT 1, 90, "700005150" UNION ALL
	SELECT 1, 91, "76424151" UNION ALL
	SELECT 1, 92, "78400242" UNION ALL
	SELECT 1, 93, "71961493" UNION ALL
	SELECT 1, 94, "73751145" UNION ALL
	SELECT 1, 95, "72265998" UNION ALL
	SELECT 1, 96, "78729475" UNION ALL
	SELECT 1, 97, "50432147286" UNION ALL
	SELECT 1, 98, "50493661688" UNION ALL
	SELECT 1, 99, "70230937" UNION ALL
	SELECT 1, 100, "75767249" UNION ALL
	SELECT 1, 101, "63156640" UNION ALL
	SELECT 1, 102, "79989279" UNION ALL
	SELECT 1, 103, "76988080" UNION ALL
	SELECT 1, 104, "70587523" UNION ALL
	SELECT 1, 105, "70676156" UNION ALL
	SELECT 1, 106, "60098990" UNION ALL
	SELECT 1, 107, "76322948" UNION ALL
	SELECT 1, 108, "78794328" UNION ALL
	SELECT 1, 109, "72606052" UNION ALL
	SELECT 1, 110, "75420115" UNION ALL
	SELECT 1, 111, "72395004" UNION ALL
	SELECT 1, 112, "96395530" UNION ALL
	SELECT 1, 113, "76676190" UNION ALL
	SELECT 1, 114, "78489848" UNION ALL
	SELECT 1, 115, "63058395" UNION ALL
	SELECT 1, 116, "77584933" UNION ALL
	SELECT 1, 117, "78713773" UNION ALL
	SELECT 1, 118, "71224758" UNION ALL
	SELECT 1, 119, "70560671" UNION ALL
	SELECT 1, 120, "75189473" UNION ALL
	SELECT 1, 121, "74799551" UNION ALL
	SELECT 1, 122, "74613386" UNION ALL
	SELECT 1, 123, "70686116" UNION ALL
	SELECT 1, 124, "76548195" UNION ALL
	SELECT 1, 125, "60750042" UNION ALL
	SELECT 1, 126, "83977479" UNION ALL
	SELECT 1, 127, "77946770" UNION ALL
	SELECT 1, 128, "77581803" UNION ALL
	SELECT 1, 129, "74486215" UNION ALL
	SELECT 1, 130, "77253704" UNION ALL
	SELECT 1, 131, "78733558" UNION ALL
	SELECT 1, 132, "73560618" UNION ALL
	SELECT 1, 133, "71013145" UNION ALL
	SELECT 1, 134, "74742848" UNION ALL
	SELECT 1, 135, "60114385" UNION ALL
	SELECT 1, 136, "76621384" UNION ALL
	SELECT 1, 137, "77548971" UNION ALL
	SELECT 1, 138, "70840774" UNION ALL
	SELECT 1, 139, "96635177" UNION ALL
	SELECT 1, 140, "73107388" UNION ALL
	SELECT 1, 141, "62008441" UNION ALL
	SELECT 1, 142, "74505922" UNION ALL
	SELECT 1, 143, "50495195533" UNION ALL
	SELECT 1, 144, "61199127" UNION ALL
	SELECT 1, 145, "71961075" UNION ALL
	SELECT 1, 146, "70288007" UNION ALL
	SELECT 1, 147, "71903760" UNION ALL
	SELECT 1, 148, "60742344" UNION ALL
	SELECT 1, 149, "74787524" UNION ALL
	SELECT 1, 150, "72253086" UNION ALL
	SELECT 1, 151, "76790457" UNION ALL
	SELECT 1, 152, "60678274" UNION ALL
	SELECT 1, 153, "69873033" UNION ALL

	SELECT 1, 155, "70099192" UNION ALL
	SELECT 1, 156, "78649439" UNION ALL
	SELECT 1, 157, "76536097" UNION ALL
	SELECT 1, 158, "95195533" UNION ALL
	SELECT 1, 159, "73369642" UNION ALL
	SELECT 1, 160, "71690462" UNION ALL
	SELECT 1, 161, "76914064" UNION ALL
	SELECT 1, 162, "70263269" UNION ALL
	SELECT 1, 163, "76511489" UNION ALL
	SELECT 1, 164, "76234915" UNION ALL
	SELECT 1, 165, "74488668" UNION ALL
	SELECT 1, 166, "50488015337" UNION ALL
	SELECT 1, 167, "79186427" UNION ALL
	SELECT 1, 168, "74752165" UNION ALL
	SELECT 1, 169, "74752165" UNION ALL
	SELECT 1, 170, "72803699" UNION ALL
	SELECT 1, 171, "72553342" UNION ALL
	SELECT 1, 172, "76171651" UNION ALL
	SELECT 1, 173, "73411891" UNION ALL
	SELECT 1, 174, "73411891" UNION ALL
	SELECT 1, 175, "75108156" UNION ALL
	SELECT 1, 176, "72785320" UNION ALL
	SELECT 1, 177, "12134228489" UNION ALL
	SELECT 1, 178, "72171202" UNION ALL
	SELECT 1, 179, "61483809" UNION ALL
	SELECT 1, 180, "50372741495" UNION ALL
	SELECT 1, 181, "75001551" UNION ALL
	SELECT 1, 182, "78647750" UNION ALL
	SELECT 1, 183, "50488631185" UNION ALL
	SELECT 1, 184, "75119255" UNION ALL
	SELECT 1, 185, "70973798" UNION ALL
	SELECT 1, 186, "77304038" UNION ALL
	SELECT 1, 187, "75202620" UNION ALL
	SELECT 1, 188, "60098990" UNION ALL
	SELECT 1, 189, "79739704" UNION ALL

	SELECT 1, 191, "78073789" UNION ALL
	SELECT 1, 192, "98443553" UNION ALL
	SELECT 1, 193, "72063073" UNION ALL
	SELECT 1, 194, "75867758" UNION ALL
	SELECT 1, 195, "78403090" UNION ALL
	SELECT 1, 196, "70210407" UNION ALL
	SELECT 1, 197, "79260764" UNION ALL
	SELECT 1, 198, "74510569" UNION ALL
	SELECT 1, 199, "79145260" UNION ALL
	SELECT 1, 200, "97841225" UNION ALL
	SELECT 1, 201, "71087126" UNION ALL
	SELECT 1, 202, "72720972" UNION ALL
	SELECT 1, 203, "70225255" UNION ALL
	SELECT 1, 204, "61738760" UNION ALL
	SELECT 1, 205, "72726851" UNION ALL
	SELECT 1, 206, "62069044" UNION ALL
	SELECT 1, 207, "76542778" UNION ALL
	SELECT 1, 208, "97565569" UNION ALL
	SELECT 1, 209, "79037734" UNION ALL
	SELECT 1, 210, "78521770" UNION ALL
	SELECT 1, 211, "78552923" UNION ALL
	SELECT 1, 212, "79240018" UNION ALL
	SELECT 1, 213, "60101607" UNION ALL
	SELECT 1, 214, "70198691" UNION ALL
	SELECT 1, 215, "76834805" UNION ALL
	SELECT 1, 216, "71406420" UNION ALL
	SELECT 1, 217, "73766335" UNION ALL
	SELECT 1, 218, "60455020" UNION ALL
	SELECT 1, 219, "60398278" UNION ALL
	SELECT 1, 220, "74705444" UNION ALL
	SELECT 1, 221, "75209776" UNION ALL
	SELECT 1, 222, "73859120" UNION ALL
	SELECT 1, 223, "17864823469" UNION ALL
	SELECT 1, 224, "73728690" UNION ALL
	SELECT 1, 225, "71657766" UNION ALL

	SELECT 1, 227, "70142110" UNION ALL
	SELECT 1, 228, "72011384" UNION ALL
	SELECT 1, 229, "72983867" UNION ALL
	SELECT 1, 230, "75860778" UNION ALL

	SELECT 1, 232, "77549928" UNION ALL
	SELECT 1, 233, "79601733" UNION ALL
	SELECT 1, 234, "61550098" UNION ALL
	SELECT 1, 235, "60665984" UNION ALL
	SELECT 1, 236, "63003669" UNION ALL
	SELECT 1, 237, "77889808" UNION ALL

	SELECT 2, 1, "71831971" UNION ALL
	SELECT 2, 2, "76070342" UNION ALL
	SELECT 2, 3, "70384341" UNION ALL
	SELECT 2, 4, "73634904" UNION ALL
	SELECT 2, 5, "61507972" UNION ALL
	SELECT 2, 6, "70374039" UNION ALL
	SELECT 2, 7, "78435650" UNION ALL
	SELECT 2, 8, "63067736" UNION ALL
	SELECT 2, 9, "76905105" UNION ALL
	SELECT 2, 10, "78679198" UNION ALL
	SELECT 2, 11, "75085042" UNION ALL
	SELECT 2, 12, "76180664" UNION ALL
	SELECT 2, 13, "61883942" UNION ALL
	SELECT 2, 14, "79280196" UNION ALL
	SELECT 2, 15, "73112498" UNION ALL
	SELECT 2, 16, "70602283" UNION ALL
	SELECT 2, 17, "69743930" UNION ALL
	SELECT 2, 18, "70679125" UNION ALL
	SELECT 2, 19, "70621136" UNION ALL
	SELECT 2, 20, "78083840" UNION ALL
	SELECT 2, 21, "75406775" UNION ALL
	SELECT 2, 22, "60544181" UNION ALL
	SELECT 2, 23, "76222208" UNION ALL
	SELECT 2, 24, "71833289" UNION ALL
	SELECT 2, 25, "69329776" UNION ALL
	SELECT 2, 26, "70140263" UNION ALL
	SELECT 2, 27, "74500579" UNION ALL
	SELECT 2, 28, "78948104" UNION ALL
	SELECT 2, 29, "60094247" UNION ALL
	SELECT 2, 30, "78930135" UNION ALL
	SELECT 2, 31, "75945412" UNION ALL
	SELECT 2, 32, "76201353" UNION ALL
	SELECT 2, 33, "61725159" UNION ALL
	SELECT 2, 34, "70584662" UNION ALL
	SELECT 2, 35, "76443448" UNION ALL
	SELECT 2, 36, "72052033" UNION ALL
	SELECT 2, 37, "71375161" UNION ALL
	SELECT 2, 38, "79550234" UNION ALL
	SELECT 2, 39, "76776075" UNION ALL
	SELECT 2, 40, "75935725" UNION ALL
	SELECT 2, 41, "79350334" UNION ALL
	SELECT 2, 42, "76121183" UNION ALL
	SELECT 2, 43, "70343180" UNION ALL
	SELECT 2, 44, "75055773" UNION ALL
	SELECT 2, 45, "88923478" UNION ALL
	SELECT 2, 46, "79768117" UNION ALL
	SELECT 2, 47, "70412302" UNION ALL
	SELECT 2, 48, "70373329" UNION ALL
	SELECT 2, 49, "76928923" UNION ALL
	SELECT 2, 50, "70450425" UNION ALL
	SELECT 2, 51, "78754727" UNION ALL
	SELECT 2, 52, "50372891317" UNION ALL
	SELECT 2, 53, "79382509" UNION ALL
	SELECT 2, 54, "79548117" UNION ALL
	SELECT 2, 55, "77286120" UNION ALL
	SELECT 2, 56, "75988964" UNION ALL
	SELECT 2, 57, "71769533" UNION ALL
	SELECT 2, 58, "78914174" UNION ALL
	SELECT 2, 59, "78038312" UNION ALL
	SELECT 2, 60, "76414687" UNION ALL
	SELECT 2, 61, "73876653" UNION ALL
	SELECT 2, 62, "60226632" UNION ALL
	SELECT 2, 63, "64200490" UNION ALL
	SELECT 2, 64, "50432376857" UNION ALL
	SELECT 2, 65, "98035298" UNION ALL
	SELECT 2, 66, "74542950" UNION ALL
	SELECT 2, 67, "70809962" UNION ALL
	SELECT 2, 68, "74997404" UNION ALL

	SELECT 2, 70, "74390210" UNION ALL
	SELECT 2, 71, "72730784" UNION ALL
	SELECT 2, 72, "78512223" UNION ALL
	SELECT 2, 73, "78977328" UNION ALL
	SELECT 2, 74, "77615011" UNION ALL
	SELECT 2, 75, "73876039" UNION ALL
	SELECT 2, 76, "13106545292" UNION ALL
	SELECT 2, 77, "73018275" UNION ALL
	SELECT 2, 78, "72613756" UNION ALL
	SELECT 2, 79, "79378135" UNION ALL
	SELECT 2, 80, "77276765" UNION ALL
	SELECT 2, 81, "76569283" UNION ALL
	SELECT 2, 82, "71810222" UNION ALL
	SELECT 2, 83, "78643736" UNION ALL
	SELECT 2, 84, "76560736" UNION ALL
	SELECT 2, 85, "76189559" UNION ALL
	SELECT 2, 86, "77390638" UNION ALL
	SELECT 2, 87, "78854060" UNION ALL
	SELECT 2, 88, "76988080" UNION ALL
	SELECT 2, 89, "76710425" UNION ALL
	SELECT 2, 90, "64388305" UNION ALL
	SELECT 2, 91, "79393467" UNION ALL
	SELECT 2, 92, "61241044" UNION ALL
	SELECT 2, 93, "77898556" UNION ALL
	SELECT 2, 94, "70891219" UNION ALL
	SELECT 2, 95, "64401108" UNION ALL
	SELECT 2, 96, "76489289" UNION ALL
	SELECT 2, 97, "61975816" UNION ALL
	SELECT 2, 98, "74382622" UNION ALL
	SELECT 2, 99, "60377030" UNION ALL
	SELECT 2, 100, "70085607" UNION ALL
	SELECT 2, 101, "77585375" UNION ALL
	SELECT 2, 102, "79401459" UNION ALL
	SELECT 2, 103, "79642447" UNION ALL
	SELECT 2, 104, "73579506" UNION ALL
	SELECT 2, 105, "74774002" UNION ALL
	SELECT 2, 106, "76813972" UNION ALL
	SELECT 2, 107, "61490464" UNION ALL
	SELECT 2, 108, "73281558" UNION ALL
	SELECT 2, 109, "76621384" UNION ALL
	SELECT 2, 110, "72545500" UNION ALL
	SELECT 2, 111, "70650216" UNION ALL
	SELECT 2, 112, "61620332" UNION ALL
	SELECT 2, 113, "71943880" UNION ALL
	SELECT 2, 114, "61710130" UNION ALL
	SELECT 2, 115, "79652013" UNION ALL
	SELECT 2, 116, "79652013" UNION ALL
	SELECT 2, 117, "87834309" UNION ALL
	SELECT 2, 118, "50360078458" UNION ALL
	SELECT 2, 119, "71473831" UNION ALL
	SELECT 2, 120, "74729489" UNION ALL
	SELECT 2, 121, "60742344" UNION ALL
	SELECT 2, 122, "71566125" UNION ALL
	SELECT 2, 123, "70533670" UNION ALL
	SELECT 2, 124, "76795995" UNION ALL
	SELECT 2, 125, "79109500" UNION ALL
	SELECT 2, 126, "72734788" UNION ALL
	SELECT 2, 127, "60525520" UNION ALL
	SELECT 2, 128, "74666758" UNION ALL
	SELECT 2, 129, "77538105" UNION ALL
	SELECT 2, 130, "74961508" UNION ALL
	SELECT 2, 131, "70846704" UNION ALL
	SELECT 2, 132, "70445965" UNION ALL
	SELECT 2, 133, "76489613" UNION ALL
	SELECT 2, 134, "69623564" UNION ALL
	SELECT 2, 135, "63076447" UNION ALL
	SELECT 2, 136, "71057950" UNION ALL
	SELECT 2, 137, "60656506" UNION ALL
	SELECT 2, 138, "72808266" UNION ALL
	SELECT 2, 139, "********" UNION ALL
	SELECT 2, 140, "73252942" UNION ALL
	SELECT 2, 141, "72160966" UNION ALL
	SELECT 2, 142, "76047972" UNION ALL
	SELECT 2, 143, "78679999" UNION ALL
	SELECT 2, 144, "76345663" UNION ALL
	SELECT 2, 145, "60737596" UNION ALL
	SELECT 2, 146, "73922826" UNION ALL
	SELECT 2, 147, "50497976882" UNION ALL
	SELECT 2, 148, "70873625" UNION ALL
	SELECT 2, 149, "72691352" UNION ALL
	SELECT 2, 150, "72741517" UNION ALL
	SELECT 2, 151, "76113534" UNION ALL
	SELECT 2, 152, "76083246" UNION ALL
	SELECT 2, 153, "76704968" UNION ALL
	SELECT 2, 154, "73596992" UNION ALL
	SELECT 2, 155, "76601435" UNION ALL
	SELECT 2, 156, "74891022" UNION ALL
	SELECT 2, 157, "70332635" UNION ALL
	SELECT 2, 158, "79522831" UNION ALL
	SELECT 2, 159, "50376871837" UNION ALL
	SELECT 2, 160, "12133981206" UNION ALL
	SELECT 2, 161, "77304038" UNION ALL
	SELECT 2, 162, "71250301" UNION ALL
	SELECT 2, 163, "18188587140" UNION ALL
	SELECT 2, 164, "71110866" UNION ALL
	SELECT 2, 165, "71273136" UNION ALL
	SELECT 2, 166, "75929696" UNION ALL
	SELECT 2, 167, "60130276" UNION ALL
	SELECT 2, 168, "75001076" UNION ALL
	SELECT 2, 169, "70151759" UNION ALL
	SELECT 2, 170, "75025104" UNION ALL
	SELECT 2, 171, "79279080" UNION ALL
	SELECT 2, 172, "72747064" UNION ALL
	SELECT 2, 173, "79095484" UNION ALL
	SELECT 2, 174, "77989386" UNION ALL
	SELECT 2, 175, "77134998" UNION ALL
	SELECT 2, 176, "78430676" UNION ALL
	SELECT 2, 177, "69816397" UNION ALL
	SELECT 2, 178, "78409903" UNION ALL
	SELECT 2, 179, "75886430" UNION ALL
	SELECT 2, 180, "50495555138" UNION ALL
	SELECT 2, 181, "79124496" UNION ALL
	SELECT 2, 182, "68279283" UNION ALL
	SELECT 2, 183, "77571232" UNION ALL
	SELECT 2, 184, "79613868" UNION ALL
	SELECT 2, 185, "78512223" UNION ALL
	SELECT 2, 186, "75930352" UNION ALL
	SELECT 2, 187, "64587758" UNION ALL
	SELECT 2, 188, "72804492" UNION ALL
	SELECT 2, 189, "68206583" UNION ALL
	SELECT 2, 190, "76001901" UNION ALL
	SELECT 2, 191, "73977335" UNION ALL
	SELECT 2, 192, "78878358" UNION ALL
	SELECT 2, 193, "72496540" UNION ALL
	SELECT 2, 194, "72395004" UNION ALL
	SELECT 2, 195, "72281999" UNION ALL
	SELECT 2, 196, "79140736" UNION ALL
	SELECT 2, 197, "74121362" UNION ALL
	SELECT 2, 198, "69692129" UNION ALL
	SELECT 2, 199, "72176248" UNION ALL
	SELECT 2, 200, "60264320" UNION ALL
	SELECT 2, 201, "74305323" UNION ALL
	SELECT 2, 202, "********" UNION ALL
	SELECT 2, 203, "********" UNION ALL
	SELECT 2, 204, "********" UNION ALL
	SELECT 2, 205, "********" UNION ALL
	SELECT 2, 206, "********" UNION ALL
	SELECT 2, 207, "********" UNION ALL
	SELECT 2, 208, "********" UNION ALL
	SELECT 2, 209, "********" UNION ALL
	SELECT 2, 210, "********" UNION ALL
	SELECT 2, 211, "********"

), ago_request_counter_parties AS (

	SELECT DISTINCT counter_party_user_key

	FROM ${ref("stg_ago20231026_phones")}
		JOIN ${ref("stg_user_accounts")} USING (account_id)
		JOIN ${ref("stg_user_wallet_transactions")} USING (user_key)

)

SELECT list, number, victim_phone,
	account_id IS NOT NULL AS has_account,
	IF(usd_volume_from_solette_farms > 0 OR usd_volume_to_solette_farms > 0, username, NULL) AS username,
	IF(usd_volume_from_solette_farms > 0 OR usd_volume_to_solette_farms > 0, DATE(accounts.created_at), NULL) AS created_at,
	COALESCE(
		usd_volume_from_solette_farms > 0 OR usd_volume_to_solette_farms > 0,
		FALSE
	) AS transacted_with_solette_accounts_by_ip,
	counter_party_user_key IS NOT NULL AS transacted_with_solette_accounts_from_ago,

FROM victim_phones
	LEFT JOIN ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})} ON phone LIKE CONCAT('%', victim_phone)
	LEFT JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} ON kratosUserId = userId
	LEFT JOIN ${ref("stg_user_accounts")} AS accounts ON id = account_id
	LEFT JOIN ${ref("report_solette_counter_parties")} USING (user_key)
	LEFT JOIN ago_request_counter_parties ON counter_party_user_key = user_key
