config {
	tags: ["bbw"],
	type: "table",
	description: "Trace the following Transaction IDs to determine the wallet that sent the money, payment hash and address: 6484b21deb6f5ce4267fa449 and 6489d22beb6f5ce426d75c2a.",
}

WITH transactions AS (

	SELECT DISTINCT journal_id, journal_entry_id AS id,

	FROM ${ref("stg_journal_entries")}

	WHERE journal_entry_id IN (
		"6484b21deb6f5ce4267fa449",
		"6489d22beb6f5ce426d75c2a",
		"6477e15b378add0f0fe9d1ad",
		"649f0f5df165acf12d229bdd",
		"6477fcc3378add0f0fec5d31",
		"648c0cbaeb6f5ce4261826dd",
		"64979df34f18fadfd8cf35d5",
		"6495b93b4f18fadfd8ac6306",
		"648c7abd4f18fadfd804205d",
		"648c79fc4f18fadfd8040c40",
		"64738391378add0f0fa420bf",
		"6476a0b6378add0f0fd465c1",
		"64a0946bf165acf12de264dc",
		"647b9b5f378add0f0f281fc9",
		"648a66f2eb6f5ce426e39e4d",
		"649dc511f165acf12d08ecf8",
		"648b5432eb6f5ce426fbc70a",
		"649f6ff3f165acf12d2c94c2",
		"649864bf4f18fadfd8d8afbd",
		"649b8b86f165acf12de0187c",
		"6498b4be4f18fadfd8dd823b",
		"64971aaf4f18fadfd8c41a0b",
		"649483294f18fadfd8961c13",
		"649470ee4f18fadfd8946d9c"
	)

), transaction_details AS (

	SELECT id, user_direction, currency, balance_change, account_id, memo,

	FROM transactions
		JOIN ${ref("stg_user_wallet_transactions")} USING (journal_id)
		JOIN ${ref("stg_user_accounts")} USING (user_key)

)

SELECT * EXCEPT(ips)

FROM transaction_details
	LEFT JOIN ${ref("tvf_user_metadata")}(ARRAY(
			SELECT DISTINCT account_id FROM transaction_details
		)) AS metadata USING (account_id)

ORDER BY id, account_id
