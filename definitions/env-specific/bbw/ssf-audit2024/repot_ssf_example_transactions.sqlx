config {
	tags: ["bbw"],
	type: "table",
}

WITH transactions AS (

	SELECT DISTINCT journal_id, journal_entry_id AS transaction_id,

	FROM ${ref("stg_journal_entries")}

	WHERE journal_entry_id IN (
		"65b02974d7f80c715433f854",
		"65b02821d7f80c715433d7ac",
		"65b022c6d7f80c7154334fd3",
		"65b01b28d7f80c7154327c27",
		"65b016fad7f80c715432207c",
		"65b01e71d7f80c715432c493",
		"65b01b28d7f80c7154327c2a",
		"65b140c6d7f80c7154483418",
		"65aff9e0d7f80c71542f7fcc",
		"65b2a9d5d7f80c71546426b9",
		"65affc4ed7f80c71542fc1a3"
	)

), transaction_details AS (

	SELECT transaction_id,
		DATE(recorded_at) AS transaction_date,
		settlement_method,
		user_direction AS direction,
		CASE
			WHEN currency = "BTC"
			THEN format("%0.8f BTC", balance_change / *********)
			WHEN currency = "USD"
			THEN format("$%0.2f", balance_change / 100)
		END AS amount,
		CASE
			WHEN settlement_method IN ("Lightning", "Onchain")
			THEN transaction_hash
		END AS transaction_hash,
		memo AS memo,
		account_id,

	FROM transactions
		JOIN ${ref("stg_user_wallet_transactions")} USING (journal_id)
		JOIN ${ref("stg_user_accounts")} USING (user_key)

)

SELECT * EXCEPT(ips)

FROM transaction_details
	LEFT JOIN ${ref("tvf_user_metadata")}(ARRAY(
			SELECT DISTINCT account_id FROM transaction_details
		)) AS metadata USING (account_id)

ORDER BY transaction_date, transaction_id, account_id
