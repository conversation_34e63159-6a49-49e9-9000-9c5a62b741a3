config {
	tags: ["bbw"],
	type: "table",
	assertions: {
		uniqueKey: ["user_wallet_transaction_key"],
		nonNull: ["user_wallet_transaction_key"],
	},
}

SELECT user_wallet_transaction_key,
	blink_fiat_direction,
	COALESCE(usd_blink_fiat_revenue, 0) AS usd_blink_fiat_revenue

FROM ${ref("stg_user_wallet_transactions")}
	LEFT JOIN ${ref("stg_blink_fiat_transactions")} USING (user_wallet_transaction_key)
