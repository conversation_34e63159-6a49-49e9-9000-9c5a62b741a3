config {
	tags: ["bbw"],
	type: "table",
}

/*
- daily track:
    - USD Liability
    - USD Exposure (& position)
    - Min < Exposure/Liability Ratio < Max

    - Exposure/Liability Ratio Distribution

    - BTC Balance on Exchange Trading Total
    - BTC Balance on Exchange Trading Frozen
    - BTC Balance on Exchange Trading Free
    - BTC Balance on Exchange Funding (aka buffer)
    - Min < Margin Leverage Ratio < Max

    - Trading Leverage = 4x
    - Min < Position/Exposure Leverage < Max

    - Dealer's Avg Open Price | Last Price | Liquidation Price
    - Dealer's Unrealized PnL Calc'd (i.e. (Last - AOP) * Position)
    - Dealer's Unrealized PnL Reported (from OKX)

    - Users' Avg Open Price | Last Price | Liquidation Price
    - Users' Unrealized PnL Calc'd (i.e. (Last - AOP) * Position)

    - Distribution of Users' Unrealized PnL Calc'd (i.e. (Last - AOP) * Position)

    - BTC Balance on Exchange Total
    - BTC Balance in Dealer's Wallet
    - BTC Balance Owed to Bria/Liquidity Pool
    - BTC Balance Under Dealer's Control (i.e. Wallet + Exchange - Liquidity Pool)
    - BTC Net Balance  (i.e. Under Control + uPnL)
    - Users' Owed BTC Balance (i.e. USD Liability / Last)
    - BTC Net Profit (i.e. BTC Net Balance - Owed BTC)

    - Last 24h Deposits/Withdrawals
    - Last 24h Orders Buys/Sells
    - Last 24h AutoDeleveraging

    - funding fee expenses, # + amount
    - funding fee incomes, # + amount

*/

WITH upnl AS (
  SELECT
      TIMESTAMP_TRUNC(minute, DAY, "UTC") AS day

    , ANY_VALUE(bank_liability_usd HAVING MAX minute) AS bank_liability_usd     -- calculated from journals of Dealer cross asset trades
    , ANY_VALUE(deposit_cent_cumsum HAVING MAX minute) AS deposit_cent_cumsum   -- calculated from journals of Users usd wallet transactions

    , ANY_VALUE(exchange_balance_usd HAVING MAX minute) AS exchange_exposure_usd

    , ANY_VALUE(user_pnl_sat_cumsum HAVING MAX minute) AS user_pnl_sat_cumsum
    , ANY_VALUE(user_paid_fees_sat_cumsum HAVING MAX minute) AS user_paid_fees_sat_cumsum

    , ANY_VALUE(liquidity_pool_transfer_btc_cumsum HAVING MAX minute) AS liquidity_pool_transfer_btc_cumsum
    , ANY_VALUE(liquidity_pool_transfer_fees_btc_cumsum HAVING MAX minute) AS liquidity_pool_transfer_fees_btc_cumsum

    , ANY_VALUE(exchange_last_price_usd_per_btc HAVING MAX minute) AS exchange_last_price_usd_per_btc
    , ANY_VALUE(exchange_average_open_price_usd_per_btc HAVING MAX minute) AS exchange_average_open_price_usd_per_btc
    , ANY_VALUE(exchange_calc_upnl_btc HAVING MAX minute) AS exchange_calc_upnl_btc
    , ANY_VALUE(exchange_unrealized_pnl_btc HAVING MAX minute) AS exchange_unrealized_pnl_btc

    , ANY_VALUE(bank_balance_btc HAVING MAX minute) AS bank_balance_btc

    , ANY_VALUE(exchange_balance_btc HAVING MAX minute) AS exchange_balance_btc

    , ANY_VALUE(immediate_fee_rate HAVING MAX minute) AS immediate_fee_rate
    , ANY_VALUE(delayed_fee_rate HAVING MAX minute) AS delayed_fee_rate

    , ANY_VALUE(market_buy_sats_per_cent HAVING MAX minute) AS market_buy_sats_per_cent
    , ANY_VALUE(market_sell_sats_per_cent HAVING MAX minute) AS market_sell_sats_per_cent
    , ANY_VALUE(avg_open_sats_per_cent HAVING MAX minute) AS avg_open_sats_per_cent
    , ANY_VALUE(user_owed_sat HAVING MAX minute) AS user_owed_sat
    , ANY_VALUE(user_owed_btc HAVING MAX minute) AS user_owed_btc
    , ANY_VALUE(bank_available_balance_btc HAVING MAX minute) AS bank_available_balance_btc
    , ANY_VALUE(user_average_open_price_usd_per_btc HAVING MAX minute) AS user_average_open_price_usd_per_btc
    , ANY_VALUE(user_upnl_sat HAVING MAX minute) AS user_upnl_sat
    , ANY_VALUE(bank_alt_net_profit_btc HAVING MAX minute) AS bank_alt_net_profit_btc
    , ANY_VALUE(bank_net_profit_btc HAVING MAX minute) AS bank_net_profit_btc

  FROM ${ref("stg_unrealized_pnl_explain")}
  GROUP BY day

), metrics AS (

  SELECT
      TIMESTAMP_TRUNC(minute, DAY, "UTC") AS day

    , ANY_VALUE(exposure_liability_ratio HAVING MAX minute) AS exposure_liability_ratio
    , ANY_VALUE(usd_liability HAVING MAX minute) AS usd_liability
    , ANY_VALUE(btc_liability HAVING MAX minute) AS btc_liability
    , ANY_VALUE(btc_outflow HAVING MAX minute) AS btc_outflow
    , ANY_VALUE(btc_dealer_wallet_balance HAVING MAX minute) AS btc_dealer_wallet_balance
    , ANY_VALUE(exposure_in_usd HAVING MAX minute) AS exposure_in_usd
    , ANY_VALUE(position_quantity HAVING MAX minute) AS exchange_position_contracts

    , ANY_VALUE(exposure_leverage_ratio HAVING MAX minute) AS exposure_leverage_ratio
    , ANY_VALUE(notional_lever HAVING MAX minute) AS notional_lever
    , ANY_VALUE(effective_leverage HAVING MAX minute) AS effective_leverage
    , ANY_VALUE(exchange_usd_total_balance HAVING MAX minute) AS exchange_usd_total_balance
    , ANY_VALUE(collateral_in_usd HAVING MAX minute) AS collateral_in_usd
    , ANY_VALUE(effective_collateral_in_usd HAVING MAX minute) AS effective_collateral_in_usd
    , ANY_VALUE(position_leverage HAVING MAX minute) AS position_leverage
    , ANY_VALUE(exchange_leverage HAVING MAX minute) AS exchange_leverage
    , ANY_VALUE(liquidation_price HAVING MAX minute) AS liquidation_price
    , ANY_VALUE(last_btc_price_in_usd HAVING MAX minute) AS last_btc_price_in_usd

    , ANY_VALUE(margin_leverage_ratio HAVING MAX minute) AS margin_leverage_ratio
    , ANY_VALUE(auto_deleveraging_indicator HAVING MAX minute) AS auto_deleveraging_indicator

    , ANY_VALUE(trading_btc_free_balance HAVING MAX minute) AS exchange_trading_free_balance_btc
    , ANY_VALUE(trading_btc_used_balance HAVING MAX minute) AS exchange_trading_used_balance_btc
    , ANY_VALUE(trading_btc_total_balance HAVING MAX minute) AS exchange_trading_total_balance_btc
    , ANY_VALUE(funding_btc_free_balance HAVING MAX minute) AS exchange_funding_free_balance_btc
    , ANY_VALUE(funding_btc_used_balance HAVING MAX minute) AS exchange_funding_used_balance_btc
    , ANY_VALUE(funding_btc_total_balance HAVING MAX minute) AS exchange_funding_total_balance_btc
    , ANY_VALUE(exchange_btc_total_balance HAVING MAX minute) AS exchange_total_balance_btc
    , ANY_VALUE(average_open_price HAVING MAX minute) AS average_open_price

    , ANY_VALUE(unrealized_pnl HAVING MAX minute) AS unrealized_pnl
    , ANY_VALUE(unrealized_pnl_ratio HAVING MAX minute) AS unrealized_pnl_ratio

    , ANY_VALUE(margin HAVING MAX minute) AS margin
    , ANY_VALUE(margin_ratio HAVING MAX minute) AS margin_ratio
    , ANY_VALUE(maintenance_margin_requirement HAVING MAX minute) AS maintenance_margin_requirement

  FROM ${ref("stg_dealer_metrics")}
  GROUP BY day

), okex_reported_transfers AS (

  SELECT
      TIMESTAMP_TRUNC(minute, DAY, "UTC") AS day
    , SUM(CASE WHEN type = 'Deposit' THEN amount ELSE 0 END) AS deposit
    , SUM(CASE WHEN type = 'Withdrawal' THEN amount ELSE 0 END) AS withdraw
  FROM ${ref("stg_okex_reported_transfers")}
  WHERE type IN ('Deposit', 'Withdrawal')
  AND TIMESTAMP_TRUNC(minute, DAY, "UTC") > '2022-06-21'
  GROUP BY day

), okex_reported_orders AS (

  SELECT
      TIMESTAMP_TRUNC(minute, DAY, "UTC") AS day
    , SUM(CASE WHEN ACTION = 'sell' THEN quantity ELSE 0 END) AS sell
    , SUM(CASE WHEN ACTION = 'buy' THEN quantity ELSE 0 END) AS buy
  FROM ${ref("stg_okex_reported_orders")}
  WHERE TIMESTAMP_TRUNC(minute, DAY, "UTC") > '2022-06-21'
  GROUP BY day

), joined AS (

  SELECT
    *
  FROM upnl
  FULL JOIN metrics using(day)
  FULL JOIN okex_reported_transfers using(day)
  FULL JOIN okex_reported_orders using(day)

), bounds AS (

  SELECT
      ROUND(0.95, 2) AS exposure_liability_ratio_minimum
    , ROUND(1.03, 2) AS exposure_liability_ratio_maximum
    , ROUND(1.0 / 0.9, 2) AS margin_leverage_ratio_minimum
    , ROUND(4.0 / 2.0, 2) AS margin_leverage_ratio_maximum
    , ROUND(4.0 * 0.9, 2) AS exposure_leverage_ratio_minimum
    , ROUND(4.0 / 2.0, 2) AS exposure_leverage_ratio_maximum

), final AS (

  SELECT
      day

    , ROUND(bank_liability_usd, 0) AS bank_liability_usd
    , ROUND(deposit_cent_cumsum / 100, 0) AS user_deposit_usd

    , exchange_exposure_usd

    , user_pnl_sat_cumsum
    , user_paid_fees_sat_cumsum

    , liquidity_pool_transfer_btc_cumsum
    , liquidity_pool_transfer_fees_btc_cumsum

    , ROUND(liquidity_pool_transfer_btc_cumsum + liquidity_pool_transfer_fees_btc_cumsum, 8) AS liquidity_pool_owed_btc

    , ROUND(exchange_last_price_usd_per_btc, 0) AS exchange_last_price_usd_per_btc
    , ROUND(exchange_average_open_price_usd_per_btc, 0) AS exchange_average_open_price_usd_per_btc
    , ROUND(exchange_calc_upnl_btc, 8) AS exchange_calc_upnl_btc
    , ROUND(exchange_unrealized_pnl_btc, 8) AS exchange_unrealized_pnl_btc

    , ROUND(bank_balance_btc, 8) AS bank_balance_btc

    , ROUND(exchange_balance_btc, 8) AS exchange_balance_btc

    , immediate_fee_rate
    , delayed_fee_rate

    , market_buy_sats_per_cent
    , market_sell_sats_per_cent
    , avg_open_sats_per_cent
    , user_owed_sat
    , ROUND(user_owed_btc, 8) AS user_owed_btc
    , bank_available_balance_btc
    , ROUND(user_average_open_price_usd_per_btc, 0) AS user_average_open_price_usd_per_btc
    , ROUND(user_upnl_sat / *********, 8) AS user_unrealized_pnl_btc
    , bank_alt_net_profit_btc
    , bank_net_profit_btc


    , ROUND(bank_balance_btc
      + exchange_total_balance_btc
      - liquidity_pool_transfer_btc_cumsum
      - liquidity_pool_transfer_fees_btc_cumsum, 8) AS available_balance_btc

    , ROUND(bank_balance_btc
      + exchange_total_balance_btc
      - liquidity_pool_transfer_btc_cumsum
      - liquidity_pool_transfer_fees_btc_cumsum
      + exchange_calc_upnl_btc, 8) AS calc_available_balance_btc

    , ROUND(bank_balance_btc
      + exchange_total_balance_btc
      - liquidity_pool_transfer_btc_cumsum
      - liquidity_pool_transfer_fees_btc_cumsum
      + exchange_unrealized_pnl_btc, 8) AS reported_available_balance_btc

    , ROUND(bank_available_balance_btc + exchange_calc_upnl_btc - user_owed_btc, 8) AS calc_net_profit_btc
    , ROUND(bank_available_balance_btc + exchange_unrealized_pnl_btc - user_owed_btc, 8) AS reported_net_profit_btc

    , ROUND(exposure_liability_ratio, 2) AS exposure_liability_ratio
    , usd_liability
    , btc_liability
    , btc_outflow
    , btc_dealer_wallet_balance
    , exposure_in_usd
    , exchange_position_contracts

    , ROUND(exposure_leverage_ratio, 2) AS exposure_leverage_ratio
    , notional_lever
    , effective_leverage
    , exchange_usd_total_balance
    , collateral_in_usd
    , effective_collateral_in_usd
    , position_leverage
    , exchange_leverage
    , ROUND(liquidation_price, 0) AS liquidation_price
    , ROUND(last_btc_price_in_usd, 0) AS last_btc_price_in_usd

    , ROUND(margin_leverage_ratio, 2) AS margin_leverage_ratio
    , auto_deleveraging_indicator

    , ROUND(exchange_trading_free_balance_btc, 8) AS exchange_trading_free_balance_btc
    , ROUND(exchange_trading_used_balance_btc, 8) AS exchange_trading_used_balance_btc
    , ROUND(exchange_trading_total_balance_btc, 8) AS exchange_trading_total_balance_btc
    , ROUND(exchange_funding_free_balance_btc, 8) AS exchange_funding_free_balance_btc
    , ROUND(exchange_funding_used_balance_btc, 8) AS exchange_funding_used_balance_btc
    , ROUND(exchange_funding_total_balance_btc, 8) AS exchange_funding_total_balance_btc
    , ROUND(exchange_total_balance_btc, 8) AS exchange_total_balance_btc
    , ROUND(average_open_price, 0) AS average_open_price

    , unrealized_pnl
    , unrealized_pnl_ratio

    , margin
    , margin_ratio
    , maintenance_margin_requirement

    , (SELECT exposure_liability_ratio_minimum FROM bounds) AS exposure_liability_ratio_minimum
    , (SELECT exposure_liability_ratio_maximum FROM bounds) AS exposure_liability_ratio_maximum
    , (SELECT margin_leverage_ratio_minimum FROM bounds) AS margin_leverage_ratio_minimum
    , (SELECT margin_leverage_ratio_maximum FROM bounds) AS margin_leverage_ratio_maximum
    , (SELECT exposure_leverage_ratio_minimum FROM bounds) AS exposure_leverage_ratio_minimum
    , (SELECT exposure_leverage_ratio_maximum FROM bounds) AS exposure_leverage_ratio_maximum

    , ROUND(COALESCE(deposit, 0), 8) AS deposit_btc
    , ROUND(COALESCE(withdraw, 0), 8) AS withdraw_btc

    , COALESCE(buy, 0) AS buy_contracts
    , COALESCE(sell, 0) AS sell_contracts

  FROM joined

)

SELECT
    day

-- - USD Liability
  , bank_liability_usd            -- calculated from journals of Dealer cross asset trades
  , user_deposit_usd              -- calculated from journals of Users usd wallet transactions

-- - USD Exposure (& position)
  , exchange_exposure_usd         --
  , exchange_position_contracts   --

-- - Min < Exposure/Liability Ratio < Max
  , exposure_liability_ratio_minimum
  -- , SAFE_DIVIDE(exchange_exposure_usd, bank_liability_usd) AS exposure_liability_ratio
  , exposure_liability_ratio
  , exposure_liability_ratio_maximum

-- - Exposure/Liability Ratio Distribution
-- ???

-- - BTC Balance on Exchange Trading Total
-- - BTC Balance on Exchange Trading Frozen
-- - BTC Balance on Exchange Trading Free
-- - BTC Balance on Exchange Funding (aka buffer)
  , exchange_balance_btc
  , exchange_trading_total_balance_btc
  , exchange_trading_used_balance_btc
  , exchange_trading_free_balance_btc
  , exchange_funding_total_balance_btc
-- - Min < Margin Leverage Ratio < Max
  , margin_leverage_ratio_minimum
  -- , SAFE_DIVIDE(exchange_trading_total_balance_btc, exchange_trading_used_balance_btc) as margin_leverage_ratio,
  , margin_leverage_ratio
  , margin_leverage_ratio_maximum

-- - Trading Leverage = 4x
  , exchange_leverage
-- - Min < Position/Exposure Leverage < Max
  -- , (exchange_leverage * 0.9) AS exposure_leverage_ratio_minimum
  -- , SAFE_DIVIDE(exposure_in_usd, (trading_btc_total_balance * last_btc_price_in_usd)) as exposure_leverage_ratio
  -- , (exchange_leverage / 2.0) AS exposure_leverage_ratio_maximum
  , exposure_leverage_ratio_minimum
  , exposure_leverage_ratio
  , exposure_leverage_ratio_maximum


-- - Dealer's Avg Open Price | Last Price | Liquidation Price
  , exchange_average_open_price_usd_per_btc -- upnl avg open price
  , average_open_price                      -- metrics avg open price
  , exchange_last_price_usd_per_btc -- upnl last price
  , last_btc_price_in_usd           -- metrics last price
  , liquidation_price
-- - Dealer's Unrealized PnL Calc'd (i.e. (Last - AOP) * Position)
  , exchange_calc_upnl_btc
-- - Dealer's Unrealized PnL Reported (from OKX)
  , exchange_unrealized_pnl_btc

-- - Users' Avg Open Price | Last Price | Liquidation Price
  , user_average_open_price_usd_per_btc
-- - Users' Unrealized PnL Calc'd (i.e. (Last - AOP) * Position)
  , user_unrealized_pnl_btc

-- - Distribution of Users' Unrealized PnL Calc'd (i.e. (Last - AOP) * Position)

-- - BTC Balance on Exchange Total
  , exchange_total_balance_btc
-- - BTC Balance in Dealer's Wallet
  , bank_balance_btc AS wallet_balance_btc
-- - BTC Balance Owed to Bria/Liquidity Pool
  , liquidity_pool_owed_btc
-- - BTC Balance Under Dealer's Control (i.e. Wallet + Exchange - Liquidity Pool)
  , available_balance_btc
-- - BTC Net Balance  (i.e. Under Control + uPnL)
  , calc_available_balance_btc
  , reported_available_balance_btc
-- - Users' Owed BTC Balance (i.e. USD Liability / Last)
  , user_owed_btc
-- - BTC Net Profit (i.e. BTC Net Balance - Owed BTC)
  , bank_available_balance_btc + exchange_calc_upnl_btc - user_owed_btc AS calc_net_profit_btc
  , bank_available_balance_btc + exchange_unrealized_pnl_btc - user_owed_btc AS reported_net_profit_btc

-- - Last 24h Deposits/Withdrawals
  , deposit_btc
  , withdraw_btc
-- - Last 24h Orders Buys/Sells
  , buy_contracts
  , sell_contracts
-- - Last 24h AutoDeleveraging

FROM final
ORDER BY day DESC
