config {
	tags: ["bbw"],
	type: "table",
	assertions: {
		uniqueKey: ["user_key"],
		nonNull: ["user_key"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH blink_fiat_revenue AS (

	SELECT user_key,
		SUM(usd_blink_fiat_revenue) AS usd_blink_fiat_revenue,

	FROM ${ref("stg_blink_fiat_transactions")}

	GROUP BY user_key

)

SELECT user_key, dismo_city,
	dismo_city IS NOT NULL AS dismo,
	account_oid = "61fc02b51cbd29dbace5aa0e" AS is_payroll,
	solette_accounts.account_id IS NOT NULL AS is_solette_farms_account,
	solette_counter_parties.user_key IS NOT NULL AS interacted_with_solette_farms,
	account_id = "03a8fb5a-c855-42b8-8de5-ff8a6d704505" AS blink_fiat_broker,
	COALESCE(DATE_TRUNC(DATE(accounts.created_at), MONTH) = "2023-11-01"
		AND accounts.level < 2
		AND number_of_transactions <= 2
		AND usd_user_volume < 0.1
		AND number_of_intraledger_counter_parties = 1
	, FALSE) AS is_possible_circles_farmer,
	COALESCE(onfido_approved, FALSE) AS onfido_approved,
	COALESCE(usd_blink_fiat_revenue, 0) AS usd_blink_fiat_revenue,
	COALESCE(accounts.username IN (
		"___",
		"agbegin",
		"ideasarelikeflames",
		"openoms",
		"bodymindarts",
		"sandipndev",
		"vbits",
		"dolcalmisv",
		"robvaldez",
		"Nicolas"
	), FALSE) AS galoy_team,
	onboarded.username IS NOT NULL AS blink_onboarded,
	account_id = "6de14c34-ec6f-5d28-b04a-1267ffb66580" AS amity_age_miners4kids,
	account_id = "e8f0b9dd-36bd-58d7-8ab5-bc8b1e92afd2" AS motiv,

FROM ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} AS accounts
	JOIN ${ref("stg_user_accounts")} ON id = account_id
	LEFT JOIN ${ref("stg_solette_accounts")} AS solette_accounts USING(account_id)
	LEFT JOIN ${ref("report_solette_counter_parties")} solette_counter_parties USING (user_key)
	LEFT JOIN ${ref("stg_dismo")} AS dismo ON LOWER(accounts.username) = dismo.username
	LEFT JOIN ${ref("stg_user_transaction_summaries")} USING (user_key)
	LEFT JOIN (
		SELECT account_id,
			LOGICAL_OR(workflow_status="approved") AS onfido_approved,
		FROM ${ref("stg_onfido_workflow_runs")}
			JOIN ${ref("stg_onfido_identities")} USING (applicant_id)
		GROUP BY account_id
	) USING (account_id)
	LEFT JOIN blink_fiat_revenue USING (user_key)
	LEFT JOIN ${ref("stg_blink_merchant_onboarding")} AS onboarded
		ON LOWER(accounts.username) = onboarded.username
