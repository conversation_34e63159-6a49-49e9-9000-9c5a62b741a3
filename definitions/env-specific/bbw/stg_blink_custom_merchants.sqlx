config {
	tags: ["bbw"],
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH usernames AS (
	SELECT username FROM ${ref("stg_dismo")}
	UNION DISTINCT
	SELECT username FROM ${ref("stg_blink_merchant_onboarding")}
)

SELECT id AS account_id,
	TRUE AS merchant,

FROM usernames AS u
	JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} AS accounts
		ON LOWER(accounts.username) = u.username
