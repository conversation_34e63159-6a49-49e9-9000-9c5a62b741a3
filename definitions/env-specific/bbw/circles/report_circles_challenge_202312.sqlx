config {
	tags: ["bbw"],
	type: "operations",
	hasOutput: true,
	description: "Query will pick around 6 random winners based on the criteria from the challenge description: include only the users with 10 inner circle for the month, size of inner circle does not influence chances of winning this month (simply random selection of all who got 10 inner circle)  ",
}

CREATE TABLE IF NOT EXISTS ${self()} AS

WITH shared_on_social_media AS (

	SELECT DISTINCT LOWER(manual_username) AS manual_username

	FROM  UNNEST([
		"kalkassa",
		"natoshi",
		"loicbtc",
		"king_mehal",
		"wizdom2004",
		"emmafx",
		"secia25",
		"blesseeddan12",
		"junaldo",
		"etophilleto",
		"giddyan7",
		"pawrangers",
		"gil7",
		"jicamet",
		"blessedval",
		"franco96",
		"xlindo",
		"larco",
		"chaos",
		"jriempa",
		"6ix",
		"lukaz",
		"kalmiprem",
		"bitcoindominicana",
		"bitfinger"
	]) AS manual_username

), eligible AS (

	SELECT username

	FROM shared_on_social_media
		JOIN ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})} ON LOWER(manual_username) = LOWER(username)
		JOIN ${ref("stg_user_accounts")} AS accounts ON id = account_id
		JOIN ${ref("stg_user_onboarding")} AS onboarding ON onboarded_by_user_key = accounts.user_key

	WHERE DATE_TRUNC(DATE(recorded_at), MONTH) = "2023-12-01"

	GROUP BY username

	HAVING COUNT(DISTINCT onboarding.user_key) >= 10

), with_total AS (

	SELECT username, COUNT(1) OVER () AS total,
	FROM eligible

)

SELECT username,

FROM with_total

WHERE RAND() < 6*(1/total)
