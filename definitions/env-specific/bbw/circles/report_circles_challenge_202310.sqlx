config {
	tags: ["bbw"],
	type: "operations",
	hasOutput: true,
	description: "Query will pick around 10 random winners based on the criteria from the challenge description:\nThis month we're giving 1,000,000 sats to somebody who has been driving Bitcoin adoption with Blink. To enter, simply post your Blink Circles card on social media with the hashtag #blinkcircles! (Tap People > View Circles > Share Circles)\nYou only need 1 person in your all-time inner circle for a chance at the sats. Want to increase the odds? Grow your inner circle! Each person in your inner circle for October gives you one additional chance to win.",
}

CREATE TABLE IF NOT EXISTS ${self()} AS

WITH shared_on_social_media AS (

	SELECT "bitcoinubuntu" AS manual_username
	UNION ALL
	SELECT "hyperfree"
	UNION ALL
	SELECT "drluke"
	UNION ALL
	SELECT "buddybuddy"
	UNION ALL
	SELECT "leeyak"
	UNION ALL
	SELECT "unsatisfied"
	UNION ALL
	SELECT "axelenriquex"
	UNION ALL
	SELECT "sanelo"
	UNION ALL
	SELECT "evelemus"
	UNION ALL
	SELECT "joen"
	UNION ALL
	SELECT "lumiex"
	UNION ALL
	SELECT "okin"
	UNION ALL
	SELECT "jicamet"
	UNION ALL
	SELECT "juanmayen"
	UNION ALL
	SELECT "montecreiffe"
	UNION ALL
	SELECT "rommeltsu"
	UNION ALL
	SELECT "ismaelgaldamez"
	UNION ALL
	SELECT "bitfinger"
	UNION ALL
	SELECT "kalmiprem"
	UNION ALL
	SELECT "tswift"
	UNION ALL
	SELECT "yutaro"
	UNION ALL
	SELECT "urlaub"
	UNION ALL
	SELECT "drluke"
	UNION ALL
	SELECT "blessedval"
	UNION ALL
	SELECT "giz"
	UNION ALL
	SELECT "fredisM"
	UNION ALL
	SELECT "ismael_larin"
	UNION ALL
	SELECT "metacoffeecompanyltd"
	UNION ALL
	SELECT "andrewkvtsk"
	UNION ALL
	SELECT "lukaz"
	UNION ALL
	SELECT "brindon"
	UNION ALL
	SELECT "luiscontreras51"
	UNION ALL
	SELECT "irving17"
	UNION ALL
	SELECT "giddyan7"
	UNION ALL
	SELECT "tanzania"
	UNION ALL
	SELECT "iamthelatter"
	UNION ALL
	SELECT "jonbyrne"
	UNION ALL
	SELECT "edga823"
	UNION ALL
	SELECT "franco96"
	UNION ALL
	SELECT "kuidoo"
	UNION ALL
	SELECT "christopher52"
	UNION ALL
	SELECT "razvanp"
	UNION ALL
	SELECT "louphis"
	UNION ALL
	SELECT "bwbjohn1633"
	UNION ALL
	SELECT "bitcoindominicana"
	UNION ALL
	SELECT "brayan"
	UNION ALL
	SELECT "raphaeleitor"
	UNION ALL
	SELECT "robert1980"

), no_duplicates AS (

	SELECT DISTINCT manual_username FROM shared_on_social_media

), weights AS (

	SELECT account_id,
		ANY_VALUE(username) AS username,
		1 + COUNT(DISTINCT onboarding.user_key) AS weight,

	FROM no_duplicates
		JOIN ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})} ON LOWER(manual_username) = LOWER(username)
		JOIN ${ref("stg_user_accounts")} AS accounts ON id = account_id
		JOIN ${ref("stg_user_onboarding")} AS onboarding ON onboarded_by_user_key = accounts.user_key

	WHERE DATE_TRUNC(DATE(recorded_at), MONTH) = "2023-10-01"

	GROUP BY account_id

), with_total AS (

	SELECT *, SUM(weight) OVER () AS total,
	FROM weights

)

SELECT account_id, username,

FROM with_total

WHERE RAND() < 10*(weight/total)
