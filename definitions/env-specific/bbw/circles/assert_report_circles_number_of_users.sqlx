config {
	tags: ["bbw"],
	type: "assertion",
}

(
	SELECT account_id
	FROM ${ref("stg_user_accounts")}
)
EXCEPT DISTINCT
(
	SELECT stg_user_accounts.account_id
	FROM ${ref("report_circles")} AS report_circles
		JOIN ${ref("stg_user_accounts")} AS stg_user_accounts
			ON report_circles.account_id = stg_user_accounts.account_id

	UNION ALL

	SELECT account_id
	FROM ${ref("stg_solette_accounts")}

	UNION ALL

	-- supportblink and supportblink1
	SELECT "192c73b2-8b57-542c-89dc-6bd5d70979ae" AS account_id
	UNION ALL
	SELECT "860073da-8dda-5365-ac49-29057cd17c4b" AS account_id
)
