config {
	tags: ["bbw"],
	type: "table",
}

SELECT
	account_id,
	COALESCE(ANY_VALUE(username), "") AS username,

FROM ${ref("stg_user_onboarding")} AS onboarding
	JOIN ${ref("stg_user_accounts")} AS accounts ON onboarded_by_user_key = accounts.user_key
	JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} AS raw_accounts ON raw_accounts.id = account_id

WHERE DATE_TRUNC(DATE(recorded_at), MONTH) = "2023-09-01"
	AND account_id NOT IN (
		SELECT account_id
		FROM ${ref("stg_solette_accounts")}
		WHERE account_id IS NOT NULL
	)
	AND COALESCE(username, "") NOT IN ("supportblink", "supportblink1")

GROUP BY account_id

HAVING COUNT(DISTINCT onboarding.user_key) >= 21

ORDER BY COUNT(DISTINCT onboarding.user_key) DESC
