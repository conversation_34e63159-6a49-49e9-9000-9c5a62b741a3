config {
	tags: ["bbw"],
	type: "view",
	assertions: {
		uniqueKey: ["account_id"],
		nonNull: ["recorded_at", "account_id"]
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH not_onboarded AS (

	SELECT account_id

	FROM ${ref("stg_rt_circles_accounts")}

	EXCEPT DISTINCT

	SELECT onboarded_account_id AS account_id

	FROM ${ref("stg_batch_onboarding_graph")}

), new_intraledger_transactions AS (

	SELECT account_id,
		JSON_VALUE(fullDocument._journal, '$."$oid"') AS journal_id,
		fullDocument.timestamp AS recorded_at,
		CASE
			WHEN fullDocument.credit > 0 THEN "Inbound"
			ELSE "Outbound"
		END AS wallet_direction,

	FROM ${ref({
			name: "mongodb_galoy_medici_transactions",
			schema: envs.currentSchema("_kafka_raw")
		})}
		JOIN ${ref("stg_rt_circles_wallets")} ON fullDocument.account_path[SAFE_ORDINAL(2)] = wallet_id
		JOIN ${ref("stg_rt_circles_accounts")} USING (account_id)

	WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 27 HOUR)
		AND operationType = "insert"
		AND fullDocument.account_path[SAFE_ORDINAL(1)] = "Liabilities"
		AND fullDocument.type IN ("on_us","ln_on_us","onchain_on_us")
		AND fullDocument.timestamp > (
			SELECT MAX(recorded_at)
			FROM ${ref("stg_journal_entries")}
		)

), numbered_inbound_transactions AS (

	SELECT l.account_id, l.recorded_at,
		r.account_id AS counter_party_account_id,
		ROW_NUMBER() OVER (
			PARTITION BY l.account_id
			ORDER BY l.recorded_at
		) as transaction_number,

	FROM new_intraledger_transactions AS l
		JOIN not_onboarded USING (account_id)
		LEFT JOIN new_intraledger_transactions AS r
			ON l.journal_id = r.journal_id AND l.account_id != r.account_id

	WHERE r.account_id IS NOT NULL
		AND l.wallet_direction = "Inbound"

)

SELECT account_id, recorded_at,
	counter_party_account_id AS onboarded_by_account_id,

FROM numbered_inbound_transactions

WHERE transaction_number = 1
