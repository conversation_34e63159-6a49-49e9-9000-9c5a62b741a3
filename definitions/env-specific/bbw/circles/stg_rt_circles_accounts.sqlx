config {
	tags: ["bbw"],
	type: "view",
	assertions: {
		uniqueKey: ["account_id"],
		nonNull: ["account_id", "user_id"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

(

	SELECT
		account_id,
		kratos_user_id AS user_id,

	FROM ${ref("stg_user_accounts")}

	WHERE kratos_user_id IS NOT NULL

)

UNION DISTINCT

(

	SELECT DISTINCT
		fullDocument.id AS account_id,
		fullDocument.kratosUserId AS user_id,

	FROM ${ref({
			name: "mongodb_galoy_accounts",
			schema: envs.currentSchema("_kafka_raw")
		})}

	WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 27 HOUR)
		AND operationType = "insert"
		AND fullDocument.id IS NOT NULL
		AND fullDocument.kratosUserId IS NOT NULL

)
