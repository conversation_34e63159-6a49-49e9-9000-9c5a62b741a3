config {
	tags: ["bbw"],
	type: "view",
}

WITH unaggregated AS (

	SELECT
		first.onboarded_by_user_key AS user_key,
		STRUCT(first.user_key, first.recorded_at) AS onboarding_first_degree,
		STRUCT(second.user_key, second.recorded_at) AS onboarding_second_degree,
		STRUCT(third.user_key, third.recorded_at) AS onboarding_third_degree,
		STRUCT(fourth.user_key, fourth.recorded_at) AS onboarding_fourth_degree,
		STRUCT(fifth.user_key, fifth.recorded_at) AS onboarding_fifth_degree,
		STRUCT(sixth.user_key, sixth.recorded_at) AS onboarding_sixth_degree,

	FROM ${ref("stg_user_onboarding")} AS first
		LEFT JOIN ${ref("stg_user_onboarding")} AS second
			ON first.user_key = second.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS third
			ON second.user_key = third.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS fourth
			ON third.user_key = fourth.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS fifth
			ON fourth.user_key = fifth.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS sixth
			ON fifth.user_key = sixth.onboarded_by_user_key

), unpivoted AS (

	SELECT *
	FROM unaggregated
	UNPIVOT(onboarding FOR degree in (
		onboarding_first_degree AS 1,
		onboarding_second_degree AS 2,
		onboarding_third_degree AS 3,
		onboarding_fourth_degree AS 4,
		onboarding_fifth_degree AS 5,
		onboarding_sixth_degree AS 6
	))

), pruned AS (

	SELECT user_key,
		onboarding.user_key AS onboarded_key,
		MIN(onboarding.recorded_at) AS recorded_at,
		MIN(degree) AS degree

	FROM unpivoted

	GROUP BY user_key, onboarded_key

	HAVING user_key != onboarded_key

)

SELECT
	this.account_id AS account_id,
	onboarded.account_id AS onboarded_account_id,
	recorded_at,
	degree,

FROM pruned
	JOIN ${ref("stg_user_accounts")} AS this USING (user_key)
	JOIN ${ref("stg_user_accounts")} AS onboarded ON onboarded_key = onboarded.user_key

ORDER BY account_id, onboarded_account_id
