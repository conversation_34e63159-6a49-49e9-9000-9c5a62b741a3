config {
	type: "test",
	dataset: "stg_rt_onboarding_graph_ordered"
}

input "stg_batch_onboarding_graph" {
	-- Standard onboarding graph
	SELECT "1" AS account_id, "2" AS onboarded_account_id, 1 AS degree
	UNION ALL
	SELECT  "2", "3", 1
	UNION ALL
	SELECT  "1", "3", 2
	UNION ALL
	SELECT  "3", "4", 1
	UNION ALL
	SELECT  "2", "4", 2
	UNION ALL
	SELECT  "1", "4", 3
	-- Add a loop
	UNION ALL
	SELECT  "3", "1", 1
	UNION ALL
	SELECT  "2", "1", 2
}
input "stg_rt_new_onboardings" {
	SELECT "3" AS onboarded_by_account_id, "5" AS account_id, TIMESTAMP_SECONDS(1) AS recorded_at
	UNION ALL
	SELECT "1" AS onboarded_by_account_id, "6" AS account_id, TIMESTAMP_SECONDS(2) AS recorded_at
}

SELECT *

FROM (
	SELECT "3" AS account_id, "5" AS onboarded_account_id, TIMESTAMP_SECONDS(1) AS recorded_at, 1 AS degree
	UNION ALL
	SELECT "2", "5", TIMESTAMP_SECONDS(1), 2
	UNION ALL
	SELECT "1", "5", TIMESTAMP_SECONDS(1), 3
	UNION ALL
	SELECT "1", "6", TIMESTAMP_SECONDS(2), 1
	UNION ALL
	SELECT "3", "6", TIMESTAMP_SECONDS(2), 2
	UNION ALL
	SELECT "2", "6", TIMESTAMP_SECONDS(2), 3
)

ORDER BY account_id, onboarded_account_id
