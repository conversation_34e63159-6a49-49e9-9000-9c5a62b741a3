config {
	tags: ["bbw"],
	type: "view",
	assertions: {
		uniqueKey: ["account_id", "onboarded_account_id"],
		uniqueKey: ["account_id", "onboarded_account_id", "degree","recorded_at"],
	},
}

(

	SELECT
		onboarded_by_account_id AS account_id,
		account_id AS onboarded_account_id,
		recorded_at,
		1 AS degree,

	FROM ${ref("stg_rt_new_onboardings")}

) UNION ALL (

	SELECT
		outer_circle.account_id AS account_id,
		inner_circle.account_id AS onboarded_account_id,
		inner_circle.recorded_at,
		degree + 1 AS degree,

	FROM ${ref("stg_rt_new_onboardings")} AS inner_circle
		JOIN ${ref("stg_batch_onboarding_graph")} AS outer_circle
			ON inner_circle.onboarded_by_account_id = outer_circle.onboarded_account_id

	WHERE degree < 6

)
