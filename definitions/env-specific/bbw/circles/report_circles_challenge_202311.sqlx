config {
	tags: ["bbw"],
	type: "table",
}

WITH shared_on_social_media AS (

	SELECT "buddybuddy" AS manual_username
	UNION ALL
	SELECT "okin"
	UNION ALL
	SELECT "blessedval"
	UNION ALL
	SELECT "giddyan7"
	UNION ALL
	SELECT "xmasxmenos"
	UNION ALL
	SELECT "salim"
	UNION ALL
	SELECT "runwithbitcoin"
	UNION ALL
	SELECT "btcmarco"
	UNION ALL
	SELECT "salvatoto"
	UNION ALL
	SELECT "loveisbitcoin"
	UNION ALL
	SELECT "jicamet"
	UNION ALL
	SELECT "lumiexc"
	UNION ALL
	SELECT "etophilleto"
	UNION ALL
	SELECT "loicbtc"
	UNION ALL
	SELECT "kitakripto"
	UNION ALL
	SELECT "pawrangers"
	UNION ALL
	SELECT "unsatisfied"
	UNION ALL
	SELECT "purplebrain13"
	UNION ALL
	SELECT "momss"
	UNION ALL
	SELECT "acreonte"
	UNION ALL
	SELECT "mak47"
	UNION ALL
	SELECT "bitfinger"
	UNION ALL
	SELECT "gustav<PERSON>ota001"
	UNION ALL
	SELECT "ambitious"
	UNION ALL
	SELECT "chukwuemeka"
	UNION ALL
	SELECT "honoratzabo"
	UNION ALL
	SELECT "adalfranco"
	UNION ALL
	SELECT "giddyan7"
	UNION ALL
	SELECT "phakyaol"
	UNION ALL
	SELECT "blessben"
	UNION ALL
	SELECT "blesseddan12"
	UNION ALL
	SELECT "manlykehendrix"
	UNION ALL
	SELECT "bitcoiner2021"
	UNION ALL
	SELECT "smart_playboi"
	UNION ALL
	SELECT "wizdom2004"
	UNION ALL
	SELECT "sendmoney"
	UNION ALL
	SELECT "makethetablelonger"
	UNION ALL
	SELECT "saail47"
	UNION ALL
	SELECT "olainukan"
	UNION ALL
	SELECT "sphinxter"
	UNION ALL
	SELECT "shedrack20"
	UNION ALL
	SELECT "joowealth"
	UNION ALL
	SELECT "emmafx7"
	UNION ALL
	SELECT "junaldo"
	UNION ALL
	SELECT "sparemary"
	UNION ALL
	SELECT "6ix"
	UNION ALL
	SELECT "juanmayen"
	UNION ALL
	SELECT "dennj75"
	UNION ALL
	SELECT "zigi"
	UNION ALL
	SELECT "bitcoin_bbail"
	UNION ALL
	SELECT "kesterbils"
	UNION ALL
	SELECT "jriepma1"
	UNION ALL
	SELECT "chris_van"
	UNION ALL
	SELECT "rafaeleitor"
	UNION ALL
	SELECT "franksaraviah"
	UNION ALL
	SELECT "benstark"
	UNION ALL
	SELECT "jamesturnernz"
	UNION ALL
	SELECT "poli"
	UNION ALL
	SELECT "ek1n8"
	UNION ALL
	SELECT "ismael_larin"
	UNION ALL
	SELECT "craigy88"
	UNION ALL
	SELECT "hyperfree"
	UNION ALL
	SELECT "sanelo"
	UNION ALL
	SELECT "mr16_akili"
	UNION ALL
	SELECT "exponostalgic"
	UNION ALL
	SELECT "robert1980"
	UNION ALL
	SELECT "yanirauseche"
	UNION ALL
	SELECT "einat"
	UNION ALL
	SELECT "diegoespinosa"
	UNION ALL
	SELECT "drluke"
	UNION ALL
	SELECT "eldelaspastillasnaranja"
	UNION ALL
	SELECT "juanio772006"
	UNION ALL
	SELECT "lagotish"
	UNION ALL
	SELECT "marexy"

), no_duplicates AS (

	SELECT DISTINCT manual_username FROM shared_on_social_media

)

SELECT manual_username,
	COUNT(DISTINCT onboarding.user_key) AS onboarded_in_november

FROM no_duplicates
	LEFT JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} AS raw_accounts ON LOWER(manual_username) = LOWER(username)
	LEFT JOIN ${ref("stg_user_accounts")} AS accounts ON id = account_id
	LEFT JOIN ${ref("stg_user_onboarding")} AS onboarding ON onboarded_by_user_key = accounts.user_key

WHERE DATE_TRUNC(DATE(recorded_at), MONTH) = "2023-11-01"

GROUP BY manual_username

ORDER BY onboarded_in_november DESC
