config {
	tags: ["bbw"],
	type: "view",
	assertions: {
		uniqueKey: ["wallet_id"],
		nonNull: ["wallet_id","account_id"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH rt AS (

	SELECT DISTINCT
		fullDocument.id AS wallet_id,
		fullDocument.accountId AS account_id,

	FROM ${ref({
			name: "mongodb_galoy_wallets",
			schema: envs.currentSchema("_kafka_raw")
		})}

	WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 27 HOUR)
		AND operationType = "insert"

)

SELECT DISTINCT wallet_id,
	COALESCE(batch.account_id, rt.account_id) AS account_id,

FROM ${ref("stg_user_wallets")} batch
	FULL JOIN rt USING (wallet_id)
