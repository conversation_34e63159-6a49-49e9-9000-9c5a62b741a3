config {
	tags: ["bbw"],
	type: "view",
	assertions: {
		nonNull: ["recorded_at", "account_id", "new_inner", "new_outer"],
	},
}

(

	SELECT account_id, recorded_at,
		IF(degree=1, 1, 0) AS new_inner,
		IF(degree>1, 1, 0) AS new_outer,
		CAST(100/POW(2,degree) AS INT64) AS new_points,

	FROM ${ref("stg_rt_onboarding_graph")}

) UNION ALL (

	SELECT account_id, recorded_at,
		IF(degree=1, 1, 0) AS new_inner,
		IF(degree>1, 1, 0) AS new_outer,
		CAST(100/POW(2,degree) AS INT64) AS new_points,

	FROM ${ref("stg_batch_onboarding_graph")}

	WHERE recorded_at > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)

)
