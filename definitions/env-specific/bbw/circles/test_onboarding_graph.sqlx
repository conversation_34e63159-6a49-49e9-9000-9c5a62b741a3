config {
	type: "test",
	dataset: "stg_batch_onboarding_graph_ordered"
}

input "stg_user_onboarding" {
	-- Standard onboarding graph
	SELECT 2 AS user_key, 1 AS onboarded_by_user_key, TIMESTAMP_SECONDS(1) AS recorded_at
	UNION ALL
	SELECT  3, 2, TIMESTAMP_SECONDS(2)
	UNION ALL
	SELECT  4, 2, TIMESTAMP_SECONDS(3)
	-- With loop
	UNION ALL
	SELECT 1, 3, TIMESTAMP_SECONDS(4)
}
input "stg_user_accounts" {
	SELECT "1" AS account_id, 1 AS user_key
	UNION ALL
	SELECT "2", 2
	UNION ALL
	SELECT "3", 3
	UNION ALL
	SELECT "4", 4
}

SELECT *

FROM (
	-- Standard onboarding graph
	SELECT
		"1" AS account_id,
		"2" AS onboarded_account_id,
		TIMESTAMP_SECONDS(1) AS recorded_at,
		1 AS degree
	UNION ALL
	SELECT "1", "3", TIMESTAMP_SECONDS(2), 2
	UNION ALL
	SELECT "1", "4", TIMES<PERSON>MP_SECONDS(3), 2
	UNION ALL
	SELECT "2", "3", TIMESTAMP_SECONDS(2), 1
	UNION ALL
	SELECT "2", "4", TIMESTAMP_SECONDS(3), 1

	-- With loop
	UNION ALL
	SELECT "3", "1", TIMESTAMP_SECONDS(4), 1
	UNION ALL
	SELECT "3", "2", TIMESTAMP_SECONDS(1), 2
	UNION ALL
	SELECT "3", "4", TIMESTAMP_SECONDS(3), 3
	UNION ALL
	SELECT "2", "1", TIMESTAMP_SECONDS(4), 2
)

ORDER BY account_id, onboarded_account_id
