config {
	tags: ["bbw"],
	type: "table",
	assertions: {
		uniqueKey: ["account_id"],
		rowConditions: [
			"number_onboarded_all_time_outer >= number_onboarded_this_month_outer",
			"number_onboarded_all_time_inner >= number_onboarded_this_month_inner",
		],
		nonNull: ["account_id", "onboarded_all_time_outer", "onboarded_this_month_outer", "points_all_time", "points_this_month", "number_onboarded_all_time_outer", "number_onboarded_this_month_outer", "number_onboarded_all_time_inner", "number_onboarded_this_month_inner", "leader_board_all_time_inner", "leader_board_this_month_inner", "leader_board_all_time_points", "leader_board_this_month_points", "leader_board_all_time_outer", "leader_board_this_month_outer"],
	},
}

WITH unaggregated AS (

	SELECT
		first.onboarded_by_user_key AS user_key,
		STRUCT(first.user_key, first.recorded_at) AS onboarding_first_degree,
		STRUCT(second.user_key, second.recorded_at) AS onboarding_second_degree,
		STRUCT(third.user_key, third.recorded_at) AS onboarding_third_degree,
		STRUCT(fourth.user_key, fourth.recorded_at) AS onboarding_fourth_degree,
		STRUCT(fifth.user_key, fifth.recorded_at) AS onboarding_fifth_degree,
		STRUCT(sixth.user_key, sixth.recorded_at) AS onboarding_sixth_degree,

	FROM ${ref("stg_user_onboarding")} AS first
		LEFT JOIN ${ref("stg_user_onboarding")} AS second
			ON first.user_key = second.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS third
			ON second.user_key = third.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS fourth
			ON third.user_key = fourth.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS fifth
			ON fourth.user_key = fifth.onboarded_by_user_key
		LEFT JOIN ${ref("stg_user_onboarding")} AS sixth
			ON fifth.user_key = sixth.onboarded_by_user_key

), unpivoted AS (

	SELECT *
	FROM unaggregated
	UNPIVOT(onboarding FOR degree in (
		onboarding_first_degree AS 1,
		onboarding_second_degree AS 2,
		onboarding_third_degree AS 3,
		onboarding_fourth_degree AS 4,
		onboarding_fifth_degree AS 5,
		onboarding_sixth_degree AS 6
	))

), pruned AS (

	SELECT user_key,
		onboarding.user_key AS onboarded_key,
		MIN(onboarding.recorded_at) AS recorded_at,
		MIN(degree) AS degree

	FROM unpivoted

	GROUP BY user_key, onboarded_key

	HAVING onboarded_key IS NOT NULL

), aggregated AS (

	SELECT user_key, degree,
		COUNT(DISTINCT onboarded_key) AS number_onboarded,
		COUNT(DISTINCT IF(
			DATE_TRUNC(DATE(recorded_at), MONTH) = DATE_TRUNC(CURRENT_DATE(), MONTH),
			onboarded_key,
			NULL
		)) AS number_onboarded_this_month,

	FROM pruned

	GROUP BY user_key, degree


), circles AS (

	SELECT user_key,
		ARRAY_AGG(STRUCT(degree, number_onboarded) ORDER BY degree) AS onboarded_all_time_outer,
		ARRAY_AGG(STRUCT(degree, number_onboarded_this_month) ORDER BY degree) AS onboarded_this_month_outer,

	FROM aggregated

	GROUP BY user_key

), circle_summaries AS (

	SELECT account_id, username, onboarded_all_time_outer, onboarded_this_month_outer,
		COALESCE((
			SELECT SUM(number_onboarded)
			FROM UNNEST(onboarded_all_time_outer)
			WHERE degree = 1
		), 0) AS number_onboarded_all_time_inner,
		COALESCE((
			SELECT SUM(number_onboarded_this_month)
			FROM UNNEST(onboarded_this_month_outer)
			WHERE degree = 1
		), 0) AS number_onboarded_this_month_inner,
		COALESCE((
			SELECT SUM(CAST(100*number_onboarded/POW(2,degree) AS INT64))
			FROM UNNEST(onboarded_all_time_outer)
		), 0) AS points_all_time,
		COALESCE((
			SELECT SUM(CAST(100*number_onboarded_this_month/POW(2,degree) AS INT64))
			FROM UNNEST(onboarded_this_month_outer)
		), 0) AS points_this_month,
		COALESCE((
			SELECT SUM(number_onboarded)
			FROM UNNEST(onboarded_all_time_outer)
			WHERE degree > 1
		), 0) AS number_onboarded_all_time_outer,
		COALESCE((
			SELECT SUM(number_onboarded_this_month)
			FROM UNNEST(onboarded_this_month_outer)
			WHERE degree > 1
		), 0) AS number_onboarded_this_month_outer,

	FROM circles
		FULL JOIN ${ref("stg_user_accounts")} USING (user_key)
		JOIN ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})} raw ON raw.id = account_id

	WHERE account_id NOT IN (
			SELECT account_id
			FROM ${ref("stg_solette_accounts")}
			WHERE account_id IS NOT NULL
		)
		AND COALESCE(username, "") NOT IN ("supportblink", "supportblink1")

), ranked AS (

	SELECT *,
		ROW_NUMBER() OVER (ORDER BY
			number_onboarded_all_time_inner DESC,
			number_onboarded_this_month_inner DESC,
			number_onboarded_all_time_outer DESC,
			number_onboarded_this_month_outer DESC,
			account_id
		) AS leader_board_all_time_inner,
		ROW_NUMBER() OVER (ORDER BY
			number_onboarded_this_month_inner DESC,
			number_onboarded_all_time_inner DESC,
			number_onboarded_this_month_outer DESC,
			number_onboarded_all_time_outer DESC,
			account_id
		) AS leader_board_this_month_inner,
		ROW_NUMBER() OVER (ORDER BY points_all_time DESC) AS leader_board_all_time_points,
		ROW_NUMBER() OVER (ORDER BY points_this_month DESC) AS leader_board_this_month_points,
		ROW_NUMBER() OVER (ORDER BY number_onboarded_all_time_outer DESC) AS leader_board_all_time_outer,
		ROW_NUMBER() OVER (ORDER BY number_onboarded_this_month_outer DESC) AS leader_board_this_month_outer,

	FROM circle_summaries

)

SELECT * REPLACE(
	CASE
		WHEN leader_board_all_time_inner <= 100 THEN username
		WHEN leader_board_this_month_inner <= 100 THEN username
		WHEN leader_board_all_time_outer <= 100 THEN username
		WHEN leader_board_this_month_outer <= 100 THEN username
		ELSE ""
	END AS username
)

FROM ranked
