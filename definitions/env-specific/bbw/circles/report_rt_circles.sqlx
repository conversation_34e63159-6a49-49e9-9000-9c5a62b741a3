config {
	tags: ["bbw"],
	type: "view",
	assertions: {
		uniqueKey: ["account_id"],
	},
}

WITH unified AS (

	SELECT recorded_at, account_id, onboarded_account_id, degree,

	FROM (
		SELECT * FROM ${ref("stg_batch_onboarding_graph")}
		UNION ALL
		SELECT * FROM ${ref("stg_rt_onboarding_graph")}
	)

	WHERE account_id != onboarded_account_id

), aggregated AS (

	SELECT account_id, degree,
		COUNT(DISTINCT onboarded_account_id) AS number_onboarded,
		COUNT(DISTINCT IF(
			DATE_TRUNC(DATE(recorded_at), MONTH) = DATE_TRUNC(CURRENT_DATE(), MONTH),
			onboarded_account_id,
			NULL
		)) AS number_onboarded_this_month,

	FROM unified

	GROUP BY account_id, degree


), circles AS (

	SELECT account_id,
		ARRAY_AGG(STRUCT(degree, number_onboarded) ORDER BY degree) AS onboarded_all_time_outer,
		ARRAY_AGG(STRUCT(degree, number_onboarded_this_month) ORDER BY degree) AS onboarded_this_month_outer,

	FROM aggregated

	GROUP BY account_id

), circle_summaries AS (

	SELECT account_id, user_id, username,
		COALESCE(onboarded_all_time_outer, []) AS onboarded_all_time_outer,
		COALESCE(onboarded_this_month_outer, []) AS onboarded_this_month_outer,
		COALESCE((
			SELECT SUM(number_onboarded)
			FROM UNNEST(onboarded_all_time_outer)
			WHERE degree = 1
		), 0) AS number_onboarded_all_time_inner,
		COALESCE((
			SELECT SUM(number_onboarded_this_month)
			FROM UNNEST(onboarded_this_month_outer)
			WHERE degree = 1
		), 0) AS number_onboarded_this_month_inner,
		COALESCE((
			SELECT SUM(CAST(100*number_onboarded/POW(2,degree) AS INT64))
			FROM UNNEST(onboarded_all_time_outer)
		), 0) AS points_all_time,
		COALESCE((
			SELECT SUM(CAST(100*number_onboarded_this_month/POW(2,degree) AS INT64))
			FROM UNNEST(onboarded_this_month_outer)
		), 0) AS points_this_month,
		COALESCE((
			SELECT SUM(number_onboarded)
			FROM UNNEST(onboarded_all_time_outer)
			WHERE degree > 1
		), 0) AS number_onboarded_all_time_outer,
		COALESCE((
			SELECT SUM(number_onboarded_this_month)
			FROM UNNEST(onboarded_this_month_outer)
			WHERE degree > 1
		), 0) AS number_onboarded_this_month_outer,

	FROM circles
		LEFT JOIN ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})} raw ON raw.id = account_id
		JOIN ${ref("stg_rt_circles_accounts")} USING (account_id)

	WHERE account_id NOT IN (
			SELECT account_id
			FROM ${ref("stg_solette_accounts")}
			WHERE account_id IS NOT NULL
		)
		AND COALESCE(username, "") NOT IN ("supportblink", "supportblink1")

), ranked AS (

	SELECT *,
		ROW_NUMBER() OVER (ORDER BY
			number_onboarded_all_time_inner DESC,
			number_onboarded_this_month_inner DESC,
			number_onboarded_all_time_outer DESC,
			number_onboarded_this_month_outer DESC,
			account_id
		) AS leader_board_all_time_inner,
		ROW_NUMBER() OVER (ORDER BY
			number_onboarded_this_month_inner DESC,
			number_onboarded_all_time_inner DESC,
			number_onboarded_this_month_outer DESC,
			number_onboarded_all_time_outer DESC,
			account_id
		) AS leader_board_this_month_inner,
		ROW_NUMBER() OVER (ORDER BY points_all_time DESC) AS leader_board_all_time_points,
		ROW_NUMBER() OVER (ORDER BY points_this_month DESC) AS leader_board_this_month_points,
		ROW_NUMBER() OVER (ORDER BY number_onboarded_all_time_outer DESC) AS leader_board_all_time_outer,
		ROW_NUMBER() OVER (ORDER BY number_onboarded_this_month_outer DESC) AS leader_board_this_month_outer,

	FROM circle_summaries

)

SELECT * REPLACE(
	CASE
		WHEN leader_board_all_time_inner <= 100 THEN username
		WHEN leader_board_this_month_inner <= 100 THEN username
		WHEN leader_board_all_time_points <= 100 THEN username
		WHEN leader_board_this_month_points <= 100 THEN username
		WHEN leader_board_all_time_outer <= 100 THEN username
		WHEN leader_board_this_month_outer <= 100 THEN username
		ELSE ""
	END AS username
)

FROM ranked
