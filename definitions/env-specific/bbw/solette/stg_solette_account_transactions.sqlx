config {
	tags: ["bbw"],
	type: "table",
}

SELECT * REPLACE (
	IF(is_solette_account, NULL, counter_party_user_key) AS counter_party_user_key
)

FROM ${ref("stg_solette_accounts")}
	JOIN (SELECT user_key, account_id FROM ${ref("stg_user_accounts")}) USING (account_id)
	JOIN ${ref("fct_user_wallet_transactions")} USING (user_key)
	LEFT JOIN (
		SELECT user_key AS counter_party_user_key, TRUE AS is_solette_account
		FROM ${ref("stg_solette_accounts")}
			JOIN (SELECT user_key, account_id FROM ${ref("stg_user_accounts")}) USING (account_id)
	) USING (counter_party_user_key)
