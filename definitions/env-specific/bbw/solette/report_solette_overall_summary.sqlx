config {
	tags: ["bbw"],
	type: "table",
}

WITH by_type AS (

	SELECT settlement_method, user_direction,
		counter_party_user_key IS NULL AS between_solette_accounts,
		SUM(usd_user_volume) AS usd_user_volume,
		COUNT(DISTINCT user_wallet_transaction_key) AS number_of_transactions,
		COUNT(DISTINCT counter_party_user_key) AS number_of_users_interacted_with,
		ARRAY_AGG(DISTINCT counter_party_user_key IGNORE NULLS) AS counter_party_user_keys,
		MIN(recorded_at) AS first_transaction,
		MAX(recorded_at) AS most_recent_transaction

	FROM ${ref("stg_solette_account_transactions")}

	GROUP BY settlement_method, user_direction, between_solette_accounts

), overall AS (

	SELECT
		ROUND(SUM(usd_user_volume), 2) AS usd_user_volume,
		SUM(number_of_transactions) AS number_of_transactions,
		ARRAY_CONCAT_AGG(counter_party_user_keys) AS counter_party_user_keys,
		ARRAY_AGG(
			STRUCT(
				settlement_method,
				user_direction,
				between_solette_accounts,
				ROUND(usd_user_volume, 2) AS usd_volume,
				number_of_transactions,
				number_of_users_interacted_with
			)
			ORDER BY user_direction, settlement_method
		) AS transaction_break_down,
		MIN(first_transaction) AS first_transaction,
		MAX(most_recent_transaction) AS most_recent_transaction

	FROM by_type

)

SELECT * EXCEPT(counter_party_user_keys),
	(
		SELECT COUNT(DISTINCT counter_party_user_key)
		FROM UNNEST(counter_party_user_keys) AS counter_party_user_key
	) AS number_of_users_interacted_with

FROM overall
