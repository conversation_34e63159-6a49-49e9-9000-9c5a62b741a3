config {
	type: "operations",
	hasOutput: true,
}

IF "${envs.current}" NOT IN ("galoy-staging", "galoy-bbw")
THEN RETURN;
END IF;

CREATE OR REPLACE TABLE ${self()} AS

WITH solette_accounts as (

	-- Determined by looking at the network diagram
	SELECT *

	FROM UNNEST([
		"6490897d3d87fa768c07e87f",
		"648b92f09e9b07c235e1d637",
		"648b57569e9b07c235c280de",
		"648bc2de05da1d168d49e0f7",
		"649086e21e616e54b834e74c",
		"648b56d572ffadf7d1b68017",
		"648bf10ca29a4a68e4dbf735",
		"6483e6cdbd8e0a686567fb50",
		"647110fd558719858908f792",
		"648a8ad736f400287cbfa605",
		"648b48e89e9b07c235ba59cf",
		"648bd168a29a4a68e4d5bd22",
		"648ce6e061b3561318e10cc0",
		"64908a4d3d87fa768c080c3c",
		"647f3c015244947c2abc8c35",
		"6481275cec5131a826b0e218",
		"648cada561b3561318cb7ac2",
		"648d079761b3561318ebb62a",
		"6478a78c09002eaa20eeb50b",
		"645d6a706cfc7bc33d96e943",
		"641f1f45b529a8976723d54e",
		"641f1fefb529a8976723ff1e"
	]) AS account_oid

), solette_account_ips AS (

	SELECT DISTINCT ip_key

	FROM solette_accounts
		LEFT JOIN ${ref("stg_account_ips")} USING (account_oid)

), accounts_linked_to_solette_by_ip AS (

	SELECT DISTINCT account_oid

	FROM solette_account_ips
		LEFT JOIN ${ref("stg_account_ips")} USING (ip_key)

), unified AS (

	SELECT account_oid, "Intra-ledger network" AS notes FROM solette_accounts

	UNION ALL

	(
		SELECT account_oid, "Linked by IP address" AS notes FROM accounts_linked_to_solette_by_ip
		EXCEPT DISTINCT
		SELECT account_oid, "Linked by IP address" AS notes FROM solette_accounts
	)

)

SELECT account_id, notes,

FROM unified
	JOIN ${ref("stg_accounts")} USING (account_oid)
