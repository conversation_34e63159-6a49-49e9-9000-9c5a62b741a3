config {
	type: "operations",
	hasOutput: true,
}

IF "${envs.current}" NOT IN ("galoy-staging", "galoy-bbw")
THEN RETURN;
END IF;

CREATE OR REPLACE TABLE ${self()} AS

SELECT account_id,
	SUM(IF(currency="BTC", balance_change, 0)) / ********* AS bitcoin_balance,
	SUM(IF(currency="USD", balance_change, 0)) / 100 AS stablesats_balance

FROM ${ref("stg_solette_accounts")}
	JOIN (SELECT user_key, account_id FROM ${ref("stg_user_accounts")}) USING (account_id)
	JOIN ${ref("stg_user_wallet_transactions")} USING (user_key)

GROUP BY account_id
