config {
	tags: ["bbw"],
	type: "table",
}

SELECT counter_party_user_key AS user_key,
	ROUND(SUM(IF(user_direction="Inbound", usd_user_volume, 0)),2) AS usd_volume_to_solette_farms,
	ROUND(SUM(IF(user_direction="Outbound", usd_user_volume, 0)),2) AS usd_volume_from_solette_farms,
	SUM(IF(user_direction="Inbound", 1, 0)) AS number_of_transactions_to_solette_farms,
	SUM(IF(user_direction="Outbound", 1, 0)) AS number_of_transactions_from_solette_farms,
	MIN(recorded_at) AS first_transaction,
	MAX(recorded_at) AS most_recent_transaction

FROM ${ref("stg_solette_account_transactions")}

WHERE counter_party_user_key IS NOT NULL

GROUP BY counter_party_user_key
