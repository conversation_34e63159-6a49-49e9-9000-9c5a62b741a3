config {
	type: "operations",
	hasOutput: true,
}


CREATE TABLE IF NOT EXISTS ${self()} AS

WITH active AS (

	SELECT account_id

	FROM ${ref("fct_user_wallet_transactions")}
		JOIN (
			SELECT user_key, account_id
			FROM ${ref("stg_user_accounts")}
			WHERE phone_number_country_name = "El Salvador"
		) USING (user_key)

	WHERE recorded_at > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)

	GROUP BY account_id

	HAVING COUNT(1) > 1

)

SELECT
	phone,
	array_agg(title) AS merchant_stores,

FROM ${ref({
		name: "merchants",
		schema: envs.currentSchema("_galoy_raw")
	})}
	JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} AS accounts USING (username)
	JOIN active ON accounts.id = account_id
	JOIN ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})} ON accounts.kratosUserId = userId

GROUP BY phone
