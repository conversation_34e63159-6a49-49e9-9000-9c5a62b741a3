config {
	type: "operations",
	hasOutput: true,
	description: "Summary of account updates",
}

DECLARE now TIMESTAMP;
SET now = CURRENT_TIMESTAMP();

CREATE TABLE IF NOT EXISTS ${self()} AS
	SELECT kratos_user_id,
		now AS valid_from,
		now AS valid_to,
		email_set,
	FROM dataform_jireva.stg_kratos_identities;

CREATE OR REPLACE TABLE ${self()} AS

WITH previous AS (

	SELECT *

	FROM ${self()}

	WHERE valid_to = (SELECT MAX(valid_to) FROM ${self()})

), still_valid AS (

	SELECT previous.* REPLACE(
		now AS valid_to
	)

	FROM previous
		LEFT JOIN dataform_jireva.stg_kratos_identities AS latest USING(kratos_user_id)

	WHERE latest.email_set = previous.email_set

), no_longer_valid AS (

	SELECT previous.*

	FROM previous
		LEFT JOIN dataform_jireva.stg_kratos_identities AS latest USING(kratos_user_id)

	WHERE latest.email_set != previous.email_set
		OR latest.email_set IS NULL

), different AS (

	SELECT kratos_user_id,
		now AS valid_from,
		now AS valid_to,
		latest.email_set,

	FROM dataform_jireva.stg_kratos_identities AS latest
		LEFT JOIN previous USING(kratos_user_id)

	WHERE latest.email_set != previous.email_set
		OR previous.email_set IS NULL

), frozen_history AS (

	SELECT *

	FROM ${self()}

	WHERE valid_to < (SELECT MAX(valid_to) FROM ${self()})

)

SELECT * FROM different
UNION ALL
SELECT * FROM no_longer_valid
UNION ALL
SELECT * FROM still_valid
UNION ALL
SELECT * FROM frozen_history
