config {
  type: "view",
  assertions: {
    uniqueKey: ["journal_id"],
    nonNull: ["journal_id"],
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Calculates the \"effective spread\" paid by users during Stablesats trades."
}

WITH trades AS (
  SELECT
    recorded_at AS time,
    journal_id,
    dealer_cent_deposit,
    dealer_sat_deposit,
    sats_per_cent
  FROM
    ${ref("stg_trades")}
  WHERE
    dealer_cent_deposit != 0
    AND dealer_sat_deposit != 0
),
prices as (
  SELECT
    timestamp AS time,
    sats_per_cent_market_buy,
    sats_per_cent_market_sell
  FROM
    ${ref("stg_minute_swap_prices")}
  WHERE
    timestamp > (
      SELECT
        timestamp_sub(min(time), interval 1 hour)
      FROM
        trades
    )
),
distinct_price_times as (
  SELECT
    DISTINCT time
  FROM
    prices
),
arrayed_price_times AS (
  SELECT
    ARRAY_AGG(time) as times,
    DATE(time) as day
  FROM
    distinct_price_times
  GROUP BY
    day
),
distinct_trade_times AS (
  SELECT
    DISTINCT time
  FROM
    trades
),
arrayed_trade_times AS (
  SELECT
    ARRAY_AGG(time) as times,
    DATE(time) as day
  FROM
    distinct_trade_times
  GROUP BY
    day
),
asof_price_trade_time_tuples AS (
  SELECT
    ${ref("udf_asof_join")}(
      arrayed_price_times.times,
      arrayed_trade_times.times
    ) as asof_tuples,
    arrayed_price_times.day as day
  FROM
    arrayed_price_times
    JOIN arrayed_trade_times USING (day)
),
unnested_times AS (
  SELECT
    a.x as price_time,
    a.y as trade_time
  FROM
    asof_price_trade_time_tuples,
    UNNEST(asof_price_trade_time_tuples.asof_tuples) as a
),
joined as (
  SELECT
    DISTINCT u.trade_time as recorded_at,
    t.journal_id,
    t.dealer_cent_deposit,
    t.dealer_sat_deposit,
    t.sats_per_cent,
    LAST_VALUE(l.sats_per_cent_market_buy) OVER (latest_asof_time) as sats_per_cent_market_buy,
    LAST_VALUE(l.sats_per_cent_market_sell) OVER (latest_asof_time) as sats_per_cent_market_sell
  FROM
    unnested_times as u
    JOIN prices as l ON u.price_time = l.time
    JOIN trades as t ON u.trade_time = t.time WINDOW latest_asof_time AS (
      PARTITION BY t.time
      ORDER BY
        l.time ROWS between unbounded preceding
        and unbounded following
    )
)
SELECT
  recorded_at,
  journal_id,
  dealer_cent_deposit,
  dealer_sat_deposit,
  sats_per_cent,
  sats_per_cent_market_buy,
  sats_per_cent_market_sell,
  dealer_sat_deposit + dealer_cent_deposit * (
    IF(
      dealer_cent_deposit > 0,
      sats_per_cent_market_buy,
      sats_per_cent_market_sell
    )
  ) AS sat_stablesats_spread,
  dealer_cent_deposit + dealer_sat_deposit / (
    IF(
      dealer_cent_deposit > 0,
      sats_per_cent_market_buy,
      sats_per_cent_market_sell
    )
  ) AS cent_stablesats_spread
FROM
  (
    SELECT
      recorded_at,
      journal_id,
      dealer_cent_deposit,
      dealer_sat_deposit,
      sats_per_cent,
      sats_per_cent_market_buy,
      sats_per_cent_market_sell
    FROM
      joined
  )
