config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH user_pnl AS (

  SELECT
    TIMESTAMP_TRUNC(recorded_at, MINUTE, "UTC") AS minute,
    SUM(sat_user_pnl) AS user_pnl_sats,
    SUM(sat_stablesats_spread) AS user_fee_sats
  FROM ${ref("stg_trade_spreads")}
  LEFT JOIN ${ref("stg_user_pnl")} USING (journal_id)
  GROUP BY minute

), funding_fees AS (

  SELECT
    TIMESTAMP_TRUNC(recorded_at, MINUTE, "UTC") AS minute,
    SUM(sat_pnl) AS funding_fee_sats,
  FROM ${ref("stg_okex_funding_fees")}
  WHERE recorded_at > "2022-06-01"
  GROUP BY minute

), trading_fees_pnl_slippage AS (

  SELECT
    TIMESTAMP_TRUNC(recorded_at, MINUTE, "UTC") AS minute,
    SUM(sat_fee) AS trading_fee_sats,
    SUM(sat_pnl) AS trading_pnl_sats,
    SUM(slippage_sats) AS slippage_sats,
  FROM ${ref("stg_okex_orders")}
  WHERE recorded_at > "2022-06-01"
  GROUP BY minute

), deposit_fees AS (

  SELECT
    TIMESTAMP_TRUNC(created_at, MINUTE, "UTC") AS minute,
    SUM(fee) * 100000000 AS fee_sats,
  FROM ${ref("stg_stablesats_internal_okex_transfers_unified")}
  WHERE action = 'deposit'
  AND state = 'success'
  AND created_at > "2022-06-01"
  GROUP BY minute

), withdrawal_fees AS (

  SELECT
    TIMESTAMP_TRUNC(created_at, MINUTE, "UTC") AS minute,
    SUM(fee) * 100000000 AS fee_sats,
  FROM ${ref("stg_stablesats_internal_okex_transfers_unified")}
  WHERE action = 'withdraw'
  AND state = 'success'
  AND created_at > "2022-06-01"
  GROUP BY minute

), price_series AS (

  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(timestamp, INTERVAL 30 SECOND), MINUTE, "UTC") as minute,
    AVG(2000000 / (sats_per_cent_market_buy + sats_per_cent_market_sell)) AS usd_per_btc_mid_price,
  FROM ${ref("stg_minute_swap_prices")}
  GROUP BY minute

), exposure_liability AS (

  SELECT
    minute,
    exposure_usd,
    stablesats_usd_liability AS liability_usd,
    stablesats_exposure_liability_ratio AS exposure_liability_ratio,
  FROM ${ref("stg_exposure_liability_ratio")}

), pnl_unified AS (

  SELECT
    minute,
    user_pnl_sats,
    user_fee_sats,
    NULL AS funding_fee_sats,
    NULL AS trading_fee_sats,
    NULL AS trading_pnl_sats,
    NULL AS slippage_sats,
    NULL AS deposit_fee_sats,
    NULL AS withdrawal_fee_sats,
    NULL AS exposure_usd,
    NULL AS liability_usd,
    NULL AS exposure_liability_ratio,
  FROM user_pnl

  UNION ALL

  SELECT
    minute,
    NULL AS user_pnl_sats,
    NULL AS user_fee_sats,
    funding_fee_sats,
    NULL AS trading_fee_sats,
    NULL AS trading_pnl_sats,
    NULL AS slippage_sats,
    NULL AS deposit_fee_sats,
    NULL AS withdrawal_fee_sats,
    NULL AS exposure_usd,
    NULL AS liability_usd,
    NULL AS exposure_liability_ratio,
  FROM funding_fees

  UNION ALL

  SELECT
    minute,
    NULL AS user_pnl_sats,
    NULL AS user_fee_sats,
    NULL AS funding_fee_sats,
    trading_fee_sats,
    trading_pnl_sats,
    slippage_sats,
    NULL AS deposit_fee_sats,
    NULL AS withdrawal_fee_sats,
    NULL AS exposure_usd,
    NULL AS liability_usd,
    NULL AS exposure_liability_ratio,
  FROM trading_fees_pnl_slippage

  UNION ALL

  SELECT
    minute,
    NULL AS user_pnl_sats,
    NULL AS user_fee_sats,
    NULL AS funding_fee_sats,
    NULL AS trading_fee_sats,
    NULL AS trading_pnl_sats,
    NULL AS slippage_sats,
    fee_sats AS deposit_fee_sats,
    NULL AS withdrawal_fee_sats,
    NULL AS exposure_usd,
    NULL AS liability_usd,
    NULL AS exposure_liability_ratio,
  FROM deposit_fees

  UNION ALL

  SELECT
    minute,
    NULL AS user_pnl_sats,
    NULL AS user_fee_sats,
    NULL AS funding_fee_sats,
    NULL AS trading_fee_sats,
    NULL AS trading_pnl_sats,
    NULL AS slippage_sats,
    NULL AS deposit_fee_sats,
    fee_sats AS withdrawal_fee_sats,
    NULL AS exposure_usd,
    NULL AS liability_usd,
    NULL AS exposure_liability_ratio,
  FROM withdrawal_fees

  UNION ALL

  SELECT
    minute,
    NULL AS user_pnl_sats,
    NULL AS user_fee_sats,
    NULL AS funding_fee_sats,
    NULL AS trading_fee_sats,
    NULL AS trading_pnl_sats,
    NULL AS slippage_sats,
    NULL AS deposit_fee_sats,
    NULL AS withdrawal_fee_sats,
    exposure_usd,
    liability_usd,
    exposure_liability_ratio,
  FROM exposure_liability
  where exposure_liability_ratio is not null

), pnl_sync AS (

  SELECT
    minute,
    SUM(user_pnl_sats) AS user_pnl_sats,
    SUM(user_fee_sats) AS user_fee_sats,
    SUM(funding_fee_sats) AS funding_fee_sats,
    SUM(trading_fee_sats) AS trading_fee_sats,
    SUM(trading_pnl_sats) AS trading_pnl_sats,
    SUM(slippage_sats) AS slippage_sats,
    SUM(deposit_fee_sats) AS deposit_fee_sats,
    SUM(withdrawal_fee_sats) AS withdrawal_fee_sats,

    AVG(exposure_usd) AS exposure_usd,
    AVG(liability_usd) AS liability_usd,
    AVG(exposure_liability_ratio) AS exposure_liability_ratio,
  FROM pnl_unified
  group by minute

), pnl_with_price AS (

  SELECT
    pnl.minute,
    pnl.user_pnl_sats,
    pnl.user_fee_sats,
    pnl.funding_fee_sats,
    pnl.trading_fee_sats,
    pnl.trading_pnl_sats,
    pnl.slippage_sats,
    pnl.deposit_fee_sats,
    pnl.withdrawal_fee_sats,

    pnl.exposure_usd,
    pnl.liability_usd,
    pnl.exposure_liability_ratio,
    px.usd_per_btc_mid_price,
  FROM pnl_sync AS pnl
  LEFT JOIN price_series AS px USING (minute)

), pnl_coalesced AS (

  SELECT
    minute,
    COALESCE(user_pnl_sats, 0) AS user_pnl_sats,
    COALESCE(user_fee_sats, 0) AS user_fee_sats,
    COALESCE(funding_fee_sats, 0) AS funding_fee_sats,
    COALESCE(trading_fee_sats, 0) AS trading_fee_sats,
    COALESCE(trading_pnl_sats, 0) AS trading_pnl_sats,
    COALESCE(slippage_sats, 0) AS slippage_sats,
    COALESCE(deposit_fee_sats, 0) AS deposit_fee_sats,
    COALESCE(withdrawal_fee_sats, 0) AS withdrawal_fee_sats,

    COALESCE(exposure_usd, 0) AS exposure_usd,
    COALESCE(liability_usd, 0) AS liability_usd,
    COALESCE(SAFE_SUBTRACT(exposure_liability_ratio ,1), 0) AS exposure_liability_ratio_minus_one,
    LAST_VALUE(usd_per_btc_mid_price IGNORE NULLS) OVER (w) AS usd_per_btc_mid_price,
  FROM pnl_with_price
  WINDOW w AS (
    ORDER BY minute
    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
  )

), pnl_explain_components AS (

  SELECT
    minute,

    funding_fee_sats,
    trading_fee_sats,
    trading_pnl_sats,
    user_pnl_sats,
    deposit_fee_sats,
    withdrawal_fee_sats,

    SAFE_ADD(
      funding_fee_sats,
      SAFE_ADD(
        trading_fee_sats,
        SAFE_ADD(
          trading_pnl_sats,
          SAFE_ADD(
            user_pnl_sats,
            SAFE_ADD(
              deposit_fee_sats,
              withdrawal_fee_sats))))) AS net_pnl_sats,

    user_fee_sats AS net_pnl_user_fees_explain_sats,

    SAFE_MULTIPLY(funding_fee_sats, exposure_liability_ratio_minus_one) AS funding_fee_mishedge_explain_sats,
    SAFE_MULTIPLY(trading_fee_sats, exposure_liability_ratio_minus_one) AS trading_fee_mishedge_explain_sats,
    SAFE_MULTIPLY(trading_pnl_sats, exposure_liability_ratio_minus_one) AS trading_pnl_mishedge_explain_sats,
    slippage_sats AS trading_pnl_slippage_explain_sats,

    usd_per_btc_mid_price,
    exposure_usd,
    liability_usd,
    1 + exposure_liability_ratio_minus_one AS exposure_liability_ratio,
  FROM pnl_coalesced

), pnl_explain AS (

  SELECT
    minute,

    funding_fee_sats,
    trading_fee_sats,
    trading_pnl_sats,
    user_pnl_sats,
    deposit_fee_sats,
    withdrawal_fee_sats,

    funding_fee_mishedge_explain_sats,
    SAFE_SUBTRACT(funding_fee_sats, funding_fee_mishedge_explain_sats) AS funding_fee_residual_explain_sats,

    trading_fee_mishedge_explain_sats,
    SAFE_SUBTRACT(trading_fee_sats, trading_fee_mishedge_explain_sats) AS trading_fee_residual_explain_sats,

    trading_pnl_mishedge_explain_sats,
    trading_pnl_slippage_explain_sats,
    SAFE_SUBTRACT(trading_pnl_sats, SAFE_ADD(trading_pnl_mishedge_explain_sats, trading_pnl_slippage_explain_sats)) AS trading_pnl_residual_explain_sats,

    net_pnl_user_fees_explain_sats,
    SAFE_SUBTRACT(net_pnl_sats, net_pnl_user_fees_explain_sats) AS net_pnl_unexplain_sats,

    net_pnl_sats,

    usd_per_btc_mid_price,
    exposure_usd,
    liability_usd,
    exposure_liability_ratio,
  FROM pnl_explain_components

)

SELECT
  minute,

  funding_fee_sats,
  trading_fee_sats,
  trading_pnl_sats,
  user_pnl_sats,
  deposit_fee_sats,
  withdrawal_fee_sats,

  funding_fee_mishedge_explain_sats,
  funding_fee_residual_explain_sats,
  trading_fee_mishedge_explain_sats,
  trading_fee_residual_explain_sats,
  trading_pnl_mishedge_explain_sats,
  trading_pnl_slippage_explain_sats,
  trading_pnl_residual_explain_sats,
  net_pnl_user_fees_explain_sats,
  net_pnl_unexplain_sats,

  net_pnl_sats,

  usd_per_btc_mid_price,
  exposure_usd,
  liability_usd,
  exposure_liability_ratio,

FROM pnl_explain
