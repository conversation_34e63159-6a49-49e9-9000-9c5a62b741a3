config {
	type: "table",
	bigquery: {
		clusterBy: ["account_id"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	accounts.id AS account_id,
	merchants.createdAt AS store_created_at,
	username AS merchant_name,
	location.coordinates,
	location.coordinates[SAFE_ORDINAL(2)] AS store_latitude,
	location.coordinates[SAFE_ORDINAL(1)] AS store_longitude,
	title AS store_name,
	validated,

FROM ${ref({
		name: "merchants",
		schema: envs.currentSchema("_galoy_raw")
	})} AS merchants
	LEFT JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} AS accounts USING (username)
