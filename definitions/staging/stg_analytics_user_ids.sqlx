config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	description: "This table is necessary to map the `user_id` field in google analytics events to users in our internal database.  The mapping is complicated because we've been changing key used internally quite a bit recently and this has resulted in events using different internal keys.",
	assertions: {
		uniqueKey: ["user_id"],
		nonNull: ["user_key"]
	},
}

SELECT user_key,
	kratos_user_id AS user_id
FROM ${ref("stg_user_accounts")}

UNION ALL

SELECT user_key,
	account_id AS user_id
FROM ${ref("stg_user_accounts")}

UNION ALL

SELECT user_key,
	account_oid AS user_id
FROM ${ref("stg_user_accounts")}
