config {
	type: "table",
	assertions: {
		uniqueKey: ["wallet_id"],
		nonNull: ["account_id", "account_name", "currency"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

SELECT DISTINCT
	w.id AS wallet_id,
	a.id AS account_id,
	role AS account_name,
	currency,

FROM (
		SELECT role, id
		FROM ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})}
		WHERE role IN UNNEST(["bankowner", "dealer", "funder"])
	) AS a
	JOIN ${ref({
		name: "wallets",
		schema: envs.currentSchema("_galoy_raw")
	})} AS w ON a.id = w.accountId
