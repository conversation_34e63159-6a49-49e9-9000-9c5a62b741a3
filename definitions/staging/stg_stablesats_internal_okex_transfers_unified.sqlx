config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH okex_transfers as (

	SELECT *
	FROM ${ref("stg_stablesats_internal_okex_transfers")}

), rt_okex_transfers as (

	SELECT *
	FROM ${ref("stg_rt_stablesats_internal_okex_transfers")}
	WHERE created_at > (SELECT MAX(created_at) FROM ${ref("stg_stablesats_internal_okex_transfers")})
)

SELECT * FROM okex_transfers

UNION ALL

SELECT * FROM rt_okex_transfers
