config {
  hasOutput: true,
}

CREATE OR REPLACE PROCEDURE ${self()}()
BEGIN

	DECLARE done ARRAY<
		STRUCT <journal_id STRING, wallet_id STRING, avg_open_sats_per_cent FLOAT64>
	>;

	DECLARE new_elements, new_rows, inner_loop_iterations INT64;

	CREATE TABLE IF NOT EXISTS ${self()} (
		journal_id STRING,
		wallet_id STRING,
		avg_open_sats_per_cent FLOAT64
	);

	SET new_rows = 9223372036854775807; -- The maximum size of an INT64 in bq

	LOOP

		CREATE OR REPLACE TEMP TABLE initial_state AS
			select l.*, r.avg_open_sats_per_cent as from_sats_per_cent
			from (
				select *,
					lag(avg_open_sats_per_cent) over(
						partition by wallet_id
						order by recorded_at, journal_id
					) as last_avg_open_sats_per_cent
				from ${ref("stg_usd_wallet_states")}
				left join ${self()} using(journal_id, wallet_id)
			) l
			left join ${self()} r
				on l.journal_id = r.journal_id and l.from_wallet_id = r.wallet_id
			where l.avg_open_sats_per_cent is null;

		IF ((SELECT count(*) FROM initial_state) >= new_rows)
		THEN break;
		END IF;
		SET new_rows = (SELECT count(*) FROM initial_state);

		-- First buys:
		SET done = ARRAY_CONCAT([], ARRAY(
			SELECT struct(journal_id, wallet_id, sats_per_cent as avg_open_sats_per_cent)
			FROM initial_state
			WHERE deposit > 0 and was_trade and (balance - deposit) <= 0
			ORDER BY recorded_at, journal_id
			LIMIT 2500
		));

		SET new_elements = -1;
		SET inner_loop_iterations = 0;

		LOOP

			IF (
				(array_length(done) >= 1000)
				or (new_elements = array_length(done))
				or (inner_loop_iterations > 250)
			)
			THEN BREAK;
			END IF;

			SET new_elements = array_length(done);
			SET inner_loop_iterations = inner_loop_iterations + 1;

			SET done = ARRAY_CONCAT(done, ARRAY(
				with state as (
					select
						l.journal_id,
						l.wallet_id,
						deposit,
						sats_per_cent,
						was_trade,
						balance,
						from_wallet_id,
						recorded_at,
						l.avg_open_sats_per_cent,
						coalesce(l.from_sats_per_cent, r.avg_open_sats_per_cent) as from_sats_per_cent,
						coalesce(
							last_avg_open_sats_per_cent,
							lag(l.avg_open_sats_per_cent) over(
								partition by l.wallet_id
								order by recorded_at, l.journal_id
							)
						) as last_avg_open_sats_per_cent
					from (
						select
							journal_id,
							wallet_id,
							deposit,
							sats_per_cent,
							was_trade,
							balance,
							from_wallet_id,
							recorded_at,
							last_avg_open_sats_per_cent,
							from_sats_per_cent,
							coalesce(
								l.avg_open_sats_per_cent,
								r.avg_open_sats_per_cent
							) as avg_open_sats_per_cent
						from initial_state l
						left join unnest(done) r using(journal_id, wallet_id)
					) l
					left join unnest(done) r
						on l.journal_id = r.journal_id
						and l.from_wallet_id = r.wallet_id
				), next_state as (
					select
						journal_id,
						wallet_id,
						CASE
							WHEN deposit < 0 THEN
								last_avg_open_sats_per_cent
							WHEN (balance - deposit) > 0 and was_trade THEN
								(
									sats_per_cent * deposit +
									last_avg_open_sats_per_cent * (balance - deposit)
								) / balance
							WHEN (balance - deposit) > 0 and not was_trade THEN
								(
									from_sats_per_cent * deposit +
									last_avg_open_sats_per_cent * (balance - deposit)
								) / balance
							WHEN (balance - deposit) <= 0 and not was_trade THEN
								from_sats_per_cent
						END as avg_open_sats_per_cent
					from state
					where avg_open_sats_per_cent is null
				)
				select struct(journal_id, wallet_id, avg_open_sats_per_cent)
				from next_state
				where avg_open_sats_per_cent is not null
				limit 1000
			));

		END LOOP;

		INSERT INTO ${self()}
		SELECT *
		FROM UNNEST(done);

	END LOOP;

END;

CALL ${self()}();
