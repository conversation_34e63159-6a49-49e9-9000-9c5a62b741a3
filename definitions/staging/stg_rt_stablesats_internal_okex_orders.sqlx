config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	client_order_id,
	correlation_id,
	instrument,
	action,
	unit,
	size,
	size_usd_value,
	target_usd_value,
	position_usd_value_before_order,
	MAX(complete) AS complete,
	MIN(lost) AS lost,
	created_at,
	MAX(order_id) AS order_id,
	MAX(avg_price) AS avg_price,
	MAX(fee) AS fee,
	MAX(state) AS state,

FROM ${ref("stg_pg_stablesats_public_okex_orders")}
GROUP BY

	client_order_id,
	correlation_id,
	instrument,
	action,
	unit,
	size,
	size_usd_value,
	target_usd_value,
	position_usd_value_before_order,
	created_at
