config {
    type: "view",
    bigquery: {
        labels: {
            execution_env: envs.current
        }
    },
    description: "Stablesats vs Galoy USD & BTC liabilities",
}

WITH stablesats_liability AS (

    SELECT
        recorded_at,
        usd_balance AS stablesats_usd_liability,
        btc_balance AS stablesats_btc_liability
    FROM ${ref("stg_stablesats_internal_liability_unified")}

), galoy_liability AS (

    SELECT
        recorded_at,
        galoy_btc_liability_cumsum AS galoy_btc_liability,
        galoy_usd_liability_cumsum AS galoy_usd_liability,
    FROM ${ref("stg_galoy_liability_unified")}

), stablesats_outflow AS (

    SELECT
      recorded_at,
      cumsum AS stablesats_btc_outflow,
    FROM
      ${ref("stg_stablesats_internal_dealer_wallet_btc_outflow_unified")}

), galoy_outflow AS (

    SELECT
      recorded_at,
      cumsum AS galoy_btc_outflow,
    FROM
      ${ref("stg_journal_entries_dealer_wallet_btc_outflow_unified")}

), joined AS (

    SELECT
        recorded_at,
        stablesats_btc_liability,
        stablesats_usd_liability,
        galoy_btc_liability,
        galoy_usd_liability,
        stablesats_btc_outflow,
        galoy_btc_outflow,
    FROM stablesats_liability
    FULL JOIN galoy_liability USING (recorded_at)
    FULL JOIN stablesats_outflow USING (recorded_at)
    FULL JOIN galoy_outflow USING (recorded_at)

), grouped AS (

    SELECT
        TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute,
        ARRAY_AGG(stablesats_btc_liability IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS stablesats_btc_liability,
        ARRAY_AGG(stablesats_usd_liability IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS stablesats_usd_liability,
        ARRAY_AGG(galoy_btc_liability IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS galoy_btc_liability,
        ARRAY_AGG(galoy_usd_liability IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS galoy_usd_liability,
        ARRAY_AGG(stablesats_btc_outflow IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS stablesats_btc_outflow,
        ARRAY_AGG(galoy_btc_outflow IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS galoy_btc_outflow,
    FROM joined
    GROUP BY minute

), final AS (

    SELECT
        minute,
        LAST_VALUE(stablesats_usd_liability IGNORE NULLS) OVER (w) AS stablesats_usd_liability,
        LAST_VALUE(galoy_usd_liability IGNORE NULLS) OVER (w) AS galoy_usd_liability,
        LAST_VALUE(stablesats_btc_liability IGNORE NULLS) OVER (w) AS stablesats_btc_liability,
        LAST_VALUE(galoy_btc_liability IGNORE NULLS) OVER (w) AS galoy_btc_liability,
        LAST_VALUE(stablesats_btc_outflow IGNORE NULLS) OVER (w) AS stablesats_btc_outflow,
        LAST_VALUE(galoy_btc_outflow IGNORE NULLS) OVER (w) AS galoy_btc_outflow,
    FROM grouped
    WINDOW w AS (
        ORDER BY minute
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    )
)

SELECT
    minute,
    stablesats_usd_liability,
    galoy_usd_liability,
    stablesats_btc_liability,
    galoy_btc_liability,
    stablesats_btc_outflow,
    galoy_btc_outflow,

    CAST(galoy_btc_liability AS BIGNUMERIC) +
    CAST(galoy_btc_outflow AS BIGNUMERIC) AS galoy_btc_wallet_balance,

    CAST(stablesats_btc_liability AS BIGNUMERIC) +
    CAST(stablesats_btc_outflow AS BIGNUMERIC) AS stablesats_btc_wallet_balance,
FROM final
