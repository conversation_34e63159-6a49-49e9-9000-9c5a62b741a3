config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	description: "Minute synchronized Spot and Swap prices from Coinbase & OKX.",
}

WITH minute_swap_prices AS (

	SELECT
		TIMESTAMP_TRUNC(TIMESTAMP_ADD(timestamp, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute,
		ANY_VALUE(sats_per_cent_market_buy HAVING MIN timestamp) AS swap_market_buy_sats_per_cent,
		ANY_VALUE(sats_per_cent_market_sell HAVING MIN timestamp) AS swap_market_sell_sats_per_cent,

	FROM ${ref("stg_minute_swap_prices")}

	WHERE sats_per_cent_market_buy IS NOT NULL
	AND sats_per_cent_market_sell IS NOT NULL

	GROUP BY minute

), minute_spot_prices AS (

	SELECT
		TIMESTAMP_TRUNC(TIMESTAMP_ADD(timestamp, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute,
		ANY_VALUE(sats_per_cent HAVING MIN timestamp) AS spot_sats_per_cent,

	FROM ${ref("stg_coincap_prices")}

	WHERE sats_per_cent IS NOT NULL

	GROUP BY minute

)

SELECT
	  minute
	, swap_market_buy_sats_per_cent
	, swap_market_sell_sats_per_cent
	, (swap_market_buy_sats_per_cent + swap_market_sell_sats_per_cent) / 2 as swap_market_mid_sats_per_cent
	, spot_sats_per_cent
FROM
	UNNEST(
		ARRAY_CONCAT(
			-- first year
			GENERATE_TIMESTAMP_ARRAY(
				'2022-06-01 00:00:00 UTC',
				'2022-12-31 23:59:00 UTC',
				INTERVAL 1 MINUTE
			),
			-- second year
			GENERATE_TIMESTAMP_ARRAY(
				'2023-01-01 00:00:00 UTC',
				'2023-12-31 23:59:00 UTC',
				INTERVAL 1 MINUTE
			),
			-- third year
			GENERATE_TIMESTAMP_ARRAY(
				'2024-01-01 00:00:00 UTC',
				'2024-12-31 23:59:00 UTC',
				INTERVAL 1 MINUTE
			),
			-- current
			GENERATE_TIMESTAMP_ARRAY(
				'2025-01-01 00:00:00 UTC',
				CURRENT_TIMESTAMP(),
				INTERVAL 1 MINUTE
			)
		)
	) as minute
	LEFT JOIN minute_swap_prices USING (minute)
	LEFT JOIN minute_spot_prices USING (minute)
