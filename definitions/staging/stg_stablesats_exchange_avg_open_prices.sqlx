config {
  type: "view",
  assertions: {
    uniqueKey: ["bill_id"]
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

WITH input_arrays AS (
  SELECT
    ARRAY_AGG(bill_id ORDER BY recorded_at, bill_id) as bill_ids,
    ARRAY_AGG(CAST(hedge_cent_deposit AS FLOAT64) ORDER BY recorded_at, bill_id) as deposits,
    ARRAY_AGG(CAST(sats_per_cent AS FLOAT64) ORDER BY recorded_at, bill_id) as open_prices,
  FROM
    ${ref("stg_stablesats_exchange_trades")}
),
output_arrays AS (
  SELECT
    bill_ids,
    ${ref("udf_avg_open_price")}(
      deposits,
      open_prices
    ) as avg_open_prices,
  FROM
    input_arrays
)
SELECT
  bill_id,
  avg_open_prices[OFFSET(o)] as avg_open_price
FROM
  output_arrays,
  UNNEST(bill_ids) AS bill_id WITH OFFSET AS o
