config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH exposure_reconciliation_series AS (

  SELECT
    CASE WHEN type = 'fee' THEN usd_exposure_reported_from_fees - usd_exposure_calculated_from_trades ELSE 0 END AS exposure_diff,
    CASE WHEN type = 'fee' THEN (usd_exposure_reported_from_fees - usd_exposure_calculated_from_trades) != 0 ELSE FALSE END AS has_exposure_diff,
    *
  FROM ${ref("stg_okex_bills_corrected")}
  WHERE type IN ('fee', 'trade')

)

SELECT
  recorded_at,
  usd_exposure_reported_from_fees,
  usd_exposure_calculated_from_trades,
  usd_exposure_change_from_trades,
  exposure_diff AS usd_exposure_diff,
  has_exposure_diff,
FROM exposure_reconciliation_series
