config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

SELECT *,
	PERCENT_RANK() OVER (ORDER BY distance) as overall_distance_rank,
	PERCENT_RANK() OVER (PARTITION BY centroid_id ORDER BY distance) as centroid_distance_rank,
	PERCENTILE_DISC(distance, 0.5) OVER (PARTITION BY centroid_id) AS median_centroid_distance

FROM (
	SELECT user_key,
		CENTROID_ID as centroid_id,
		(
			SELECT MIN(DISTANCE)
			FROM UNNEST(NEAREST_CENTROIDS_DISTANCE)
		) AS distance

	FROM ML.PREDICT(
		MODEL ${ref("stg_ml_user_clustering_model")},
		(
			SELECT * EXCEPT(n_transactions_per_day_rank, sum_usd_user_volume_per_day_rank, days_active_rank)

			FROM ${ref("stg_ml_user_transaction_summaries")}
		)
	)
)
