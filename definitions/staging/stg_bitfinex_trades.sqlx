config {
	type: "incremental",
	uniqueKey: ["trade_id"],
	bigquery: {
		clusterBy: ["recorded_at"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["recorded_at"],
		uniqueKey: ["trade_id"],
	},
}

pre_operations {
	DECLARE last_inserted_at DEFAULT (
		${when(incremental(),
			`SELECT MAX(recorded_at) FROM ${self()}`,
			`SELECT TIMESTAMP("0001-01-01 00:00:00")`
		)}
	);
}

SELECT DISTINCT
	LAX_INT64(trade[0]) AS trade_id,
	TIMESTAMP_MILLIS(LAX_INT64(trade[1])) AS recorded_at,
	LAX_FLOAT64(trade[2]) AS amount,
	LAX_FLOAT64(trade[3]) AS price,
	IF(LAX_FLOAT64(trade[2]) >= 0, "Buy", "Sell") AS direction,

FROM ${ref({
		name: "bitfinex_trades",
		schema: envs.currentSchema("_functions_raw"),
	})}
	CROSS JOIN UNNEST(JSON_EXTRACT_ARRAY(response)) AS trade

WHERE inserted_at > last_inserted_at
