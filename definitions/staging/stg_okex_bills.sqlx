config {
  type: "table",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Billing details of dealer's trades with OKX." +
    "See https://www.okx.com/docs-v5/en/#rest-api-account-get-bills-details-last-7-days",
  assertions: {
    uniqueKey: ["bill_id"]
  }
}

SELECT DISTINCT
  billId as bill_id,
  TIMESTAMP(datetime, "UTC") as recorded_at,
  type,
  amount,
  status,
  bal as balance,
  balChg as balance_change,
  fee * ********* AS sat_fee,
  execType as execution_type,
--  ordId as order_id,
  pnl * ********* AS sat_pnl,
  subType as sub_type_code,
  CASE
	WHEN subType=1 THEN "Buy"
	WHEN subType=2 THEN "Sell"
	WHEN subType=173 THEN "Funding fee expense"
	WHEN subType=174 THEN "Funding fee income"
  END AS sub_type,
  sz as size,
  mgnMode as margin_mode,
  instType as instrument_type,
--  referenceId as reference_id,
  instId as instrument_id,
  currency
FROM
  ${
    ref({
      name: "okex_bills",
      schema: envs.currentSchema("_functions_raw")
    })
  }
