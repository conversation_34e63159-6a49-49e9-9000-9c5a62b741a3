config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	id,
	buy_amount,
	buy_unit,
	sell_amount,
	sell_unit,
	external_ref,
	created_at,
	MAX(ledger_tx_id) AS ledger_tx_id,
	MAX(correction_ledger_tx_id) AS correction_ledger_tx_id,

FROM ${ref("stg_pg_stablesats_public_user_trades")}
GROUP BY
	id,
	buy_amount,
	buy_unit,
	sell_amount,
	sell_unit,
	external_ref,
	created_at
