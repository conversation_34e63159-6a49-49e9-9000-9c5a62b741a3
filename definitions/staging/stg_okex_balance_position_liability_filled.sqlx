config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Backfilled position, balance and liability timeseries.",
}

SELECT
  minute,

  LAST_VALUE(last_btc_price_in_usd IGNORE NULLS) OVER (w) AS last_btc_price_in_usd,
  LAST_VALUE(exposure_in_usd IGNORE NULLS) OVER (w) AS exposure_in_usd,
  LAST_VALUE(margin IGNORE NULLS) OVER (w) AS margin,
  LAST_VALUE(auto_deleveraging_indicator IGNORE NULLS) OVER (w) AS auto_deleveraging_indicator,
  LAST_VALUE(liquidation_price IGNORE NULLS) OVER (w) AS liquidation_price,
  LAST_VALUE(position_quantity IGNORE NULLS) OVER (w) AS position_quantity,
  LAST_VALUE(average_open_price IGNORE NULLS) OVER (w) AS average_open_price,
  LAST_VALUE(unrealized_pnl IGNORE NULLS) OVER (w) AS unrealized_pnl,
  LAST_VALUE(unrealized_pnl_ratio IGNORE NULLS) OVER (w) AS unrealized_pnl_ratio,
  LAST_VALUE(margin_ratio IGNORE NULLS) OVER (w) AS margin_ratio,
  LAST_VALUE(maintenance_margin_requirement IGNORE NULLS) OVER (w) AS maintenance_margin_requirement,
  LAST_VALUE(exchange_leverage IGNORE NULLS) OVER (w) AS exchange_leverage,
  LAST_VALUE(trading_btc_free_balance IGNORE NULLS) OVER (w) AS trading_btc_free_balance,
  LAST_VALUE(trading_btc_used_balance IGNORE NULLS) OVER (w) AS trading_btc_used_balance,
  LAST_VALUE(trading_btc_total_balance IGNORE NULLS) OVER (w) AS trading_btc_total_balance,
  LAST_VALUE(notional_lever IGNORE NULLS) OVER (w) AS notional_lever,
  LAST_VALUE(funding_btc_free_balance IGNORE NULLS) OVER (w) AS funding_btc_free_balance,
  LAST_VALUE(funding_btc_used_balance IGNORE NULLS) OVER (w) AS funding_btc_used_balance,
  LAST_VALUE(funding_btc_total_balance IGNORE NULLS) OVER (w) AS funding_btc_total_balance,
  LAST_VALUE(usd_liability IGNORE NULLS) OVER (w) AS usd_liability,
  LAST_VALUE(btc_liability IGNORE NULLS) OVER (w) AS btc_liability,
  LAST_VALUE(btc_outflow IGNORE NULLS) OVER (w) AS btc_outflow,

FROM ${ref("stg_okex_balance_position_liability_sync")}

WINDOW w AS (
	ORDER BY minute
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
