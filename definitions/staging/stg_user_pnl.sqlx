config {
  type: "view",
  description: "Hedge independent profit and loss from user trades.",
  columns: {
    user_trade_sat_pnl: "The realized profit and loss for the bank (independent of hedge) from this trade."
  },
  uniqueKey: ["journal_id", "wallet_id"],
  assertions: {
    uniqueKey: ["journal_id", "wallet_id"],
    nonNull: ["sat_user_pnl"],
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

select
  journal_id,
  wallet_id,
  abs(deposit) * avg_open_sats_per_cent - abs(deposit) * sats_per_cent as sat_user_pnl
from
  ${ref("stg_wallet_avg_open_prices")}
  left join ${ref("stg_usd_wallet_states")} using (journal_id, wallet_id)
where
  deposit < 0
  AND was_trade
