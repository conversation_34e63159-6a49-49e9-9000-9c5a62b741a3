config {
	type: "incremental",
	bigquery: {
		clusterBy: ["recorded_at", "user_id"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["user_id", "recorded_at"]
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20221218"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT user_id,
	TIMESTAMP_MICROS(event_timestamp) AS recorded_at,
	_TABLE_SUFFIX AS table_suffix,
	(
		SELECT value.int_value
		FROM unnest(event_params)
		WHERE key = "ga_session_id"
	) AS session_id,
	(
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "ga_session_number"
	) AS session_number,
	(
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "engaged_session_event"
	) AS session_event_number,
	(
		SELECT value.string_value
		FROM UNNEST(event_params)
		WHERE key = "firebase_screen"
	) AS screen,
	(
		SELECT value.string_value
		FROM UNNEST(event_params)
		WHERE key = "firebase_screen_class"
	) AS screen_class,
	(
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "engagement_time_msec"
	) AS engagement_time_msec,

FROM `${envs.project}.${envs.currentAnalyticsSchema()}.events_*`

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)
	AND event_name = "screen_view"
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "network" AND value.string_value = "${envs.currentNetwork()}"
	)
	AND user_id IS NOT NULL
	AND EXISTS(
		SELECT 1
		FROM UNNEST(event_params)
		WHERE key = "firebase_screen" and value.string_value IS NOT NULL
	)
