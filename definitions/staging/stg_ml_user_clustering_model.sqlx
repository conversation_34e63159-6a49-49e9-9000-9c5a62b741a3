config {
	hasOutput: true,
}

CREATE TABLE IF NOT EXISTS `${schema()}.stg_ml_user_clustering_model_timestamp` AS
	SELECT DATE_SUB(CURRENT_DATE(), INTERVAL 8 DAY) AS ts;

IF (SELECT ts FROM `${schema()}.stg_ml_user_clustering_model_timestamp`) > DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
THEN RETURN;
END IF;

CREATE OR REPLACE TABLE `${schema()}.stg_ml_user_clustering_model_timestamp` AS
	SELECT CURRENT_DATE() AS ts;

CREATE OR REPLACE MODEL ${self()}
OPTIONS(
	MODEL_TYPE = 'kmeans',
	KMEANS_INIT_METHOD = 'KMEANS++',
	STANDARDIZE_FEATURES = TRUE,
	NUM_CLUSTERS = 11

	-- Hyperparameter tuning:
	--NUM_TRIALS = 16,
	--NUM_CLUSTERS = HPARAM_RANGE(3,17)
) AS

SELECT * EXCEPT(user_key, n_transactions_per_day_rank, sum_usd_user_volume_per_day_rank, days_active_rank)

FROM ${ref("stg_ml_user_transaction_summaries")}

WHERE n_transactions_per_day_rank < 0.999
	AND sum_usd_user_volume_per_day_rank < 0.999
	AND days_active_rank < 0.999
