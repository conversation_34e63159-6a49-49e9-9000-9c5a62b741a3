config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH liability as (

	SELECT *
	FROM ${ref("stg_stablesats_internal_liability")}

)

SELECT * FROM liability

------------------------------------------------------------------------
-- Disable real-time liability while troubleshooting kafka table
------------------------------------------------------------------------

-- , rt_liability as (

-- 	SELECT *
-- 	FROM ${ref("stg_rt_stablesats_internal_liability")}
-- 	WHERE version > (SELECT MAX(version) FROM ${ref("stg_stablesats_internal_liability")})
-- )

-- SELECT * FROM liability

-- UNION ALL

-- SELECT * FROM rt_liability
