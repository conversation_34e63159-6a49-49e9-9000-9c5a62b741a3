config {
	type: "view",
}

SELECT day, user_key, created_at, spot_close_sats_per_cent,
	SUM(COALESCE(sat_bitcoin_balance_change, 0)) OVER (c) AS sat_bitcoin_close_balance,
	SUM(COALESCE(cent_stablesats_balance_change, 0)) OVER (c) AS cent_stablesats_close_balance,
	LAST_VALUE(previous_recorded_at IGNORE NULLS) OVER (c) AS previous_recorded_at,

FROM ${ref("stg_user_days")}
	LEFT JOIN ${ref("stg_daily_user_transaction_summaries")} USING (day, user_key)

WINDOW c AS (
	PARTITION BY user_key
	ORDER BY day
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
