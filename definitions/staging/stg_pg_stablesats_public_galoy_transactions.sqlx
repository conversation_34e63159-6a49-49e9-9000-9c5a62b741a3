config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

    id,
    cursor,
    is_paired,
    CAST(settlement_amount AS BIGNUMERIC) AS settlement_amount,
    settlement_currency,
    settlement_method,
    direction,
    memo,
    CAST(cents_per_unit AS BIGNUMERIC) AS cents_per_unit,
    CAST(amount_in_usd_cents AS BIGNUMERIC) AS amount_in_usd_cents,
    created_at,

FROM ${ref({
	name: "pg_stablesats_public_galoy_transactions",
	schema: envs.currentSchema("_kafka_raw")
})}
WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
