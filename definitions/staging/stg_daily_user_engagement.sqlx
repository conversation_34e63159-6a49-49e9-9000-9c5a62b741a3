config {
	type: "table",
	bigquery: {
		clusterBy: ["day", "user_key"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["user_key", "day"],
		nonNull: ["user_key", "day"]
	},
}

SELECT user_key, day,
	SUM(engagement_time_msec/1000) AS engagement_time_sec

FROM ${ref("stg_daily_user_engagement_raw")}
	INNER JOIN ${ref("stg_analytics_user_ids")} USING(user_id)

GROUP BY user_key, day
