config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}


WITH unpaired_btc_journal_entries AS(

	SELECT
		ARRAY_AGG(deposit IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)] AS dealer_sat_deposit,
		ANY_VALUE(recorded_at) AS recorded_at,

	FROM ${ref("stg_journal_entries")}

	WHERE recorded_at > "2022-06-01"
		AND wallet_id IN (
			SELECT wallet_id
			FROM ${ref("stg_static_wallets")}
			WHERE account_name="dealer"
		)
		AND not cross_asset
		AND currency = 'BTC'

	GROUP BY journal_id

	HAVING count(*) = 1

)

SELECT
	recorded_at,
	dealer_sat_deposit / ********* AS galoy_btc_outflow,
FROM unpaired_btc_journal_entries
