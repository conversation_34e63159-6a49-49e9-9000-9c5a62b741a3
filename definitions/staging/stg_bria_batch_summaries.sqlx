config {
	type: "table",
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		},
	},
}

SELECT
	batch_id,
	wallet_id	AS bria_wallet_id,
	current_keychain_id,
	total_in_sats,
	total_spent_sats,
	change_sats,
	change_address,
	change_vout,
	total_fee_sats,
	batch_created_ledger_tx_id,
	batch_broadcast_ledger_tx_id,
	created_at,
	modified_at,
	cpfp_fee_sats,
	PARSE_JSON(cpfp_details) AS cpfp_details,

FROM ${ref({
	name: "bria_batch_wallet_summaries",
	schema: envs.currentSchema("_bria_raw")
})}
