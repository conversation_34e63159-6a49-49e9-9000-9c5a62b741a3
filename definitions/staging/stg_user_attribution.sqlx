config {
	type: "table",
	bigquery: {
		clusterBy: ["user_key"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["user_key"],
		nonNull: ["user_key"]
	},
}

SELECT user_key,
	MAX(attribution_medium) AS attribution_medium,
	MAX(attribution_source) AS attribution_source,
	MAX(attribution_term) AS attribution_term,
	MAX(attribution_campaign) AS attribution_campaign,
	MAX(attribution_content) AS attribution_content,

FROM ${ref("stg_pseudo_user_attribution")}
	INNER JOIN ${ref("stg_analytics_pseudo_users")} USING (first_open_time, user_pseudo_id)
	INNER JOIN ${ref("stg_analytics_user_ids")} USING (user_id)

GROUP BY user_key
