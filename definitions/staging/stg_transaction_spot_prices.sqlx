config {
  type: "table",
  assertions: {
    uniqueKey: ["journal_id", "wallet_id"],
    nonNull: ["journal_id", "wallet_id", "sats_per_cent"],
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Spot price annotations for ledger transactions."
}

WITH transactions AS (
  SELECT
    recorded_at AS time,
    journal_id,
    wallet_id
  FROM
    ${ref("stg_journal_entries")}
    INNER JOIN ${ref("stg_user_wallets")} USING (wallet_id)
),
prices as (
  SELECT
    timestamp AS time,
    sats_per_cent
  FROM
    ${ref("stg_coincap_prices")}
),
distinct_price_times as (
  SELECT
    DISTINCT time
  FROM
    prices
),
arrayed_price_times AS (
  SELECT
    ARRAY_AGG(time) as times,
    DATE(time) as day
  FROM
    distinct_price_times
  GROUP BY
    day
),
distinct_transaction_times AS (
  SELECT
    DISTINCT time
  FROM
    transactions
),
arrayed_transaction_times AS (
  SELECT
    ARRAY_AGG(time) as times,
    DATE(time) as day
  FROM
    distinct_transaction_times
  GROUP BY
    day
),
asof_price_transaction_time_tuples AS (
  SELECT
    ${ref("udf_asof_join")}(
      arrayed_price_times.times,
      arrayed_transaction_times.times
    ) as asof_tuples,
    arrayed_price_times.day as day
  FROM
    arrayed_price_times
    JOIN arrayed_transaction_times USING (day)
),
unnested_times AS (
  SELECT
    a.x as price_time,
    a.y as transaction_time
  FROM
    asof_price_transaction_time_tuples,
    UNNEST(asof_price_transaction_time_tuples.asof_tuples) as a
)
SELECT DISTINCT
  journal_id,
  wallet_id,
  sats_per_cent,
FROM
  unnested_times as u
  JOIN prices as l ON u.price_time = l.time
  FULL JOIN transactions as t ON u.transaction_time = t.time
