config {
	type: "incremental",
	uniqueKey: ["payment_request"],
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH prs AS (

	SELECT DISTINCT created_at, payment_request

	FROM ${ref("stg_lightning_payments")}

	WHERE payment_request IS NOT NULL
		AND payment_request != ""
		${when(incremental(), `AND payment_hash NOT IN (SELECT payment_hash FROM ${self()})`) }

), parsed AS (

	SELECT created_at, payment_request,
		${ref("udf_parse_payment_request")}(payment_request).*

	FROM prs

)

SELECT *,
	CASE
		WHEN ARRAY_LENGTH(routing_hints) > 0
		THEN ARRAY(
			SELECT DISTINCT route[ORDINAL(1)].pubkey
			FROM UNNEST(routing_hints)
			WHERE ARRAY_LENGTH(route) > 0
			ORDER BY pubkey
		)
	END AS pseudo_destination,

FROM parsed
