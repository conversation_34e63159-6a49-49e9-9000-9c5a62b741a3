config {
	type: "table",
	bigquery: {
		clusterBy: ["user_key"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["user_key"],
		nonNull: ["user_key"]
	},
}

SELECT user_key,
	ANY_VALUE(device_brand HAVING MAX updated_on) AS device_brand,
	ANY_VALUE(device_model HAVING MAX updated_on) AS device_model,
	ANY_VALUE(device_os HAVING MAX updated_on) AS device_os,
	ANY_VALUE(device_os_version HAVING MAX updated_on) AS device_os_version,
	ANY_VALUE(device_language HAVING MAX updated_on) AS device_language,
	ANY_VALUE(device_is_limited_ad_tracking HAVING MAX updated_on) AS device_is_limited_ad_tracking,
	ANY_VALUE(device_continent HAVING MAX updated_on) AS device_continent,
	ANY_VALUE(device_sub_continent HAVING MAX updated_on) AS device_sub_continent,
	ANY_VALUE(device_country HAVING MAX updated_on) AS device_country,
	ANY_VALUE(device_city HAVING MAX updated_on) AS device_city,
	ANY_VALUE(device_metro HAVING MAX updated_on) AS device_metro,
	ANY_VALUE(app_version HAVING MAX updated_on) AS app_version,
	ANY_VALUE(app_install_source HAVING MAX updated_on) AS app_install_source,
	MAX(updated_on) AS last_interaction_recorded_at,

FROM ${ref("stg_user_app_info_raw")}
	INNER JOIN ${ref("stg_analytics_user_ids")} USING(user_id)

GROUP BY user_key
