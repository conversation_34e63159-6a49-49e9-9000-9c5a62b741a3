config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["kratos_user_id", "credential_name"],
	},
	description: "Summary of wallets that participate in transactions, excluding static_wallets.",
}

SELECT created_at, updated_at,
	identity_id AS kratos_user_id,
	name AS credential_name,

FROM ${ref({
		name: "identity_credentials",
		schema: envs.currentSchema("_kratos_raw")
	})}
