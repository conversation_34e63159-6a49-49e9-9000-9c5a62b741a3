config {
	type: "table",
	assertions: {
		uniqueKey: ["journal_id"],
		nonNull: ["journal_id", "recorded_at", "dealer_sat_deposit", "dealer_cent_deposit"],
		rowConditions: [
			"IF(dealer_sat_deposit >= 0, dealer_cent_deposit <= 0, dealer_cent_deposit >= 0)"
		]
	},
	dependencies: ["assert_wallet_journal_entries"],
	bigquery: {
		partitionBy: "DATE(recorded_at)",
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	journal_id,
	ANY_VALUE(recorded_at) AS recorded_at,
	ARRAY_AGG(IF(currency="BTC", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)] AS dealer_sat_deposit,
	ARRAY_AGG(IF(currency="USD", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)] AS dealer_cent_deposit,
	SAFE_DIVIDE(
		ABS(ARRAY_AGG(IF(currency="BTC", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)]),
		ABS(ARRAY_AGG(IF(currency="USD", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)])
	) AS sats_per_cent

FROM ${ref("stg_journal_entries")}

WHERE recorded_at > "2022-06-01"
	AND wallet_id IN (
		SELECT wallet_id
		FROM ${ref("stg_static_wallets")}
		WHERE account_name="dealer"
	)
	AND cross_asset

GROUP BY journal_id

HAVING count(*) = 2
