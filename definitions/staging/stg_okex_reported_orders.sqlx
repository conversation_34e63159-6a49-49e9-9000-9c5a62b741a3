config {
  type: "table",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Exchange reported orders",
}

WITH minute_okex_orders AS (

  SELECT
    TIMESTAMP_TRUNC(recorded_at, MINUTE, "UTC") AS minute,
    quantity,
  FROM ${ref("stg_okex_orders")}

), grouped_orders AS (

  SELECT
      minute,
      SUM(quantity) AS quantity,
  FROM minute_okex_orders
  GROUP BY minute

), final AS (

  SELECT
    minute,
    CASE WHEN quantity > 0 THEN 'buy' ELSE 'sell' END AS action,
    quantity,
  FROM grouped_orders

)

SELECT
  minute,
  action,
  quantity,
FROM
  final
