config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	SUM(CASE WHEN account_id = '********-1000-0000-0000-************' AND currency = 'USD' THEN settled_cr_balance - settled_dr_balance END) as usd_balance,
	SUM(CASE WHEN account_id = '********-2000-0000-0000-************' AND currency = 'BTC' THEN settled_cr_balance - settled_dr_balance END) as btc_balance,
	version,
	MAX(modified_at) as recorded_at

FROM ${ref({
	name: "sqlx_ledger_balances",
	schema: envs.currentSchema("_stablesats_raw")
})}
WHERE account_id in
(
	'********-1000-0000-0000-************', -- STABLESATS_OMNIBUS
	'********-2000-0000-0000-************'  -- STABLESATS_BTC_WALLET
)
GROUP BY version, modified_at
