config {
	type: "table",
	assertions: {
	},
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		}
	}
}

WITH phone_keys AS (

	SELECT kratos_user_id,
		ANY_VALUE(phone_key HAVING MAX in_users) AS phone_key,
		ARRAY_AGG(IF(deleted, phone_key, NULL) IGNORE NULLS) deleted_phone_keys,
		ARRAY_AGG(IF(deleted, alpha_2_code, NULL) IGNORE NULLS) deleted_alpha_2_codes,
		ANY_VALUE(alpha_2_code HAVING MAX in_users) AS alpha_2_code,

	FROM ${ref("stg_account_phones")}

	GROUP BY kratos_user_id

), email_keys AS (

	SELECT kratos_user_id,
		ANY_VALUE(email_key HAVING MAX in_identities) AS email_key,
		ARRAY_AGG(IF(deleted, email_key, NULL) IGNORE NULLS) deleted_email_keys,

	FROM ${ref("stg_account_emails")}

	GROUP BY kratos_user_id

)

SELECT
	userId AS kratos_user_id,
	deviceId AS device_id,
	--deletedDeviceId AS deleted_device_id,
	language,
	--role,
	deviceTokens AS device_tokens,
	createdAt AS created_at,
	revision,
	COALESCE(by_code.country_short_name, by_code.country_name, by_libphonenumber.country_short_name, by_libphonenumber.country_name) AS phone_number_country_name,
	COALESCE(by_code.alpha_2_code, phone_keys.alpha_2_code) AS phone_number_country_code,
	phone_key,
	email_key,
	deleted_phone_keys,
	deleted_alpha_2_codes AS deleted_phone_number_country_codes,
	deleted_email_keys,

FROM ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})}
	LEFT JOIN ${ref("stg_countries")} AS by_code ON phoneMetadata.countryCode = by_code.alpha_2_code
	LEFT JOIN ${ref("stg_countries")} AS by_libphonenumber USING (alpha_2_code)
	LEFT JOIN phone_keys ON userId = kratos_user_id
	LEFT JOIN email_keys USING (kratos_user_id)
