config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

WITH partitioned AS (
	SELECT *,
		COUNT(*) OVER (PARTITION BY long_partition) AS long_partition_size,
		COUNT(*) OVER (PARTITION BY short_partition) AS short_partition_size
	FROM (
		SELECT
			mishedge_id,
			balance,
			deposit,
			long_balance,
			short_balance,
			long_deposit,
			short_deposit,
			sats_per_cent,
			SUM(IF(long_balance=long_deposit, 1, 0)) OVER (
				ORDER BY mishedge_id
				ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
			) AS long_partition,
			SUM(IF(short_balance=short_deposit, 1, 0)) OVER (
				ORDER BY mishedge_id
				ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
			) AS short_partition
		FROM ${ref("stg_stablesats_mishedge")}
	)
),
long_input_arrays AS (
  SELECT
    long_partition,
    ARRAY_AGG(mishedge_id ORDER BY mishedge_id) as mishedge_ids,
    ARRAY_AGG(CAST(long_deposit AS FLOAT64) ORDER BY mishedge_id) as deposits,
    ARRAY_AGG(CAST(sats_per_cent AS FLOAT64) ORDER BY mishedge_id) as sats_per_cent
  FROM
    partitioned
  WHERE long_partition_size > 1
  GROUP BY long_partition
),
short_input_arrays AS (
  SELECT
    short_partition,
    ARRAY_AGG(mishedge_id ORDER BY mishedge_id) as mishedge_ids,
    ARRAY_AGG(CAST(short_deposit AS FLOAT64) ORDER BY mishedge_id) as deposits,
    ARRAY_AGG(CAST(sats_per_cent AS FLOAT64) ORDER BY mishedge_id) as sats_per_cent
  FROM
    partitioned
  WHERE short_partition_size > 1
  GROUP BY short_partition
),
long_output_arrays AS (
  SELECT
    mishedge_ids,
    ${ref("udf_avg_open_price")}(
      deposits,
      sats_per_cent
    ) as avg_open_sats_per_cent,
  FROM long_input_arrays
),
short_output_arrays AS (
  SELECT
    mishedge_ids,
    ${ref("udf_avg_open_price")}(
      deposits,
      sats_per_cent
    ) as avg_open_sats_per_cent,
  FROM short_input_arrays
),
long_unnested AS (
  SELECT
    mishedge_id,
    avg_open_sats_per_cent[OFFSET(o)] as long_avg_open_sats_per_cent
  FROM
    long_output_arrays,
    UNNEST(mishedge_ids) AS mishedge_id WITH OFFSET AS o
),
short_unnested AS (
  SELECT
    mishedge_id,
    avg_open_sats_per_cent[OFFSET(o)] as short_avg_open_sats_per_cent
  FROM
    short_output_arrays,
    UNNEST(mishedge_ids) AS mishedge_id WITH OFFSET AS o
)
SELECT *
FROM long_unnested
  FULL JOIN short_unnested USING (mishedge_id)
