config {
	type: "table",
	assertions: {
		nonNull: [
			"kratos_user_id",
			"email_key",
		],
	},
	bigquery: {
		clusterBy: ["kratos_user_id"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH emails AS (

	SELECT email
	FROM ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})}, UNNEST(deletedEmail) AS email

	UNION DISTINCT

	SELECT JSON_VALUE(traits, '$.email') AS email,
	FROM ${ref({
		name: "identities",
		schema: envs.currentSchema("_kratos_raw")
	})}

), email_keys AS (

	SELECT email,
		ROW_NUMBER() OVER () AS email_key,

	FROM emails

	WHERE email IS NOT NULL

), users_deleted AS (


	SELECT userId AS kratos_user_id,
		email_key,
		TRUE AS deleted,

	FROM ${ref({
			name: "users",
			schema: envs.currentSchema("_galoy_raw")
		})}, UNNEST(deletedEmail) AS email
		JOIN email_keys USING (email)

	WHERE userId IS NOT NULL


), identities AS (

	SELECT id AS kratos_user_id,
		email_key,
		TRUE AS in_identities,

	FROM ${ref({
			name: "identities",
			schema: envs.currentSchema("_kratos_raw")
		})}
		JOIN email_keys ON email = JSON_VALUE(traits, '$.email')

)

SELECT kratos_user_id, email_key,
	COALESCE(deleted, FALSE) AS deleted,
	COALESCE(in_identities, FALSE) AS in_identities,
	COUNT(DISTINCT kratos_user_id) OVER (PARTITION BY email_key) AS users_with_this_email,

FROM users_deleted
	FULL JOIN identities USING (kratos_user_id, email_key)
