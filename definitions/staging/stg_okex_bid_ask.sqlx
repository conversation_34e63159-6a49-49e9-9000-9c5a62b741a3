config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Bid ask historical prices from OKX." +
    "See https://www.okx.com/docs-v5/en/#rest-api-market-data-get-ticker",
  assertions: {
    nonNull: ["ask_price", "timestamp", "bid_price", "inst_id", "inst_type"]
  }
}

SELECT
  ts as timestamp,
  last as last_price,
  lastSz as last_size,
  bidPx as bid_price,
  bidSz as bid_size,
  askPx as ask_price,
  askSz as ask_size,
  instType as inst_type,
  instId as inst_id
FROM
  ${
    ref({
      name: "okex_bid_ask",
      schema: envs.currentSchema("_functions_raw")
    })
  }
