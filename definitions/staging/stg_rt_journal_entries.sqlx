config {
	type: "view",
	materialized: true,
	assertions: {
		uniqueKey: ["journal_entry_id"],
		nonNull: [
			"recorded_at",
			"journal_entry_id",
			"journal_id",
			"deposit",
		],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

js {
	const currentDate = new Date().toISOString().slice(0, 10);
}

SELECT DISTINCT
	fullDocument.timestamp AS recorded_at,
	JSON_VALUE(fullDocument._id, '$."$oid"') AS journal_entry_id,
	JSON_VALUE(fullDocument._journal, '$."$oid"') AS journal_id,

	CAST(fullDocument.credit - fullDocument.debit AS INTEGER) AS deposit,

	fullDocument.centsAmount AS cents_amount,
	fullDocument.satsAmount AS sats_amount,
	fullDocument.centsFee AS cents_fee,
	fullDocument.satsFee AS sats_fee,

	fullDocument.accounts,
	CASE
		WHEN fullDocument.account_path[SAFE_ORDINAL(1)] = "Liabilities"
		THEN fullDocument.account_path [SAFE_ORDINAL(2)]
	END AS wallet_id,

	CASE
		WHEN fullDocument.type = "invoice" THEN "Lightning"
		WHEN fullDocument.type = "payment" THEN "Lightning"
		WHEN fullDocument.type = "onchain_payment" THEN "Onchain"
		WHEN fullDocument.type = "onchain_receipt" THEN "Onchain"
		ELSE "Intra-ledger"
	END AS settlement_method,
	CASE
		WHEN fullDocument.type = "invoice" THEN "Inbound"
		WHEN fullDocument.type = "onchain_receipt" THEN "Inbound"
		WHEN fullDocument.type = "payment" THEN "Outbound"
		WHEN fullDocument.type = "onchain_payment" THEN "Outbound"
		ELSE "Inter-wallet transfer"
	END AS bank_direction,
	CASE
		WHEN fullDocument.credit > 0 THEN "Inbound"
		ELSE "Outbound"
	END AS wallet_direction,

	fullDocument.type,
	fullDocument.currency,
	COALESCE(fullDocument.pending, FALSE) AS pending,
	COALESCE(fullDocument.voided, FALSE) AS voided,
	fullDocument.memo,

	fullDocument.payee_addresses[SAFE_ORDINAL(1)] AS payee_address,

FROM ${ref({
		name: "mongodb_galoy_medici_transactions",
		schema: envs.currentSchema("_kafka_raw")
	})}

WHERE _PARTITIONTIME > TIMESTAMP_SUB("${currentDate}", INTERVAL 2 DAY)
	AND operationType = "insert"
