config {
  type: "table",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  assertions: {
    uniqueKey: ["wallet_id"],
    nonNull: ["wallet_id", "account_id", "currency"],
  },
  description: "Summary of wallets that participate in transactions, excluding static_wallets.",
  columns: {
    wallet_id: "The id of the wallet, same as stg_journal_entries.wallet_id.",
    currency: "The wallet's, same as stg_journal_entries.currency for this wallet's journal entries.",
    account_id: "The id of the owner of this wallet."
  },
}

SELECT
  id AS wallet_id,
  currency,
  accountId AS account_id,
  type as wallet_type,
  onchain
FROM
  ${
    ref({
      name: "wallets",
      schema: envs.currentSchema("_galoy_raw")
    })
  }
