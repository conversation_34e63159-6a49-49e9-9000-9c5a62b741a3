config {
	type: "table",
	assertions: {
		uniqueKey: ["journal_entry_id"],
		nonNull: [
			"recorded_at",
			"journal_entry_id",
			"journal_id",
			"deposit",
			"balance",
		],
	},
	bigquery: {
		partitionBy: "DATE(recorded_at)",
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	recorded_at,
	journal_entry_id,
	journal_id,

	deposit,

	sats_amount,
	cents_amount,
	sats_fee,
	cents_fee,

	accounts,
	CASE
		WHEN account_path[SAFE_ORDINAL(1)] = "Liabilities"
		THEN account_path[SAFE_ORDINAL(2)]
	END AS wallet_id,

	ROW_NUMBER() OVER (
		PARTITION BY accounts
		ORDER BY recorded_at, journal_id, journal_entry_id
	) AS wallet_transaction_number,

	SUM(deposit) OVER (
		PARTITION BY accounts
		ORDER BY recorded_at, journal_id, journal_entry_id
		ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
	) as balance,

	CASE
		WHEN type = "invoice" THEN "Lightning"
		WHEN type = "payment" THEN "Lightning"
		WHEN type = "onchain_payment" THEN "Onchain"
		WHEN type = "onchain_receipt" THEN "Onchain"
		ELSE "Intra-ledger"
	END as settlement_method,
	CASE
		WHEN type = "invoice" THEN "Inbound"
		WHEN type = "onchain_receipt" THEN "Inbound"
		WHEN type = "payment" THEN "Outbound"
		WHEN type = "onchain_payment" THEN "Outbound"
		ELSE "Inter-wallet transfer"
	END as bank_direction,
	CASE
		WHEN credit > 0 THEN "Inbound"
		ELSE "Outbound"
	END as wallet_direction,

	COUNT(DISTINCT journal_entry_id) OVER (PARTITION BY journal_id) AS entries_in_journal,
	COUNT(DISTINCT currency) OVER (PARTITION BY journal_id) > 1 AS cross_asset,

	type,
	currency,
	voided,
	original_journal,
	memo,
	transaction_hash,
	payee_addresses,
	pending,
	payout_id,
	vout,

FROM ${ref("stg_journal_entries_raw")}
