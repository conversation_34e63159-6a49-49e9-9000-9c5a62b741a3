config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH user_wallet_keys AS (

	SELECT id AS wallet_id,
		ROW_NUMBER() OVER () AS user_wallet_key

	FROM (SELECT DISTINCT id FROM ${ref({
		name: "wallets",
		schema: envs.currentSchema("_galoy_raw")
	})})

)

SELECT *

FROM ${ref("stg_wallets")}
	LEFT JOIN user_wallet_keys USING (wallet_id)

WHERE NOT account_id IN (SELECT account_id FROM ${ref("stg_static_accounts")})
