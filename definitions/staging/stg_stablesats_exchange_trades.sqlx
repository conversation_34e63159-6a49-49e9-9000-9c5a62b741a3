config {
  type: "view",
  assertions: {
    uniqueKey: ["bill_id"],
    nonNull: ["hedge_cent_deposit","sub_type","recorded_at","sat_fee","sat_pnl"],
    rowConditions: [
      "execution_type in UNNEST([\"M\", \"T\"])",
      "IF(sub_type=\"Buy\", hedge_cent_deposit < 0, hedge_cent_deposit > 0)",
      "IF(hedge_cent_deposit > 0, sats_per_cent is not null, true)"
    ]
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
}

WITH low_fee_exceptions AS (

-- 2023-07-31 when usd balance > 100k but charged high fees by OKX
	SELECT 606079229008465923 AS bill_id UNION ALL
  SELECT 606079229008465924 UNION ALL
  SELECT 606079229008465925 UNION ALL
  SELECT 606119756286316547 UNION ALL
  SELECT 606119756286316549 UNION ALL
  SELECT 606119756286316550 UNION ALL
  SELECT 606119756286316551 UNION ALL
  SELECT 606119756286316552 UNION ALL
  SELECT 606291098902122496 UNION ALL

-- 2023-10-31 when usd balance > 100k but charged high fees by OKX
  SELECT 639388834140450825 UNION ALL
  SELECT 639388834140450830 UNION ALL
  SELECT 639388834140450834 UNION ALL

-- 2023-11-21 14:09:39.684000 UTC
  SELECT 647196960344428544 UNION ALL

-- 2023-11-30 when usd balance > 100k but charged high fees by OKX
  SELECT 650460380992827395 UNION ALL
  SELECT 650460380992827399 UNION ALL
  SELECT 650460380992827400 UNION ALL
  SELECT 650460380992827401 UNION ALL
  SELECT 650460380992827402 UNION ALL
  SELECT 650460380992827405 UNION ALL
  SELECT 650460380992827407 UNION ALL
  SELECT 650460380992827410 UNION ALL
  SELECT 650461405074092047

), high_fee_exceptions AS (

-- 2022-11-17
  SELECT 513287289636810758 AS bill_id UNION ALL
  SELECT 513287289636810759 UNION ALL
  SELECT 513287289636810762 UNION ALL
  SELECT 513287289636810763 UNION ALL
  SELECT 513287289636810764 UNION ALL
  SELECT 513287289636810765 UNION ALL
  SELECT 513287289636810766 UNION ALL
  SELECT 513287289636810768 UNION ALL
  SELECT 513287289636810773 UNION ALL
  SELECT 513506014155665408 UNION ALL

-- 2022-11-17 16:09:34.255000 UTC
  SELECT 513506014155665409 UNION ALL
  SELECT 513506014155665410 UNION ALL
  SELECT 513506014155665411 UNION ALL
  SELECT 513506014155665412 UNION ALL
  SELECT 513604727708479499 UNION ALL
  SELECT 513604727708479501 UNION ALL
  SELECT 513604727708479502 UNION ALL
  SELECT 513604727708479503 UNION ALL

-- 2023-08-01
  SELECT 606540034153828358 UNION ALL
  SELECT 606540034153828359 UNION ALL
  SELECT 606540034153828360 UNION ALL

-- 2023-10-26 06:28:21.968000 UTC
  SELECT 637658787083907073 UNION ALL
  SELECT 637658787083907077 UNION ALL

-- 2022-11-16
  SELECT 645180608968577031 UNION ALL
  SELECT 645180608968577034 UNION ALL
  SELECT 645180608968577036 UNION ALL
  SELECT 645180608968577039 UNION ALL
  SELECT 645180912657158153 UNION ALL
  SELECT 645180912657158160 UNION ALL
  SELECT 645180912657158168 UNION ALL
  SELECT 645191037702819841 UNION ALL
  SELECT 645191037702819846 UNION ALL
  SELECT 645191037702819850 UNION ALL
  SELECT 645191037702819854 UNION ALL
  SELECT 645195478355070976 UNION ALL
  SELECT 645195478355070981 UNION ALL
  SELECT 645195478355070985 UNION ALL
  SELECT 645197779140530180 UNION ALL
  SELECT 645197779140530181 UNION ALL
  SELECT 646909825007669252 UNION ALL
  SELECT 646909825007669253 UNION ALL
  SELECT 646909825007669254 UNION ALL
  SELECT 646909825007669255

)


SELECT
  bill_id,
  recorded_at,
  sub_type,
  execution_type,
  hedge_cent_deposit,
  sat_fee,
  sat_pnl,
  SUM(hedge_cent_deposit) OVER (
    ORDER BY recorded_at
    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
  ) as hedge_cent_balance,
  -- We can infer the open price from the fact that the fee is
  -- 5bps per share for Taker orders
  -- and 2bps per share for Maker orders
  -- The open price only matters when we increase our position,
  -- (i.e. when we "Sell" bitcoin).

  CASE
	WHEN execution_type="T"
		THEN ABS(sat_fee / hedge_cent_deposit)  * IF((lia.exchange_usd_total_balance >= 100000 OR bill_id IN (SELECT bill_id FROM high_fee_exceptions)) AND bill_id NOT IN (SELECT bill_id FROM low_fee_exceptions), 3333, 2000)
	WHEN execution_type="M"
		THEN ABS(sat_fee / hedge_cent_deposit)  * IF((lia.exchange_usd_total_balance >= 100000 OR bill_id IN (SELECT bill_id FROM high_fee_exceptions)) AND bill_id NOT IN (SELECT bill_id FROM low_fee_exceptions), 10000, 5000)
  END AS sats_per_cent,

  lia.exchange_usd_total_balance
FROM (
  SELECT
    bill_id,
    recorded_at,
    sub_type,
--    order_id,
    execution_type,
    -- Each contract is for $100 so to convert to cents multiply by 10,000:
    CASE
      WHEN sub_type="Buy" THEN size * -10000
      WHEN sub_type="Sell" THEN size * 10000
    END AS hedge_cent_deposit,
    sat_fee,
    sat_pnl
  FROM
    ${ref("stg_okex_bills_corrected")}
  WHERE
    type = "trade"
    AND recorded_at > "2022-06-01"
    AND status = "ok"
    AND margin_mode = "cross"
    AND instrument_type = "SWAP"
    AND currency = "BTC"
    AND instrument_id = "BTC-USD-SWAP"
    AND sub_type in ("Buy", "Sell")
)
LEFT JOIN ${ref("stg_dealer_metrics")} AS lia ON lia.minute = TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC")
