config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["kratos_user_id"],
		nonNull: ["email_set", "phone_set", "username_set","kratos_user_id"],
	},
	description: "Summary of wallets that participate in transactions, excluding static_wallets.",
}

SELECT created_at, updated_at, state, state_changed_at,
	id AS kratos_user_id,
	JSON_VALUE(traits, '$.email') IS NOT NULL AS email_set,
	JSON_VALUE(traits, '$.phone') IS NOT NULL AS phone_set,
	JSON_VALUE(traits, '$.username') IS NOT NULL AS username_set,

FROM ${ref({
		name: "identities",
		schema: envs.currentSchema("_kratos_raw")
	})}
