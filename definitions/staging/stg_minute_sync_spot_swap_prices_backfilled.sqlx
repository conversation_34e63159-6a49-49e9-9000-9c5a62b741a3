config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	description: "Backfilled minute synchronized Spot and Swap prices from Coinbase & OKX.",
}

WITH prices AS (

	SELECT
		  minute
		, swap_market_buy_sats_per_cent
		, swap_market_sell_sats_per_cent
		, swap_market_mid_sats_per_cent
		, spot_sats_per_cent

	FROM ${ref("stg_minute_sync_spot_swap_prices")}

), backfilled_prices AS (

	SELECT
		  minute
		, LAST_VALUE(swap_market_buy_sats_per_cent IGNORE NULLS) OVER (w) AS swap_market_buy_sats_per_cent
		, LAST_VALUE(swap_market_sell_sats_per_cent IGNORE NULLS) OVER (w) AS swap_market_sell_sats_per_cent
		, LAST_VALUE(swap_market_mid_sats_per_cent IGNORE NULLS) OVER (w) AS swap_market_mid_sats_per_cent
		, LAST_VALUE(spot_sats_per_cent IGNORE NULLS) OVER (w) AS spot_sats_per_cent
	FROM prices

	WINDOW w AS (
		ORDER BY minute
		ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
	)

)

SELECT *
FROM backfilled_prices
