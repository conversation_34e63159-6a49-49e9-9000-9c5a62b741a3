config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Stablesats external galoy <-> okex transfers",
}

WITH minute_external_okex_transfers AS (
  SELECT
    TIMESTAMP_TRUNC(created_at, MINUTE, "UTC") as minute,
    case when action = 'deposit' then amount else -(amount+fee) end as amount,
    action,
    CONCAT(lost, '_', state) as lost_state
  FROM ${ref("stg_stablesats_internal_okex_transfers_unified")}
  WHERE action in ('deposit', 'withdraw')
)

SELECT
  minute,
  COALESCE(amount, 0) as amount,
  COALESCE(action, 'n/a') as action,
  COALESCE(lost_state, 'n/a') as lost_state,
FROM
  UNNEST(
		ARRAY_CONCAT(
			-- first year
      GENERATE_TIMESTAMP_ARRAY(
        '2023-02-01 21:18:00 UTC',
        '2023-12-31 23:59:00 UTC',
        INTERVAL 1 MINUTE
      ),
			-- following year
      GENERATE_TIMESTAMP_ARRAY(
				'2024-01-01 00:00:00 UTC',
				'2024-12-31 23:59:00 UTC',
        INTERVAL 1 MINUTE
      ),
			-- current
			GENERATE_TIMESTAMP_ARRAY(
				'2025-01-01 00:00:00 UTC',
				CURRENT_TIMESTAMP(),
				INTERVAL 1 MINUTE
			)
    )
  ) as minute
LEFT JOIN minute_external_okex_transfers USING (minute)
