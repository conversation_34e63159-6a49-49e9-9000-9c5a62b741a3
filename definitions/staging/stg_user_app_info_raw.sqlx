config {
	type: "incremental",
	uniqueKey: ["user_id"],
	bigquery: {
		clusterBy: ["user_id", "updated_on"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["user_id"],
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20221218"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT user_id,
	TIMESTAMP_MICROS(MAX(event_timestamp)) AS updated_on,
	ANY_VALUE(_TABLE_SUFFIX HAVING MAX event_timestamp) AS table_suffix,
	ANY_VALUE(device.mobile_brand_name HAVING MAX event_timestamp) AS device_brand,
	ANY_VALUE(device.mobile_model_name HAVING MAX event_timestamp) AS device_model,
	ANY_VALUE(device.operating_system HAVING MAX event_timestamp) AS device_os,
	ANY_VALUE(device.operating_system_version HAVING MAX event_timestamp) AS device_os_version,
	ANY_VALUE(device.language HAVING MAX event_timestamp) AS device_language,
	ANY_VALUE(device.is_limited_ad_tracking HAVING MAX event_timestamp) AS device_is_limited_ad_tracking,
	ANY_VALUE(geo.continent HAVING MAX event_timestamp) AS device_continent,
	ANY_VALUE(geo.sub_continent HAVING MAX event_timestamp) AS device_sub_continent,
	ANY_VALUE(geo.country HAVING MAX event_timestamp) AS device_country,
	ANY_VALUE(geo.city HAVING MAX event_timestamp) AS device_city,
	ANY_VALUE(geo.metro HAVING MAX event_timestamp) AS device_metro,
	ANY_VALUE(app_info.version HAVING MAX event_timestamp) AS app_version,
	ANY_VALUE(app_info.install_source HAVING MAX event_timestamp) AS app_install_source,

FROM `${envs.project}.${envs.currentAnalyticsSchema()}.events_*`

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "network" AND value.string_value = "${envs.currentNetwork()}"
	)
	AND user_id IS NOT NULL

GROUP BY user_id
