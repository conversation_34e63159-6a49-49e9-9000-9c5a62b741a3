config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH user_keys AS (

	SELECT user_id,
		ROW_NUMBER() OVER () AS user_key

	FROM (
		SELECT DISTINCT COALESCE(kratosUserId, l.oid) as user_id
		FROM ${ref({
				name: "accounts",
				schema: envs.currentSchema("_galoy_raw")
		})} l
		FULL JOIN ${ref({
			name: "users",
			schema: envs.currentSchema("_galoy_raw")
		})} r
		ON l.kratosUserId =r.userId
	)

)

SELECT *

FROM ${ref("stg_accounts")}
	LEFT JOIN user_keys USING (user_id)

WHERE NOT account_id IN (SELECT account_id FROM ${ref("stg_static_accounts")})
