config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH dealer_wallet_btc_outflow AS (

	SELECT
		recorded_at,
		dealer_wallet_btc_outflow,
	FROM ${ref("stg_stablesats_internal_dealer_wallet_btc_outflow")}

), rt_dealer_wallet_btc_outflow AS (

	SELECT
		recorded_at,
		dealer_wallet_btc_outflow,
	FROM ${ref("stg_rt_stablesats_internal_dealer_wallet_btc_outflow")}
	WHERE TIMESTAMP_TRUNC(recorded_at, SECOND) > (
		SELECT TIMESTAMP_TRUNC(MAX(recorded_at), SECOND)
		FROM ${ref("stg_stablesats_internal_dealer_wallet_btc_outflow")}
	)

), unioned AS (

	SELECT * FROM    dealer_wallet_btc_outflow UNION ALL
	SELECT * FROM rt_dealer_wallet_btc_outflow

), grouped AS (

	SELECT
		  TIMESTAMP_TRUNC(recorded_at, SECOND) AS recorded_at
		, SUM(dealer_wallet_btc_outflow) AS dealer_wallet_btc_outflow
	FROM unioned
	GROUP BY TIMESTAMP_TRUNC(recorded_at, SECOND)

)

SELECT
	recorded_at,
	dealer_wallet_btc_outflow,
	SUM(dealer_wallet_btc_outflow) OVER (w) AS cumsum,
FROM grouped

WINDOW w AS (
	ORDER BY recorded_at ASC
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
