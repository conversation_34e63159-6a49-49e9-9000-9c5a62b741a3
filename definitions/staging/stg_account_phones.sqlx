config {
	type: "table",
	assertions: {
		nonNull: [
			"kratos_user_id",
			"phone_key",
		],
	},
	bigquery: {
		clusterBy: ["kratos_user_id"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH phones AS (

	SELECT phone
	FROM ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})}

	UNION DISTINCT

	SELECT phone
	FROM ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})}, UNNEST(deletedPhones) AS phone

	UNION DISTINCT

	SELECT JSON_VALUE(traits, '$.phone') AS phone,
	FROM ${ref({
		name: "identities",
		schema: envs.currentSchema("_kratos_raw")
	})}

), phone_keys AS (

	SELECT phone,
		ROW_NUMBER() OVER () AS phone_key,
		${ref("udf_libphonenumber")}(phone) AS phone_metadata,

	FROM phones

	WHERE phone IS NOT NULL

), users AS (

	SELECT userId AS kratos_user_id,
		phone_key,
		TRUE AS in_users,

	FROM ${ref({
			name: "users",
			schema: envs.currentSchema("_galoy_raw")
		})}
		JOIN phone_keys USING (phone)

	WHERE userId IS NOT NULL

), users_deleted AS (


	SELECT userId AS kratos_user_id,
		phone_key,
		TRUE AS deleted,

	FROM ${ref({
			name: "users",
			schema: envs.currentSchema("_galoy_raw")
		})}, UNNEST(deletedPhones) AS deleted_phone
		JOIN phone_keys ON phone_keys.phone = deleted_phone

	WHERE userId IS NOT NULL


), identities AS (

	SELECT id AS kratos_user_id,
		phone_key,
		TRUE AS in_identities,

	FROM ${ref({
			name: "identities",
			schema: envs.currentSchema("_kratos_raw")
		})}
		JOIN phone_keys ON phone = JSON_VALUE(traits, '$.phone')

)

SELECT kratos_user_id, phone_key,
	phone_metadata.country AS alpha_2_code,
	COALESCE(in_users, FALSE) AS in_users,
	COALESCE(deleted, FALSE) AS deleted,
	COALESCE(in_identities, FALSE) AS in_identities,
	COUNT(DISTINCT kratos_user_id) OVER (PARTITION BY phone_key) AS users_with_this_phone,

FROM users
	FULL JOIN users_deleted USING (kratos_user_id, phone_key)
	FULL JOIN identities USING (kratos_user_id, phone_key)
	JOIN phone_keys USING (phone_key)
