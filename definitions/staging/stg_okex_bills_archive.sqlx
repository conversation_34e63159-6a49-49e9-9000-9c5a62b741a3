config {
  type: "table",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Billing details of dealer's trades with OKX." +
    "See https://www.okx.com/docs-v5/en/#rest-api-account-get-bills-details-last-3-months",
  assertions: {
    uniqueKey: ["bill_id", "order_id"]
  }
}

WITH raw_okex_bills_archive AS (
  SELECT
    * EXCEPT (order_id),
    order_id
  FROM
    ${
      ref({
        name: "okex_bills_archive",
        schema: envs.currentSchema("_functions_raw")
      })
    }
  WHERE order_id NOT IN (2212609751920730112, 2212609751920730113)
),

corrected_raw_okex_bills_archive AS (
  SELECT
    * EXCEPT (order_id),
    2212609751920730113 AS order_id
  FROM
    ${
      ref({
        name: "okex_bills_archive",
        schema: envs.currentSchema("_functions_raw")
      })
    }
  WHERE order_id = 2212609751920730112
),

null_order_id_raw_okex_bills_archive AS (
  SELECT
    * EXCEPT (order_id),
    order_id
  FROM
    ${
      ref({
        name: "okex_bills_archive",
        schema: envs.currentSchema("_functions_raw")
      })
    }
  WHERE order_id IS NULL
),

unioned AS (
  SELECT * FROM raw_okex_bills_archive
  UNION ALL
  SELECT * FROM corrected_raw_okex_bills_archive
  UNION ALL
  SELECT * FROM null_order_id_raw_okex_bills_archive
)

SELECT DISTINCT
  bill_id,
  TIMESTAMP_MILLIS(timestamp) AS recorded_at,
  CASE
    WHEN type_code=1 THEN "transfer" -- transfer
    WHEN type_code=2 THEN "trade"    -- trade
    WHEN type_code=3 THEN "trade"    -- delivery
    WHEN type_code=4 THEN "rebate"   -- auto token conversion
    WHEN type_code=5 THEN "trade"    -- liquidation
    WHEN type_code=6 THEN "transfer" -- margin transfer
    WHEN type_code=7 THEN "trade"    -- interest deduction
    WHEN type_code=8 THEN "fee"      -- funding rate
    WHEN type_code=9 THEN "trade"    -- adl
    WHEN type_code=10 THEN "trade"   -- clawback
    WHEN type_code=11 THEN "trade"   -- system token conversion
  END AS type,
  "ok" AS status,
  balance,
  balance_change,
  fee * 100000000 AS sat_fee,
  execution_type,
  order_id,
  pnl * 100000000 AS sat_pnl,
  sub_type_code,
  CASE
    WHEN sub_type_code=1 THEN "Buy"
    WHEN sub_type_code=2 THEN "Sell"
    WHEN sub_type_code=11 THEN "Transfer in"
    WHEN sub_type_code=12 THEN "Transfer out"
    WHEN sub_type_code=173 THEN "Funding fee expense"
    WHEN sub_type_code=174 THEN "Funding fee income"
  END AS sub_type,
  size,
  price,
  margin_mode,
  instrument_type,
  instrument_id,
  currency
FROM unioned
