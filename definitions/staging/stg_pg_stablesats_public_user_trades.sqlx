config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	id,
	CAST(buy_amount AS BIGNUMERIC) AS buy_amount,
	buy_unit,
	CAST(sell_amount AS BIGNUMERIC) AS sell_amount,
	sell_unit,
	external_ref,
	created_at,
	ledger_tx_id,
	correction_ledger_tx_id,

FROM ${ref({
	name: "pg_stablesats_public_user_trades",
	schema: envs.currentSchema("_kafka_raw")
})}
WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
