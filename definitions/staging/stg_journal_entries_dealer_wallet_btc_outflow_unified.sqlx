config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH medici_dealer_wallet_btc_outflow AS (

	SELECT
		recorded_at,
		galoy_btc_outflow,
	FROM ${ref("stg_journal_entries_dealer_wallet_btc_outflow")}

), rt_medici_dealer_wallet_btc_outflow AS (

	SELECT
		recorded_at,
		galoy_btc_outflow,
	FROM ${ref("stg_rt_journal_entries_dealer_wallet_btc_outflow")}
	WHERE TIMESTAMP_TRUNC(recorded_at, SECOND) > (
		SELECT TIMESTAMP_TRUNC(MAX(recorded_at), SECOND)
		FROM ${ref("stg_journal_entries_dealer_wallet_btc_outflow")}
	)

), unioned AS (

	SELECT * FROM    medici_dealer_wallet_btc_outflow UNION ALL
	SELECT * FROM rt_medici_dealer_wallet_btc_outflow

), grouped AS (

	SELECT
		  TIMESTAMP_TRUNC(recorded_at, SECOND) AS recorded_at
		, SUM(galoy_btc_outflow) AS galoy_btc_outflow
	FROM unioned
	GROUP BY TIMESTAMP_TRUNC(recorded_at, SECOND)

)

SELECT
	recorded_at,
	galoy_btc_outflow,
	SUM(galoy_btc_outflow) OVER (w) AS cumsum,
FROM grouped

WINDOW w AS (
	ORDER BY recorded_at ASC
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
