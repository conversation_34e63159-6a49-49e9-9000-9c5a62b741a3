config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	id,
	cursor,
	MAX(is_paired) AS is_paired,
	settlement_amount,
	settlement_currency,
	settlement_method,
	direction,
	memo,
	cents_per_unit,
	amount_in_usd_cents,
	created_at,

FROM ${ref("stg_pg_stablesats_public_galoy_transactions")}
WHERE memo IS NOT NULL
GROUP BY

    id,
    cursor,
    settlement_amount,
    settlement_currency,
    settlement_method,
    direction,
    memo,
    cents_per_unit,
    amount_in_usd_cents,
    created_at
