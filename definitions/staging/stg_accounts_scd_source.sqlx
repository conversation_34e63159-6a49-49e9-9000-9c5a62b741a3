config {
	type: "table",
	assertions: {
		uniqueKey: ["user_id"],
		nonNull: ["user_id","row_hash"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

SELECT
	user_id,
	accounts.account_id,
	username_set,
	created_at,
	level,
	language,
	NULL AS withdraw_fee,
	merchant,
	phone_number_country_name,
	phone_number_country_code,
	display_currency,
	currency AS default_wallet_currency,
	-- Needed for dataform-scd:
	CURRENT_TIMESTAMP() AS updated_at,
	SHA256(CONCAT(
		user_id,
		COALESCE(CAST(username_set AS STRING), ""),
		COALESCE(CAST(created_at AS STRING), ""),
		COALESCE(CAST(level AS STRING), ""),
		COALESCE(language, ""),
		COALESCE(CAST(merchant AS STRING), ""),
		COALESCE(phone_number_country_name, ""),
		COALESCE(phone_number_country_code, ""),
		COALESCE(display_currency, ""),
		COALESCE(currency, "")
	)) AS row_hash

FROM ${ref("stg_accounts")} AS accounts
	LEFT JOIN ${ref("stg_wallets")} ON default_wallet_id = wallet_id

WHERE user_id IS NOT NULL
