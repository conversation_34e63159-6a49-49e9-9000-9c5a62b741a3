config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Minute synchronized okex position, balance and galoy liability timeseries.",
}

WITH minute_positions AS (

  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(TIMESTAMP_MILLIS(timestamp), INTERVAL 30 SECOND), MINUTE, "UTC") as minute,
    ANY_VALUE(COALESCE(last, 0) HAVING MAX timestamp) as last,
    ANY_VALUE(COALESCE(notional_usd, 0) HAVING MAX timestamp) as notional_usd,
    ANY_VALUE(COALESCE(margin, 0) HAVING MAX timestamp) as margin,
    ANY_VALUE(COALESCE(auto_deleveraging_indicator, 0) HAVING MAX timestamp) as auto_deleveraging_indicator,
    ANY_VALUE(COALESCE(liquidation_price, exchange_leverage * last, 10 * last, 0) HAVING MAX timestamp) as liquidation_price,
    ANY_VALUE(position_quantity HAVING MAX timestamp) as position_quantity,
    ANY_VALUE(COALESCE(average_open_price, 0) HAVING MAX timestamp) as average_open_price,
    ANY_VALUE(COALESCE(unrealized_pnl, 0) HAVING MAX timestamp) as unrealized_pnl,
    ANY_VALUE(COALESCE(unrealized_pnl_ratio, 0) HAVING MAX timestamp) as unrealized_pnl_ratio,
    ANY_VALUE(COALESCE(margin_ratio, 0) HAVING MAX timestamp) as margin_ratio,
    ANY_VALUE(COALESCE(maintenance_margin_requirement, 0) HAVING MAX timestamp) as maintenance_margin_requirement,
    ANY_VALUE(exchange_leverage HAVING MAX timestamp) as exchange_leverage,
  FROM
    ${
      ref({
        name: "okex_position",
        schema: envs.currentSchema("_functions_raw")
      })
    }
  GROUP BY minute

), minute_balances AS (

  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(TIMESTAMP_MILLIS(timestamp), INTERVAL 30 SECOND), MINUTE, "UTC") as minute,
    ANY_VALUE(trading_btc_free_balance  HAVING MAX timestamp) AS trading_btc_free_balance,
    ANY_VALUE(trading_btc_used_balance  HAVING MAX timestamp) AS trading_btc_used_balance,
    ANY_VALUE(trading_btc_total_balance  HAVING MAX timestamp) AS trading_btc_total_balance,
    ANY_VALUE(notional_lever  HAVING MAX timestamp) AS notional_lever,
    ANY_VALUE(funding_btc_free_balance  HAVING MAX timestamp) AS funding_btc_free_balance,
    ANY_VALUE(funding_btc_used_balance  HAVING MAX timestamp) AS funding_btc_used_balance,
    ANY_VALUE(funding_btc_total_balance  HAVING MAX timestamp) AS funding_btc_total_balance,
  FROM
    ${
      ref({
        name: "okex_balances",
        schema: envs.currentSchema("_functions_raw")
      })
    }
  GROUP BY minute

), minute_liability AS (

  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") as minute,
    MIN(usd_balance) as usd_balance,
    MAX(btc_balance) as btc_balance
  FROM
    ${ref("stg_stablesats_internal_liability_unified")}
  GROUP BY minute

), minute_btc_outflow AS (

    SELECT
      TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") as minute,
      ANY_VALUE(cumsum HAVING MAX recorded_at) as btc_outflow,
    FROM
      ${ref("stg_stablesats_internal_dealer_wallet_btc_outflow_unified")}
    GROUP BY minute

)

SELECT
  minute,

  minute_positions.last as last_btc_price_in_usd,
  minute_positions.notional_usd as exposure_in_usd,
  minute_positions.margin,
  minute_positions.auto_deleveraging_indicator,
  minute_positions.liquidation_price,
  minute_positions.position_quantity,
  minute_positions.average_open_price,
  minute_positions.unrealized_pnl,
  minute_positions.unrealized_pnl_ratio,
  minute_positions.margin_ratio,
  minute_positions.maintenance_margin_requirement,
  minute_positions.exchange_leverage,

  minute_balances.trading_btc_free_balance,
  minute_balances.trading_btc_used_balance,
  minute_balances.trading_btc_total_balance,
  minute_balances.notional_lever,
  minute_balances.funding_btc_free_balance,
  minute_balances.funding_btc_used_balance,
  minute_balances.funding_btc_total_balance,

  minute_liability.usd_balance as usd_liability,
  minute_liability.btc_balance as btc_liability,

  minute_btc_outflow.btc_outflow,

FROM
  UNNEST(
		ARRAY_CONCAT(
			-- first year
      GENERATE_TIMESTAMP_ARRAY(
        '2023-03-17 16:00:00 UTC',
        '2023-12-31 23:59:00 UTC',
        INTERVAL 1 MINUTE
      ),
			-- following year
      GENERATE_TIMESTAMP_ARRAY(
				'2024-01-01 00:00:00 UTC',
				'2024-12-31 23:59:00 UTC',
        INTERVAL 1 MINUTE
      ),
			-- current
			GENERATE_TIMESTAMP_ARRAY(
				'2025-01-01 00:00:00 UTC',
				CURRENT_TIMESTAMP(),
				INTERVAL 1 MINUTE
			)
    )
  ) as minute
LEFT JOIN minute_positions USING (minute)
LEFT JOIN minute_balances USING (minute)
LEFT JOIN minute_liability USING (minute)
LEFT JOIN minute_btc_outflow USING (minute)
