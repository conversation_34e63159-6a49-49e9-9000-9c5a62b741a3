config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  assertions: {
    uniqueKey: ["bill_id", "order_id"]
  }
}

SELECT DISTINCT
    id as bill_id,
    time as recorded_at,
    case
      when action in ('Buy', 'Sell') then 'trade'
      when action in ('Funding fee income', 'Funding fee expense') then 'fee'
      when action in ('Transfer in', 'Transfer out') then 'transfer'
      else null
    end as type,
    'ok' as status,
    balance,
    balance_change,
    fee * 100000000 as sat_fee,
    case
      when action in ('Buy', 'Sell') then 'T'
      when action in ('Funding fee income', 'Funding fee expense') then ''
      when action in ('Transfer in', 'Transfer out') then null
      else null
    end as execution_type,
    case when action in ('Buy', 'Sell') then order_id else null end as order_id, -- for some reason 'Funding fee expense' have a shared order id of 389482649479442435
    pnl * 100000000 as sat_pnl,
    CASE
      WHEN action='Buy' THEN 1
      WHEN action='Sell' THEN 2
      WHEN action='Transfer in' THEN 11
      WHEN action='Transfer out' THEN 12
      WHEN action='Funding fee expense' THEN 173
      WHEN action='Funding fee income' THEN 174
    END AS sub_type_code,
    action as sub_type,
    case
      when action in ('Buy', 'Sell') then Amount
      when action in ('Funding fee income', 'Funding fee expense') then Amount
      when action in ('Transfer in', 'Transfer out') then balance_change
      else null
    end as size,
    fill_price as price,
    case when trade_type = 'Swap' then 'cross' else '' end as margin_mode,
    case when trade_type = 'Swap' then 'SWAP' else '' end as instrument_type,
    COALESCE(instrument, '') as instrument_id,
    balance_unit as currency,
FROM
  ${
    ref({
      name: "okx_trading_account_history",
      schema: envs.currentSchema("_functions_raw")
    })
  }
