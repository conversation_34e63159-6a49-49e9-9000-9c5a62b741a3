config {
  type: "view",
  assertions: {
    uniqueKey: ["journal_id"]
  },
  uniqueKey: ["journal_id"],
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

WITH txs AS (
  SELECT
    *
  FROM
    ${ref("stg_journal_entries")}
  WHERE
    entries_in_journal = 2
    AND wallet_id IS NOT NULL
)
SELECT
  DISTINCT t1.journal_id AS journal_id,
  t1.wallet_id AS from_wallet_id,
  t2.wallet_id AS to_wallet_id,
  ABS(t1.deposit) AS transfer_amount,
  t1.currency AS currency,
  t1.type AS type,
  t1.recorded_at AS recorded_at
FROM
  (
    SELECT
      *
    FROM
      txs
    WHERE
      deposit < 0
  ) t1
  JOIN txs t2 ON t1.journal_id = t2.journal_id
  AND t1.wallet_id != t2.wallet_id
