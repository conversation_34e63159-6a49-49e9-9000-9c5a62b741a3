config {
	type: "table",
	assertions: {
		uniqueKey: ["payment_hash"],
		rowConditions: [
			"payment_hash = \"9192b813542d818ea1accb99fa79805d7f72196ed67dbe782e42e2096f89de6c\" OR COALESCE(payment_status, \"\") IN UNNEST([\"settled\", \"failed\"])",
			// No pending payment attempts when payments are complete:
			"NOT EXISTS(SELECT 1 FROM UNNEST(attempts) WHERE is_pending)"
		]
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	status AS payment_status,
	paymentHash as payment_hash,
	sentFromPubkey AS sent_from_pubkey,
	attempts,
	revision,
	paymentRequest AS payment_request,
	confirmedDetails as confirmed_details,
	milliSatsAmount as msat_amount,
	createdAt as created_at,
	roundedUpAmount as sat_rounded_up_amount

FROM ${ref({
	name: "lnpayments",
	schema: envs.currentSchema("_galoy_raw")
})}

WHERE isCompleteRecord
