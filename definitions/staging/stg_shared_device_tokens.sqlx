config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH pairs AS (

	SELECT user_key, device_token,

	FROM ${ref("stg_user_accounts")}
		CROSS JOIN UNNEST(device_tokens) AS device_token

), tokens AS (

	SELECT device_token,
		ARRAY_AGG(DISTINCT user_key) AS user_keys,

	FROM pairs

	GROUP BY device_token

), arrayed AS (

	SELECT user_key,
		ARRAY_CONCAT_AGG(user_keys) AS shares_device_token_with,

	FROM pairs
		JOIN tokens USING (device_token)

	GROUP BY user_key

)

SELECT user_key,
	(
		SELECT COUNT(DISTINCT user_key)
		FROM UNNEST(shares_device_token_with) AS user_key
	) AS shares_device_token_with

FROM arrayed
