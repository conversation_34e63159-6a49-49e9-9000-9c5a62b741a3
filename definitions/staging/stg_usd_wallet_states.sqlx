config {
  type: "table",
  uniqueKey: ["journal_id", "wallet_id"],
  assertions: {
    uniqueKey: ["journal_id", "wallet_id"],
    nonNull: ["wallet_id", "journal_id", "deposit", "balance", "recorded_at"],
    rowConditions: [
      '(from_wallet_id = wallet_id) OR (to_wallet_id = wallet_id)'
    ]
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

select
  *
except
  (currency)
from
  ${ref("stg_wallet_states")}
where
  DATE(recorded_at) > DATE("2022-01-01")
  and currency = "USD"
  and wallet_id != (
    SELECT
      usd_wallet_id
    FROM
      ${ref("stg_static_accounts")}
    WHERE
      account_name = "dealer"
    LIMIT
      1
  )
  and deposit != 0
