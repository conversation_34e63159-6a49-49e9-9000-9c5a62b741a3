config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	client_order_id,
	correlation_id,
	instrument,
	action,
	unit,
	CAST(size AS BIGNUMERIC) AS size,
	CAST(size_usd_value AS BIGNUMERIC) AS size_usd_value,
	CAST(target_usd_value AS BIGNUMERIC) AS target_usd_value,
	CAST(position_usd_value_before_order AS BIGNUMERIC) AS position_usd_value_before_order,
	complete,
	lost,
	created_at,
	order_id,
	CAST(avg_price AS BIGNUMERIC) AS avg_price,
	CAST(fee AS BIGNUMERIC) AS fee,
	state,

FROM ${ref({
	name: "pg_stablesats_public_okex_orders",
	schema: envs.currentSchema("_kafka_raw")
})}
WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
