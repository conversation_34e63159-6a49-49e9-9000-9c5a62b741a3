config {
	type: "table",
	assertions: {
		uniqueKey: ["day", "user_key"]
	},
	bigquery: {
		clusterBy: ["day", "user_key"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH funding AS (

	SELECT
		DATE(recorded_at) as day,
		SUM(sat_funding_pnl) AS sat_funding_pnl,

	FROM ${ref("stg_stablesats_exchange_funding_fees")}

	GROUP BY day

)

SELECT day, user_key, created_at, spot_close_sats_per_cent, sat_bitcoin_close_balance, cent_stablesats_close_balance,
	COALESCE((
		sat_funding_pnl *
		cent_stablesats_close_balance /
		SUM(cent_stablesats_close_balance) OVER (PARTITION BY day)
	) / spot_close_sats_per_cent / 100, 0) AS usd_stablesats_funding_revenue,

FROM ${ref("stg_daily_user_balances")}
	LEFT JOIN funding USING (day)
