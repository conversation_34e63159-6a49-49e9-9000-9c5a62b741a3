config {
	type: "table",
	assertions: {
		uniqueKey: ["user_wallet_transaction_key"],
		nonNull: [
			"user_wallet_transaction_key",
			"transaction_key",
			"user_wallet_key",
			"user_key",
			"recorded_at",
			"settlement_method",
			"bank_direction",
			"user_direction",
			"wallet_direction",
			"currency",
			"balance",
			"balance_change",
			"sat_wallet_volume",
			"usd_wallet_volume",
			"sat_user_volume",
			"usd_user_volume",
			"sat_bank_volume",
			"usd_bank_volume",
			"sat_onchain_fee_revenue",
			"usd_onchain_fee_revenue",
			"sat_stablesats_spread_revenue",
			"usd_stablesats_spread_revenue",
			"is_quiz_reward",
			"sat_protocol_fee_estimate",
			"sat_protocol_fee_paid",
			"usd_protocol_fee_estimate",
			"usd_protocol_fee_paid",
			"is_fee_reimbursement",
			"sat_bria_fee_reconciliation",
			"sat_bria_cpfp",
		],
		rowConditions: [
			"recorded_at < \"2022-07-02\" OR is_fee_reimbursement OR voided OR voider OR sats_amount IS NULL OR cents_amount IS NULL OR sats_fee IS NULL OR cents_fee IS NULL OR journal_entry_id = '6495dffdfa5a25e345be24a0' OR ( ABS(balance_change) = ( IF(currency='BTC', sats_amount, cents_amount) + IF(currency='BTC', sats_fee, cents_fee) * IF(bank_direction='Inbound' AND settlement_method='Onchain', 0, 1) ) )",
		],
	},
	bigquery: {
		clusterBy: ["recorded_at"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH transaction_keys AS (

	SELECT journal_id,
		ROW_NUMBER() OVER () AS transaction_key

	FROM (SELECT DISTINCT journal_id FROM ${ref("stg_journal_entries")})

), stablesats_revenue AS (

	SELECT
		journal_id,
		"USD" AS currency,
		sat_stablesats_spread AS sat_stablesats_spread_revenue,
		cent_stablesats_spread/100 AS usd_stablesats_spread_revenue

	FROM ${ref("stg_trade_spreads")}

), quiz_rewards AS (

	SELECT
		journal_id,
		TRUE AS is_quiz_reward

	FROM ${ref("stg_funder_journal_entries")}

	WHERE type = "on_us"
		AND deposit < 0

), intra_ledger_counter_parties AS (

	SELECT
		journal_id AS counter_party_journal_id,
		user_wallet_key AS counter_party_user_wallet_key,
		user_key AS counter_party_user_key

	FROM ${ref("stg_journal_entries")}
		INNER JOIN ${ref("stg_user_wallets")} USING(wallet_id, currency)
		INNER JOIN ${ref("stg_user_accounts")} USING(account_id)

	WHERE bank_direction = "Inter-wallet transfer"

), bria_cpfp AS (

	SELECT payout_id,
		SUM(cpfp_fee_sats) AS sat_bria_cpfp,

	FROM ${ref("stg_bria_cpfp")}

	GROUP BY payout_id

)

SELECT
	ROW_NUMBER() OVER () AS user_wallet_transaction_key,
	transaction_key,
	user_wallet_key,
	user_key,
	recorded_at,
	LAG(recorded_at) OVER (
		PARTITION by user_wallet_key
		ORDER BY recorded_at, journal_id, journal_entry_id
	) AS previous_recorded_at,
	settlement_method,
	CASE
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 1
			AND cross_asset
			AND type != "fee_reimbursement"
			THEN "Intra-user transfer"
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 2
			THEN "Inter-user transfer"
		ELSE bank_direction
	END AS bank_direction,
	CASE
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 1
			AND cross_asset
			AND type != "fee_reimbursement"
			THEN "Intra-user transfer"
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 2
			AND deposit > 0
			THEN "Inbound"
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 2
			AND deposit < 0
			THEN "Outbound"
		ELSE bank_direction
	END AS user_direction,
	wallet_direction,
	currency,
	balance,
	deposit as balance_change,
	ABS(IF(currency="BTC", deposit, deposit * sats_per_cent)) as sat_wallet_volume,
	COALESCE(ABS(IF(currency="BTC", (deposit / sats_per_cent) / 100, deposit/100)), 0) as usd_wallet_volume,
	CASE
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 1
			THEN ABS(IF(currency="BTC", deposit, deposit * sats_per_cent))/2
		ELSE ABS(IF(currency="BTC", deposit, deposit * sats_per_cent))
	END AS sat_user_volume,
	COALESCE(CASE
		WHEN settlement_method="Intra-ledger"
			AND COUNT(DISTINCT user_id) OVER (PARTITION BY journal_id) = 1
			THEN ABS(IF(currency="BTC", (deposit / sats_per_cent) / 100, deposit/100))/2
		ELSE ABS(IF(currency="BTC", (deposit / sats_per_cent) / 100, deposit/100))
	END, 0) AS usd_user_volume,
	CASE
		WHEN settlement_method="Intra-ledger" THEN ABS(IF(currency="BTC", deposit, deposit * sats_per_cent))/2
		ELSE ABS(IF(currency="BTC", deposit, deposit * sats_per_cent))
	END AS sat_bank_volume,
	COALESCE(CASE
		WHEN settlement_method="Intra-ledger" THEN ABS(IF(currency="BTC", (deposit / sats_per_cent) / 100, deposit/100))/2
		ELSE ABS(IF(currency="BTC", (deposit / sats_per_cent) / 100, deposit/100))
	END, 0) AS usd_bank_volume,
	COALESCE(sat_onchain_fee_revenue, 0) AS sat_onchain_fee_revenue,
	COALESCE((sat_onchain_fee_revenue / sats_per_cent) / 100, 0) AS usd_onchain_fee_revenue,
	COALESCE(sat_stablesats_spread_revenue, 0) AS sat_stablesats_spread_revenue,
	COALESCE(usd_stablesats_spread_revenue, 0) AS usd_stablesats_spread_revenue,
	transaction_hash,
	voided,
	original_journal IS NOT NULL AS voider,
	pending,
	COALESCE(is_quiz_reward, FALSE) AS is_quiz_reward,
	COALESCE(CASE
		WHEN settlement_method="Onchain" AND bank_direction="Outbound"
			THEN sats_fee - sat_onchain_fee_revenue
		WHEN settlement_method="Lightning" AND bank_direction="Outbound"
			THEN sats_fee
	END, 0)  sat_protocol_fee_estimate,
	COALESCE(CASE
		WHEN settlement_method="Onchain" AND bank_direction="Outbound"
			THEN sats_fee - sat_onchain_fee_revenue
		WHEN settlement_method="Lightning" AND bank_direction="Outbound"
			THEN sats_fee + COALESCE(fee_reimbursement, 0)
	END, 0)  sat_protocol_fee_paid,
	COALESCE(CASE
		WHEN settlement_method="Onchain" AND bank_direction="Outbound"
			THEN ((sats_fee - sat_onchain_fee_revenue) / sats_per_cent) / 100
		WHEN settlement_method="Lightning" AND bank_direction="Outbound"
			THEN (sats_fee / sats_per_cent) / 100
	END, 0)  usd_protocol_fee_estimate,
	COALESCE(CASE
		WHEN settlement_method="Onchain" AND bank_direction="Outbound"
			THEN ((sats_fee - sat_onchain_fee_revenue) / sats_per_cent) / 100
		WHEN settlement_method="Lightning" AND bank_direction="Outbound"
			THEN ((sats_fee + COALESCE(fee_reimbursement, 0)) / sats_per_cent) / 100
	END, 0)  usd_protocol_fee_paid,
	counter_party_user_wallet_key,
	counter_party_user_key,
	payee_addresses,
	journal_id,
	memo,
	journal_entry_id,
	sats_amount,
	cents_amount,
	sats_fee,
	cents_fee,
	type = "fee_reimbursement" AS is_fee_reimbursement,
	COALESCE(sat_bria_fee_reconciliation, 0) AS sat_bria_fee_reconciliation,
	COALESCE(sat_bria_cpfp, 0) AS sat_bria_cpfp,
	CASE
		WHEN settlement_method = "Onchain"
		THEN IF(currency="BTC", deposit, deposit * sats_per_cent)
		WHEN settlement_method = "Lightning"
		THEN -1 * IF(currency="BTC", deposit, deposit * sats_per_cent)
		ELSE 0
	END AS sat_imbalance,

FROM ${ref("stg_journal_entries")}
	INNER JOIN ${ref("stg_user_wallets")} USING(wallet_id, currency)
	INNER JOIN ${ref("stg_user_accounts")} USING(account_id)
	LEFT JOIN ${ref("stg_onchain_bank_fees")} USING (journal_id)
	LEFT JOIN stablesats_revenue USING (journal_id, currency)
	LEFT JOIN ${ref("stg_transaction_spot_prices")} USING (journal_id, wallet_id)
	LEFT JOIN quiz_rewards USING (journal_id)
	LEFT JOIN transaction_keys USING (journal_id)
	LEFT JOIN intra_ledger_counter_parties
		ON counter_party_journal_id = journal_id
		AND counter_party_user_wallet_key != user_wallet_key
	LEFT JOIN ${ref("stg_lightning_fee_reimbursements")} USING (transaction_hash)
	LEFT JOIN ${ref("stg_bria_fee_reconciliations")} USING (payout_id)
	LEFT JOIN bria_cpfp USING (payout_id)
