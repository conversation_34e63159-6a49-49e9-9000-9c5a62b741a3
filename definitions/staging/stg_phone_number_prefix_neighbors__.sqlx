config {
	hasOutput: true,
	description: "Exposes groups of phone number prefix clusters without revealing the numbers",
}

CREATE TABLE IF NOT EXISTS ${self()} AS

SELECT
	l.userId AS user_id_1,
	r.userId AS user_id_2,
	${ref("udf_longest_common_prefix")}(l.phone, r.phone) AS longest_common_prefix_length,
	abs(timestamp_diff(l.createdAt, r.createdAt, DAY)) AS created_at_diff_in_days

FROM ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})} l, ${ref({
		name: "users",
		schema: envs.currentSchema("_galoy_raw")
	})} r

WHERE
	l.createdAt > "2023-01-01"
	AND r.createdAt > "2023-01-01"
	AND l.userId < r.userId
	AND abs(timestamp_diff(l.createdAt, r.createdAt, DAY)) <= 28
	AND ${ref("udf_longest_common_prefix")}(l.phone, r.phone) > 4
