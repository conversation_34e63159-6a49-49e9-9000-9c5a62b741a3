config {
	type: "table",
	assertions: {
		uniqueKey: ["day", "user_key"]
	},
	bigquery: {
		partitionBy: "day",
		clusterBy: ["user_key"],
		labels: {
			execution_env: envs.current
		}
	},
}

WITH users AS (

	SELECT user_key, DATE(created_at) AS created_at,

	FROM ${ref("stg_user_accounts")}
)

SELECT day, user_key, created_at, spot_close_sats_per_cent,

FROM ${ref("stg_days")}, users

WHERE day >= created_at
