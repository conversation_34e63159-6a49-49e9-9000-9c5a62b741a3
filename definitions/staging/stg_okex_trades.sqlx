config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
}

SELECT
  DISTINCT
    recorded_at,
    bill_id,
    order_id,
    type,
    sub_type,
    price,
    CASE WHEN sub_type = 'Buy' THEN size ELSE -size END AS quantity,
    balance,
    balance_change,
    sat_fee,
    sat_pnl,
    currency,
    execution_type,
    instrument_id,
    instrument_type,
    margin_mode,
FROM ${ref("stg_okex_bills_corrected")}
WHERE type = 'trade'
