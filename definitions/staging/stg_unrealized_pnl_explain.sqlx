config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
  description: "Stablesats & Users rPnl & uPnl + outlook on full liquidation of USD positions scenario"
}

WITH galoy_balances AS (

  SELECT
      minute AS recorded_at
    , galoy_btc_wallet_balance AS bank_balance_btc
    , ABS(galoy_usd_liability) AS bank_liability_usd
  FROM ${ref("stg_liability_reconciliation")}
  WHERE galoy_btc_wallet_balance IS NOT NULL OR galoy_usd_liability IS NOT NULL

), exchange_balances_upnl AS (

  SELECT
      minute AS recorded_at
    , trading_btc_total_balance + funding_btc_total_balance AS exchange_balance_btc
    , exposure_in_usd AS exchange_balance_usd
    , last_btc_price_in_usd AS exchange_last_price_usd_per_btc
    , average_open_price AS exchange_average_open_price_usd_per_btc
    , exposure_in_usd * (SAFE_DIVIDE(1, last_btc_price_in_usd) - SAFE_DIVIDE(1, average_open_price)) AS exchange_calc_upnl_btc
    , unrealized_pnl AS exchange_unrealized_pnl_btc
  FROM ${ref("stg_okex_balance_position_liability_filled")}

), bria_transfers AS (

  SELECT
    recorded_at,
    amount,
    fee,
  FROM ${ref("stg_stablesats_internal_okex_bria_transfers")}

), user_usd_wallet AS (

  SELECT
      TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute
    , wallet_id
    , journal_id
    , deposit AS deposit_cent
    , deposit * sats_per_cent AS withdrawal_sat
    , sats_per_cent AS trade_sats_per_cent
    , avg_open_sats_per_cent
    , balance AS balance_cent
  FROM ${ref("stg_wallet_avg_open_prices")}
  LEFT JOIN ${ref("stg_usd_wallet_states")} USING (journal_id, wallet_id)
  WHERE was_trade

), swap_prices AS (

  SELECT
      minute
    , swap_market_buy_sats_per_cent AS market_buy_sats_per_cent
    , swap_market_sell_sats_per_cent AS market_sell_sats_per_cent
  FROM ${ref("stg_minute_sync_spot_swap_prices_backfilled")}

), user_pnl AS (

  SELECT
      minute AS recorded_at
    , wallet_id
    , journal_id
    , deposit_cent
    , withdrawal_sat
    , avg_open_sats_per_cent
    , CASE WHEN deposit_cent < 0 THEN ABS(deposit_cent) * (trade_sats_per_cent - avg_open_sats_per_cent) ELSE 0 END AS user_pnl_sat
    , deposit_cent * (IF(deposit_cent > 0, market_buy_sats_per_cent, market_sell_sats_per_cent) - trade_sats_per_cent) AS user_paid_fees_sat
  FROM user_usd_wallet
  LEFT JOIN swap_prices USING (minute)

), fee_schedule AS (

    SELECT
        recorded_at
      , immediate_fee_rate
      , delayed_fee_rate
    FROM ${ref("stg_stablesats_historical_fee_schedule")}

), joined AS (

  SELECT
      recorded_at

    , wallet_id
    , journal_id
    , deposit_cent
    , withdrawal_sat
    , avg_open_sats_per_cent
    , user_pnl_sat
    , user_paid_fees_sat

    , amount AS liquidity_pool_transfer_btc
    , fee AS liquidity_pool_transfer_fees_btc

    , exchange_balance_usd
    , exchange_last_price_usd_per_btc
    , exchange_average_open_price_usd_per_btc
    , exchange_calc_upnl_btc
    , exchange_unrealized_pnl_btc

    , bank_balance_btc
    , bank_liability_usd

    , exchange_balance_btc

    , immediate_fee_rate
    , delayed_fee_rate
  FROM user_pnl
  FULL JOIN bria_transfers USING(recorded_at)
  FULL JOIN exchange_balances_upnl USING(recorded_at)
  FULL JOIN galoy_balances USING(recorded_at)
  FULL JOIN fee_schedule USING(recorded_at)

), grouped AS (

  SELECT
      TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute

    , SUM(deposit_cent) AS deposit_cent
    , SUM(withdrawal_sat) AS withdrawal_sat
    , SUM(deposit_cent * avg_open_sats_per_cent) AS sum_deposit_cent_x_avg_open_sats_per_cent
    , SUM(deposit_cent) AS sum_deposit_cent
    , SUM(user_pnl_sat) AS user_pnl_sat
    , SUM(user_paid_fees_sat) AS user_paid_fees_sat

    , SUM(liquidity_pool_transfer_btc) AS liquidity_pool_transfer_btc
    , SUM(liquidity_pool_transfer_fees_btc) AS liquidity_pool_transfer_fees_btc

    , ARRAY_AGG(exchange_balance_usd IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS exchange_balance_usd
    , ARRAY_AGG(exchange_last_price_usd_per_btc IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS exchange_last_price_usd_per_btc
    , ARRAY_AGG(exchange_average_open_price_usd_per_btc IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS exchange_average_open_price_usd_per_btc
    , ARRAY_AGG(exchange_calc_upnl_btc IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS exchange_calc_upnl_btc
    , ARRAY_AGG(exchange_unrealized_pnl_btc IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS exchange_unrealized_pnl_btc

    , ARRAY_AGG(bank_balance_btc IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS bank_balance_btc
    , ARRAY_AGG(bank_liability_usd IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS bank_liability_usd

    , ARRAY_AGG(exchange_balance_btc IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS exchange_balance_btc

    , ARRAY_AGG(immediate_fee_rate IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS immediate_fee_rate
    , ARRAY_AGG(delayed_fee_rate IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS delayed_fee_rate
  FROM joined
  GROUP BY minute

), pnl_with_prices AS (

  SELECT
      minute

    , deposit_cent
    , withdrawal_sat
    , sum_deposit_cent_x_avg_open_sats_per_cent
    , sum_deposit_cent
    , user_pnl_sat
    , user_paid_fees_sat

    , liquidity_pool_transfer_btc
    , liquidity_pool_transfer_fees_btc

    , exchange_balance_usd
    , exchange_last_price_usd_per_btc
    , exchange_average_open_price_usd_per_btc
    , exchange_calc_upnl_btc
    , exchange_unrealized_pnl_btc

    , bank_balance_btc
    , bank_liability_usd

    , exchange_balance_btc

    , immediate_fee_rate
    , delayed_fee_rate

    , px.market_buy_sats_per_cent
    , px.market_sell_sats_per_cent
  FROM grouped
  LEFT JOIN swap_prices px USING (minute)

), pnl_filled AS (

  SELECT
      minute

    , deposit_cent
    , withdrawal_sat
    , sum_deposit_cent_x_avg_open_sats_per_cent
    , sum_deposit_cent
    , user_pnl_sat
    , user_paid_fees_sat

    , liquidity_pool_transfer_btc
    , liquidity_pool_transfer_fees_btc

    , SUM(deposit_cent) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS deposit_cent_cumsum
    , SUM(sum_deposit_cent_x_avg_open_sats_per_cent) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS sum_deposit_cent_x_avg_open_sats_per_cent_cumsum
    , SUM(sum_deposit_cent) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS sum_deposit_cent_cumsum
    , SUM(user_pnl_sat) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS user_pnl_sat_cumsum
    , SUM(user_paid_fees_sat) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS user_paid_fees_sat_cumsum

    , SUM(liquidity_pool_transfer_btc) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS liquidity_pool_transfer_btc_cumsum
    , SUM(liquidity_pool_transfer_fees_btc) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS liquidity_pool_transfer_fees_btc_cumsum

    , LAST_VALUE(exchange_balance_usd IGNORE NULLS) OVER (w) AS exchange_balance_usd
    , LAST_VALUE(exchange_last_price_usd_per_btc IGNORE NULLS) OVER (w) AS exchange_last_price_usd_per_btc
    , LAST_VALUE(exchange_average_open_price_usd_per_btc IGNORE NULLS) OVER (w) AS exchange_average_open_price_usd_per_btc
    , LAST_VALUE(exchange_calc_upnl_btc IGNORE NULLS) OVER (w) AS exchange_calc_upnl_btc
    , LAST_VALUE(exchange_unrealized_pnl_btc IGNORE NULLS) OVER (w) AS exchange_unrealized_pnl_btc

    , LAST_VALUE(bank_balance_btc IGNORE NULLS) OVER (w) AS bank_balance_btc
    , LAST_VALUE(bank_liability_usd IGNORE NULLS) OVER (w) AS bank_liability_usd

    , LAST_VALUE(exchange_balance_btc IGNORE NULLS) OVER (w) AS exchange_balance_btc

    , LAST_VALUE(immediate_fee_rate IGNORE NULLS) OVER (w) AS immediate_fee_rate
    , LAST_VALUE(delayed_fee_rate IGNORE NULLS) OVER (w) AS delayed_fee_rate

    , LAST_VALUE(market_buy_sats_per_cent IGNORE NULLS) OVER (w) AS market_buy_sats_per_cent
    , LAST_VALUE(market_sell_sats_per_cent IGNORE NULLS) OVER (w) AS market_sell_sats_per_cent
  FROM pnl_with_prices

  WINDOW w AS (
    ORDER BY minute
    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
  )

), final AS (

  SELECT
      *
    , SAFE_DIVIDE(sum_deposit_cent_x_avg_open_sats_per_cent_cumsum, sum_deposit_cent_cumsum) AS avg_open_sats_per_cent
    , deposit_cent_cumsum * market_buy_sats_per_cent AS user_owed_sat
    , deposit_cent_cumsum * market_buy_sats_per_cent / ********* AS user_owed_btc
    , ABS(withdrawal_sat) * immediate_fee_rate AS user_immediate_fee_sat
    , ABS(withdrawal_sat) * delayed_fee_rate AS user_delayed_fee_sat
    , bank_balance_btc
      + exchange_balance_btc
      - liquidity_pool_transfer_btc_cumsum
      - liquidity_pool_transfer_fees_btc_cumsum AS bank_available_balance_btc
  FROM pnl_filled

)

SELECT
    *
  , SUM(user_immediate_fee_sat) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS user_immediate_fee_sat_cumsum
  , SUM(user_delayed_fee_sat) OVER (ORDER BY minute ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS user_delayed_fee_sat_cumsum
  , 1000000 /  avg_open_sats_per_cent AS user_average_open_price_usd_per_btc
  , deposit_cent_cumsum * (market_buy_sats_per_cent - avg_open_sats_per_cent) AS user_upnl_sat
  , bank_available_balance_btc + exchange_calc_upnl_btc - user_owed_btc AS bank_alt_net_profit_btc
  , bank_available_balance_btc + exchange_unrealized_pnl_btc - user_owed_btc AS bank_net_profit_btc
  , bank_available_balance_btc
    + exchange_balance_usd * (SAFE_DIVIDE((1 - SAFE_DIVIDE((deposit_cent_cumsum / 100), exchange_balance_usd)), exchange_last_price_usd_per_btc) - SAFE_DIVIDE(1, exchange_average_open_price_usd_per_btc))
    AS bank_alt2_net_profit_btc
FROM final
