config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH okex_orders as (

	SELECT *
	FROM ${ref("stg_stablesats_internal_okex_orders")}

), rt_okex_orders as (

	SELECT *
	FROM ${ref("stg_rt_stablesats_internal_okex_orders")}
	WHERE created_at > (SELECT MAX(created_at) FROM ${ref("stg_stablesats_internal_okex_orders")})
)

SELECT * FROM okex_orders

UNION ALL

SELECT * FROM rt_okex_orders
