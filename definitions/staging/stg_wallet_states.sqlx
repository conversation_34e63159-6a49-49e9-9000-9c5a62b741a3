config {
  type: "view",
  uniqueKey: ["journal_id", "wallet_id"],
  assertions: {
    uniqueKey: ["journal_id", "wallet_id"],
    nonNull: ["journal_id", "wallet_id", "deposit", "currency", "was_trade", "balance", "recorded_at"]
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

SELECT
  journal_id,
  wallet_id,
  e.deposit as deposit,
  sats_per_cent,
  e.currency as currency,
  COALESCE(t.journal_id IS NOT NULL, FALSE) as was_trade,
  balance,
  from_wallet_id,
  to_wallet_id,
  e.recorded_at
FROM
  ${ref("stg_journal_entries")} e
  INNER JOIN ${ref("stg_user_wallets")} w USING(wallet_id)
  LEFT JOIN (
    SELECT *
    FROM ${ref("stg_trades")}
    WHERE dealer_cent_deposit != 0 AND dealer_sat_deposit != 0
  ) t USING(journal_id)
  LEFT JOIN ${ref("stg_simple_wallet_transfers")} i USING(journal_id)
