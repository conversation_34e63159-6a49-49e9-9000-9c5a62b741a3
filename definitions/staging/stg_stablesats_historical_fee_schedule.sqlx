config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

/*

- Use deployed fees history:

    - since 2022-12-26:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0010
        delayed_fee_rate   = 0.0010

    - from 2022-12-26:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0010
        delayed_fee_rate   = 0.0010

    - from 2022-12-26:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0015
        delayed_fee_rate   = 0.0010

    - from 2022-12-26:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0005
        delayed_fee_rate   = 0.0010

    - from 2022-12-26:
        base_fee_rate      = 0.0008
        immediate_fee_rate = 0.0005
        delayed_fee_rate   = 0.0007


    - from 2022-11-21:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0015
        delayed_fee_rate   = 0.0010


    - from 2022-11-15:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0015
        delayed_fee_rate   = 0.0010

    - from 2022-11-10:
        base_fee_rate      = 0.0010
        immediate_fee_rate = 0.0005
        delayed_fee_rate   = 0.0010

    - before 2022-11-10:
        base_fee_rate      = 0.0008
        immediate_fee_rate = 0.0005
        delayed_fee_rate   = 0.0007

*/

WITH fee_schedule AS (
    SELECT '2022-06-01 00:00:00 UTC' AS recorded_at, 0.0008 AS base_fee_rate, 0.0005 AS immediate_fee_rate, 0.0007 AS delayed_fee_rate UNION ALL
    SELECT '2022-11-10 00:00:00 UTC', 0.0010, 0.0005, 0.0010 UNION ALL
    SELECT '2022-11-15 00:00:00 UTC', 0.0010, 0.0015, 0.0010 UNION ALL
    SELECT '2022-12-26 00:00:00 UTC', 0.0010, 0.0010, 0.0010
)

SELECT
      TIMESTAMP(recorded_at) AS recorded_at
    , CAST(base_fee_rate + immediate_fee_rate AS NUMERIC) AS immediate_fee_rate
    , CAST(base_fee_rate + delayed_fee_rate AS NUMERIC) AS delayed_fee_rate
FROM fee_schedule
