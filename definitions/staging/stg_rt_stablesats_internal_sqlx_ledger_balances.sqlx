config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT DISTINCT

	journal_id,
	account_id,
	entry_id,
	currency,
	settled_dr_balance,
	settled_cr_balance,
	settled_entry_id,
	settled_modified_at,
	pending_dr_balance,
	pending_cr_balance,
	pending_entry_id,
	pending_modified_at,
	encumbered_dr_balance,
	encumbered_cr_balance,
	encumbered_entry_id,
	encumbered_modified_at,
	version,
	modified_at,
	created_at,

FROM ${ref("stg_pg_stablesats_public_sqlx_ledger_balances")}
