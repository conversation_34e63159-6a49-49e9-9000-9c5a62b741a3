config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH galoy_liability as (

	SELECT
		recorded_at,
		galoy_btc_liability,
		galoy_usd_liability,
	FROM ${ref("stg_galoy_liability")}

), rt_galoy_liability as (

	SELECT
		recorded_at,
		galoy_btc_liability,
		galoy_usd_liability,
	FROM ${ref("stg_rt_galoy_liability")}

), unioned AS (

	SELECT * FROM    galoy_liability UNION ALL
	SELECT * FROM rt_galoy_liability

), grouped AS (

	SELECT
		  TIMESTAMP_TRUNC(recorded_at, SECOND) AS recorded_at
		, SUM(galoy_btc_liability) AS galoy_btc_liability
		, SUM(galoy_usd_liability) AS galoy_usd_liability
	FROM unioned
	GROUP BY TIMESTAMP_TRUNC(recorded_at, SECOND)

)

SELECT
	recorded_at,
	galoy_btc_liability AS galoy_btc_liability_increment,
	galoy_usd_liability AS galoy_usd_liability_increment,
	SUM(galoy_btc_liability) OVER (w) AS galoy_btc_liability_cumsum,
	SUM(galoy_usd_liability) OVER (w) AS galoy_usd_liability_cumsum,
FROM grouped

WINDOW w AS (
	ORDER BY recorded_at ASC
	ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
)
