config {
	type: "table",
	bigquery: {
		clusterBy: ["recorded_at", "user_id"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["user_id", "recorded_at"]
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20221218"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT
	user_id,
	MIN(_TABLE_SUFFIX) AS table_suffix,
	(
		SELECT value.int_value
		FROM unnest(event_params)
		WHERE key = "ga_session_id"
	) AS session_id,
	(
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "ga_session_number"
	) AS session_number,
	TIMESTAMP_MICROS(MIN(event_timestamp)) AS recorded_at,
	SUM(IF(event_name="user_engagement", (
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "engagement_time_msec"
	), 0)) AS engagement_time_msec,
	COUNT(1) as number_of_events,

FROM `${envs.project}.${envs.currentAnalyticsSchema()}.events_*`

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "network" AND value.string_value = "${envs.currentNetwork()}"
	)
	AND user_id IS NOT NULL
	AND EXISTS(
		SELECT 1
		FROM UNNEST(event_params)
		WHERE key = "ga_session_number" and value.int_value IS NOT NULL
	)

GROUP BY user_id, session_id, session_number

HAVING session_id IS NOT NULL
