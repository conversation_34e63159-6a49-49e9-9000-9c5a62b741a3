config {
	type: "incremental",
	bigquery: {
		clusterBy: ["first_open_time", "user_pseudo_id"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["user_pseudo_id", "first_open_time"]
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20221218"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT DISTINCT user_pseudo_id, user_id,
	(
		SELECT TIMESTAMP_MILLIS(value.int_value)
		FROM UNNEST(user_properties)
		WHERE key = "first_open_time"
	) AS first_open_time,
	_TABLE_SUFFIX AS table_suffix,

FROM `${envs.project}.${envs.currentAnalyticsSchema()}.events_*`

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "network" AND value.string_value = "${envs.currentNetwork()}"
	)
	AND user_pseudo_id IS NOT NULL
	AND user_id IS NOT NULL
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "first_open_time"
			AND value.int_value > 0
	)
