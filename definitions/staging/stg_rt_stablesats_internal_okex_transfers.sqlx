config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	client_transfer_id,
	correlation_id,
	action,
	currency,
	amount,
	fee,
	transfer_from,
	transfer_to,
	target_usd_exposure,
	current_usd_exposure,
	trading_btc_used_balance,
	trading_btc_total_balance,
	current_usd_btc_price,
	funding_btc_total_balance,
	MIN(lost) AS lost,
	MAX(transfer_id) AS transfer_id,
	MAX(state) AS state,
	created_at,

FROM ${ref("stg_pg_stablesats_public_okex_transfers")}
GROUP BY

	client_transfer_id,
	correlation_id,
	action,
	currency,
	amount,
	fee,
	transfer_from,
	transfer_to,
	target_usd_exposure,
	current_usd_exposure,
	trading_btc_used_balance,
	trading_btc_total_balance,
	current_usd_btc_price,
	funding_btc_total_balance,
	created_at
