config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	journal_id,
	account_id,
	entry_id,
	currency,
	CAST(settled_dr_balance AS BIGNUMERIC) AS settled_dr_balance,
	CAST(settled_cr_balance AS BIGNUMERIC) AS settled_cr_balance,
	settled_entry_id,
	settled_modified_at,
	CAST(pending_dr_balance AS BIGNUMERIC) AS pending_dr_balance,
	CAST(pending_cr_balance AS BIGNUMERIC) AS pending_cr_balance,
	pending_entry_id,
	pending_modified_at,
	CAST(encumbered_dr_balance AS BIGNUMERIC) AS encumbered_dr_balance,
	CAST(encumbered_cr_balance AS BIGNUMERIC) AS encumbered_cr_balance,
	encumbered_entry_id,
	encumbered_modified_at,
	version,
	modified_at,
	created_at,

FROM ${ref({
	name: "pg_stablesats_public_sqlx_ledger_balances",
	schema: envs.currentSchema("_kafka_raw")
})}
WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
