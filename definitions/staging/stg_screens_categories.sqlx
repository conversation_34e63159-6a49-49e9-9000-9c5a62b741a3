config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["screen"]
	},
}

SELECT "Home" AS screen, "Home" AS category UNION ALL
SELECT "MoveMoneyManual", "Home" UNION ALL
SELECT "sendBitcoinDestination", "Send" UNION ALL
SELECT "settings", "Settings" UNION ALL
SELECT "sendBitcoinDetails", "Send" UNION ALL
SELECT "authentication", "Authenticate" UNION ALL
SELECT "sendBitcoinConfirmation", "Send" UNION ALL
SELECT "earnsSection", "Earn" UNION ALL
SELECT "priceHistory", "Price" UNION ALL
SELECT "receiveBitcoin", "Receive" UNION ALL
SELECT "sendBitcoinSuccess", "Send" UNION ALL
SELECT "earnsQuiz", "Earn" UNION ALL
SELECT "transactionDetail", "Transaction Detail" UNION ALL
SELECT "Earn", "Earn" UNION ALL
SELECT "transactionHistory", "Transaction History" UNION ALL
SELECT "sendBitcoinDestinationManual", "Send" UNION ALL
SELECT "contactList", "People" UNION ALL
SELECT "MoveMoney", "Home" UNION ALL
SELECT "sendBitcoinDetailsManual", "Send" UNION ALL
SELECT "sendBitcoinConfirmationManual", "Send" UNION ALL
SELECT "priceDetailManual", "Price" UNION ALL
SELECT "conversionDetails", "Convert" UNION ALL
SELECT "Map", "Map" UNION ALL
SELECT "authenticationManual", "Authenticate" UNION ALL
SELECT "phoneInput", "Authenticate" UNION ALL
SELECT "earnsSectionManual", "Earn" UNION ALL
SELECT "settingsManual", "Settings" UNION ALL
SELECT "accountScreen", "Settings" UNION ALL
SELECT "contactDetail", "People" UNION ALL
SELECT "receiveBitcoinManual", "Receive" UNION ALL
SELECT "sendBitcoinSuccessManual", "Send" UNION ALL
SELECT "earnsQuizManual", "Earn" UNION ALL
SELECT "conversionConfirmation", "Convert" UNION ALL
SELECT "pin", "Authenticate" UNION ALL
SELECT "EarnManual", "Earn" UNION ALL
SELECT "conversionSuccess", "Convert" UNION ALL
SELECT "addressScreen", "Settings" UNION ALL
SELECT "transactionDetailManual", "Transaction Detail" UNION ALL
SELECT "scanningQRCode", "Send" UNION ALL
SELECT "phoneValidation", "Authenticate" UNION ALL
SELECT "sectionCompleted", "Earn" UNION ALL
SELECT "conversionDetailsManual", "Convert" UNION ALL
SELECT "contactListManual", "People" UNION ALL
SELECT "priceDetail", "Price" UNION ALL
SELECT "welcomeFirstManual", "Earn" UNION ALL
SELECT "peopleHome", "People" UNION ALL
SELECT "currency", "Settings" UNION ALL
SELECT "welcomePhoneInputManual", "Authenticate" UNION ALL
SELECT "moveMoney", "Home" UNION ALL
SELECT "MapManual", "Map" UNION ALL
SELECT "transactionHistoryManual", "Transaction History" UNION ALL
SELECT "phoneLoginInitiate", "Authenticate" UNION ALL
SELECT "security", "Settings" UNION ALL
SELECT "language", "Settings" UNION ALL
SELECT "pinManual", "Authenticate" UNION ALL
SELECT "conversionConfirmationManual", "Convert" UNION ALL
SELECT "defaultWallet", "Settings" UNION ALL
SELECT "welcomePhoneValidationManual", "Authenticate" UNION ALL
SELECT "conversionSuccessManual", "Convert" UNION ALL
SELECT "addressScreenManual", "Settings" UNION ALL
SELECT "contactDetailManual", "People" UNION ALL
SELECT "sectionCompletedManual", "Earn" UNION ALL
SELECT "welcomePhoneInput", "Authenticate" UNION ALL
SELECT "circlesDashboard", "Circles" UNION ALL
SELECT "accountScreenManual", "Settings" UNION ALL
SELECT "phoneLoginValidate", "Authenticate" UNION ALL
SELECT "welcomeFirst", "Earn" UNION ALL
SELECT "scanningQRCodeManual", "Send" UNION ALL
SELECT "allContacts", "People" UNION ALL
SELECT "theme", "Settings" UNION ALL
SELECT "Contacts", "People" UNION ALL
SELECT "securityManual", "Settings" UNION ALL
SELECT "emailLoginInitiate", "Authenticate" UNION ALL
SELECT "sendBitcoin", "Send" UNION ALL
SELECT "emailRegistrationInitiate", "Authenticate" UNION ALL
SELECT "welcomePhoneValidation", "Authenticate" UNION ALL
SELECT "languageManual", "Settings" UNION ALL
SELECT "emailRegistrationValidate", "Authenticate" UNION ALL
SELECT "emailLoginValidate", "Authenticate" UNION ALL
SELECT "Settings", "Settings" UNION ALL
SELECT "ContactsManual", "People" UNION ALL
SELECT "emailLoginInput", "Authenticate" UNION ALL
SELECT "liteDeviceAccount", "Authenticate" UNION ALL
SELECT "webView", "Fiat" UNION ALL
SELECT "transactionLimitsScreen", "Settings" UNION ALL
SELECT "redeemBitcoinResult", "Receive" UNION ALL
SELECT "redeemBitcoinDetail", "Receive" UNION ALL
SELECT "notificationSettingsScreen", "Settings" UNION ALL
SELECT "phoneRegistrationInitiate", "Authenticate" UNION ALL
SELECT "sendBitcoinCompleted", "Send"
