config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
  description: "OKX <-> Bria deposits & withdrawals from stablesats records with corrections"
}

WITH okex_bria_transfers AS (

  SELECT
    created_at AS recorded_at,
    CASE WHEN action = 'withdraw' THEN -amount ELSE amount END AS amount,
    CASE WHEN action = 'withdraw' THEN -fee ELSE fee END AS fee,
  FROM ${ref("stg_stablesats_internal_okex_transfers")}
  WHERE state = 'success'
  AND NOT lost
  AND ACTION IN ('deposit', 'withdraw')
  AND created_at > '2023-08-31 01:17:51 UTC'  -- Effective Bria takeover date

), okex_bria_corrections AS (

  -- missing in the table as of 2024-08-29
  SELECT TIMESTAMP('2024-08-09 14:36:33.064940 UTC') AS recorded_at, -1.15001594 AS amount, -0.00003 AS fee UNION ALL
  SELECT TIMESTAMP('2024-08-09 02:34:01.288992 UTC') AS recorded_at, -0.19351547 AS amount, -0.00003 AS fee UNION ALL
  SELECT TIMESTAMP('2024-07-05 04:18:34.579869 UTC') AS recorded_at, -1.40406426 AS amount, -0.00005 AS fee UNION ALL
  SELECT TIMESTAMP('2024-06-13 16:06:20.325609 UTC') AS recorded_at, -1.35957075 AS amount, -0.00020 AS fee UNION ALL
  SELECT TIMESTAMP('2024-03-11 21:56:38.709582 UTC') AS recorded_at, 0.47704970 AS amount, 0 AS fee UNION ALL

  -- missing in the table, confirmed by Bria & okx data
  SELECT TIMESTAMP('2024-01-08 17:49:12.391416 UTC') AS recorded_at, 0.46695340 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-11-30 14:27:33.685344 UTC') AS recorded_at, 0.46260640 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-10-25 16:32:48.333628 UTC') AS recorded_at, 0.29340902 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-10-25 15:28:06.141791 UTC') AS recorded_at, 0.29340733 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-10-25 14:26:51.888251 UTC') AS recorded_at, 0.29340719 AS amount, 0 AS fee UNION ALL

  -- duplicated 4 times in the table, confirmed by Bria & okx data
  SELECT TIMESTAMP('2023-10-21 21:02:41.000004 UTC') AS recorded_at, -0.30685928 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-10-21 21:02:41.000003 UTC') AS recorded_at, -0.30685928 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-10-21 21:02:41.000002 UTC') AS recorded_at, -0.30685928 AS amount, 0 AS fee UNION ALL
  SELECT TIMESTAMP('2023-10-21 21:02:41.000001 UTC') AS recorded_at, -0.30685928 AS amount, 0 AS fee

)

SELECT * FROM okex_bria_transfers UNION ALL
SELECT * FROM okex_bria_corrections
