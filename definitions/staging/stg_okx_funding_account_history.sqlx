config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  assertions: {
    uniqueKey: ["id"]
  }
}

SELECT DISTINCT
    id,
    time as recorded_at,
    type,
    amount,
    before_balance,
    after_balance,
    fee,
    symbol as unit,
FROM
  ${
    ref({
      name: "okx_funding_account_history",
      schema: envs.currentSchema("_functions_raw")
    })
  }
