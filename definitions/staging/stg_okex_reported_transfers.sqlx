config {
  type: "table",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Exchange reported transfers to funding account",
}

WITH minute_okex_transfers AS (

  SELECT
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute,
    type,
    amount,
  FROM ${ref("stg_okx_funding_account_history")}

), grouped_transfers AS (

  SELECT
      minute,
      type,
      SUM(amount) AS amount,
  FROM minute_okex_transfers
  GROUP BY minute, type

)

SELECT
  minute,
  type,
  amount,
FROM
  grouped_transfers
