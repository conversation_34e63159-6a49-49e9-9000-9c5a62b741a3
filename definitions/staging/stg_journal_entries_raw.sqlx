config {
	type: "table",
	assertions: {
		rowConditions: [
			"journal_entry_id = '6495dffdfa5a25e345be24a0' OR (credit >= 0 AND debit >= 0)",
		],
	},
	bigquery: {
		clusterBy: ["recorded_at"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	timestamp AS recorded_at,
	oid AS journal_entry_id,
	journal AS journal_id,

	book,
	account_path,
	accounts,

	credit,
	debit,
	CAST(credit - debit AS INTEGER) AS deposit,
	satsAmount AS sats_amount,
	centsAmount AS cents_amount,
	fee,
	satsFee AS sats_fee,
	centsFee AS cents_fee,

	currency,
	type,

	pending,

	displayCurrency as display_currency,
	displayFee as display_fee,
	displayAmount as display_amount,

	meta,
	feeKnownInAdvance AS fee_known_in_advance,
	COALESCE(voided, FALSE) AS voided,
	void_reason,
	original_journal,
	related_journal,
	satsAmountMigration AS sats_amount_migration,

	memo,
	memoPayer AS memo_payer,

	`hash` as transaction_hash,
	vout,
	pubkey,
	payee_addresses,
	payout_id,
	request_id,

FROM ${ref({
	name: "medici_transactions",
	schema: envs.currentSchema("_galoy_raw")
})}
