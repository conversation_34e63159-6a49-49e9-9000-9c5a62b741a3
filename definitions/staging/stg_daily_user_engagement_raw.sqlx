config {
	type: "incremental",
	bigquery: {
		clusterBy: ["day", "user_id"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["user_id", "day"]
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20221218"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT user_id,
	MAX(_TABLE_SUFFIX) AS table_suffix,
	DATE(TIMESTAMP_MICROS(event_timestamp)) AS day,
	SUM(event_params.value.int_value) AS engagement_time_msec,

FROM `${envs.project}.${envs.currentAnalyticsSchema()}.events_*` AS events, events.event_params

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "network" AND value.string_value = "${envs.currentNetwork()}"
	)
	AND user_id IS NOT NULL
	AND event_name = 'user_engagement'
	AND event_params.key = 'engagement_time_msec'
	AND event_params.value.int_value > 0

GROUP BY user_id, day
