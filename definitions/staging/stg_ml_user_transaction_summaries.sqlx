config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH transactions AS (

	SELECT user_key, user_direction, settlement_method, currency, usd_user_volume,
		COUNT(DISTINCT user_wallet_transaction_key) OVER (w) AS n_transactions,
		COUNT(DISTINCT counter_party_user_key) OVER (w) AS n_intraledger_counter_parties,
		SUM(usd_user_volume) OVER (w) AS sum_usd_user_volume,
		PERCENTILE_CONT(usd_user_volume, 0.5 IGNORE NULLS) OVER (w) AS median_usd_user_volume,
		GREATEST(1/24, (UNIX_SECONDS(MAX(recorded_at) OVER (w)) - UNIX_SECONDS(MIN(recorded_at) OVER (w)))/60*60*24) AS days_active,
		PERCENTILE_CONT(
			COALESCE(UNIX_SECONDS(recorded_at) - UNIX_SECONDS(previous_recorded_at), 0),
			0.5
			IGNORE NULLS
		) OVER (w) AS median_seconds_between_transactions

	FROM ${ref("stg_user_wallet_transactions")}

	WHERE DATE(recorded_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)

	WINDOW w AS (
		PARTITION BY user_key
	)

), user_summaries AS (

	SELECT user_key,

		ANY_VALUE(n_transactions)/ANY_VALUE(days_active) AS n_transactions_per_day,
		ANY_VALUE(n_intraledger_counter_parties)/ANY_VALUE(days_active) AS n_intraledger_counter_parties_per_day,

		COUNTIF(currency="USD")/ANY_VALUE(n_transactions) AS p_usd_transactions,

		COUNTIF(user_direction = "Intra-user transfer")/ANY_VALUE(n_transactions) AS p_intra_transactions,
		COUNTIF(user_direction = "Outbound"
			AND settlement_method = "Intra-ledger")/ANY_VALUE(n_transactions) AS p_inter_out_transactions,
		COUNTIF(user_direction = "Inbound"
			AND settlement_method = "Intra-ledger")/ANY_VALUE(n_transactions) AS p_inter_in_transactions,
		COUNTIF(user_direction = "Outbound"
			AND settlement_method = "Onchain")/ANY_VALUE(n_transactions) AS p_onchain_out_transactions,
		COUNTIF(user_direction = "Inbound"
			AND settlement_method = "Onchain")/ANY_VALUE(n_transactions) AS p_onchain_in_transactions,
		COUNTIF(user_direction = "Outbound"
			AND settlement_method = "Lightning")/ANY_VALUE(n_transactions) AS p_lightning_out_transactions,
		COUNTIF(user_direction = "Inbound"
			AND settlement_method = "Lightning")/ANY_VALUE(n_transactions) AS p_lightning_in_transactions,

		ANY_VALUE(sum_usd_user_volume)/ANY_VALUE(days_active) as sum_usd_user_volume_per_day,

		SUM(IF(currency = "USD", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_usd_user_volume,

		SUM(IF(user_direction = "Intra-user transfer", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_intra_user_volume,
		SUM(IF(user_direction = "Outbound"
			AND settlement_method = "Intra-ledger", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_inter_out_user_volume,
		SUM(IF(user_direction = "Inbound"
			AND settlement_method = "Intra-ledger", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_inter_in_user_volume,
		SUM(IF(user_direction = "Outbound"
			AND settlement_method = "Onchain", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_onchain_out_user_volume,
		SUM(IF(user_direction = "Inbound"
			AND settlement_method = "Onchain", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_onchain_in_user_volume,
		SUM(IF(user_direction = "Outbound"
			AND settlement_method = "Lightning", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_lightning_out_user_volume,
		SUM(IF(user_direction = "Inbound"
			AND settlement_method = "Lightning", usd_user_volume, 0))/ANY_VALUE(sum_usd_user_volume) AS p_lightning_in_user_volume,

		ANY_VALUE(median_usd_user_volume) as median_usd_user_volume,
		ANY_VALUE(days_active) as days_active,
		ANY_VALUE(median_seconds_between_transactions) as median_seconds_between_transactions

	FROM transactions

	WHERE n_transactions > 10
		AND sum_usd_user_volume > 10
		AND days_active > 3

	GROUP BY user_key

)

SELECT *,
	PERCENT_RANK() OVER (ORDER BY n_transactions_per_day) AS n_transactions_per_day_rank,
	PERCENT_RANK() OVER (ORDER BY sum_usd_user_volume_per_day) AS sum_usd_user_volume_per_day_rank,
	PERCENT_RANK() OVER (ORDER BY days_active) AS days_active_rank

FROM user_summaries
