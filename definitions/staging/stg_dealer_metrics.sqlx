config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
  description: "Stablesats risk metrics",
}

SELECT

  minute,

  SAFE_DIVIDE(exposure_in_usd, abs(usd_liability)) as exposure_liability_ratio,
  usd_liability,
  btc_liability,
  btc_outflow,
  btc_liability + btc_outflow AS btc_dealer_wallet_balance,
  exposure_in_usd,
  position_quantity,

  SAFE_DIVIDE(exposure_in_usd, (trading_btc_total_balance * last_btc_price_in_usd)) as exposure_leverage_ratio,
  notional_lever,
  SAFE_DIVIDE(SAFE_DIVIDE(exposure_in_usd, (trading_btc_total_balance + funding_btc_total_balance)), last_btc_price_in_usd) as effective_leverage,
  (trading_btc_total_balance + funding_btc_total_balance) * last_btc_price_in_usd as exchange_usd_total_balance,
  trading_btc_total_balance * last_btc_price_in_usd as collateral_in_usd,
  (trading_btc_total_balance + funding_btc_total_balance) * last_btc_price_in_usd as effective_collateral_in_usd,
  SAFE_DIVIDE(SAFE_DIVIDE(exposure_in_usd, last_btc_price_in_usd), margin) as position_leverage,
  exchange_leverage,
  liquidation_price,
  last_btc_price_in_usd,

  SAFE_DIVIDE(trading_btc_total_balance, trading_btc_used_balance) as margin_leverage_ratio,
  auto_deleveraging_indicator,

  trading_btc_free_balance,
  trading_btc_used_balance,
  trading_btc_total_balance,
  funding_btc_free_balance,
  funding_btc_used_balance,
  funding_btc_total_balance,
  trading_btc_total_balance + funding_btc_total_balance as exchange_btc_total_balance,
  average_open_price,

  unrealized_pnl,
  unrealized_pnl_ratio * 100 as unrealized_pnl_ratio,

  margin,
  margin_ratio,
  maintenance_margin_requirement

FROM ${ref("stg_okex_balance_position_liability_filled")}
