config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		uniqueKey: ["account_id"],
	},
}

SELECT
	account_id,
	account_oid,
	LEAST(accounts.created_at, users.created_at) AS created_at,
	level,
	language,
	device_tokens,
	kratos_user_id,
	username_set,
	phone_number_country_name,
	phone_number_country_code,
	last_connection,
	default_wallet_id,
	display_currency,
	status_history,
	device_id,
	--deleted_device_id,
	phone_key,
	deleted_phone_keys,
	email_key,
	deleted_email_keys,

	COALESCE(merchant, FALSE) AS merchant,

	COALESCE(kratos_user_id, account_oid) as user_id,

	(
		SELECT ANY_VALUE(status HAVING MAX updated_at)
		FROM UNNEST(status_history)
	) AS status,

	COALESCE(credential_name = "totp", FALSE) AS totp,

FROM ${ref("stg_accounts_raw")} accounts
	LEFT JOIN ${ref("stg_users_raw")} users USING (kratos_user_id)
	LEFT JOIN (
		SELECT DISTINCT account_id, TRUE as merchant,
		FROM ${ref("stg_account_merchants")}
	) USING (account_id)
	LEFT JOIN (
		SELECT kratos_user_id,
			ANY_VALUE(credential_name HAVING MAX updated_at) AS credential_name
		FROM ${ref("stg_kratos_identity_credentials")}
		GROUP BY kratos_user_id
	) USING (kratos_user_id)
