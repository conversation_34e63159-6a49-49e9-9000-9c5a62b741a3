config {
	description: "Days since the first ledger transaction",
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH daily_swap_prices AS (

	SELECT
		DATE(timestamp) AS day,
		ANY_VALUE(sats_per_cent_market_buy HAVING MIN timestamp) AS swap_buy_open_sats_per_cent,
		ANY_VALUE(sats_per_cent_market_sell HAVING MIN timestamp) AS swap_sell_open_sats_per_cent,
		ANY_VALUE(sats_per_cent_market_buy HAVING MAX timestamp) AS swap_buy_close_sats_per_cent,
		ANY_VALUE(sats_per_cent_market_sell HAVING MAX timestamp) AS swap_sell_close_sats_per_cent

	FROM ${ref("stg_minute_swap_prices")}

	WHERE sats_per_cent_market_buy IS NOT NULL
		AND sats_per_cent_market_sell IS NOT NULL

	GROUP BY day

), daily_spot_prices AS (

	SELECT
		DATE(timestamp) AS day,
		ANY_VALUE(sats_per_cent HAVING MIN timestamp) AS spot_open_sats_per_cent,
		ANY_VALUE(sats_per_cent HAVING MAX timestamp) AS spot_close_sats_per_cent

	FROM ${ref("stg_coincap_prices")}

	WHERE sats_per_cent IS NOT NULL

	GROUP BY day

)

SELECT *

FROM
	UNNEST (
		GENERATE_DATE_ARRAY(
			(SELECT DATE(MIN(first_timestamp)) FROM ${ref("stg_static_values")}),
			CURRENT_DATE(),
			INTERVAL 1 DAY
		)
	) AS day
	LEFT JOIN daily_swap_prices USING (day)
	LEFT JOIN daily_spot_prices USING (day)
