config {
	type: "table",
	assertions: {
		rowConditions: [
			"IF(contact_name_same_as_username, contact_name_set, TRUE)"
		]
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	l.id as account_id,
	r.id AS contact_account_id,
	COALESCE(m.name != "", FALSE) AS contact_name_set,
	COALESCE(m.id = m.name, FALSE) AS contact_name_same_as_username,
	m.transactionsCount AS n_transactions,

FROM ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} l
	CROSS JOIN UNNEST(contacts) m
	INNER JOIN ${ref({
		name: "accounts",
		schema: envs.currentSchema("_galoy_raw")
	})} r ON m.id = r.username
