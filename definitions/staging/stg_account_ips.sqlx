config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH ip_keys AS (
	SELECT ip,
		ROW_NUMBER() OVER () AS ip_key
	FROM (
		SELECT DISTINCT ip
		FROM ${ref({
			name: "accountips",
			schema: envs.currentSchema("_galoy_raw")
		})}
	)
)

SELECT
	CASE
		WHEN ip = "**************" THEN ip
	END AS ip,
	ip_key,
	accountId AS account_oid,
	firstConnection as first_connection,
	lastConnection as last_connection,
	metadata.region,
	metadata.country,
	metadata.proxy,
	metadata.provider,
	metadata.city,
	metadata.asn

FROM ${ref({
		name: "accountips",
		schema: envs.currentSchema("_galoy_raw")
	})}
	JOIN ip_keys USING (ip)
