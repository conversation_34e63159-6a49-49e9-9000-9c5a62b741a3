config {
	type: "table",
	assertions: {
		uniqueKey: ["user_key"],
		nonNull: ["recorded_at", "user_key"]
	},
	bigquery: {
		clusterBy: ["user_key"],
		labels: {
			execution_env: envs.current
		},
	},
}

WITH numbered_inbound_transactions AS (

	SELECT user_key, recorded_at, counter_party_user_key, settlement_method, usd_user_volume,
		ROW_NUMBER() OVER (
			PARTITION BY user_key
			ORDER BY recorded_at
		) AS transaction_number,

	FROM ${ref("stg_user_wallet_transactions")}

	WHERE user_direction = "Inbound"
		AND settlement_method = "Intra-ledger"
		AND counter_party_user_key IS NOT NULL
		AND NOT is_quiz_reward

)

SELECT user_key, recorded_at, settlement_method, usd_user_volume, created_at,
	TIMESTAMP_DIFF(recorded_at, created_at, SECOND) AS seconds_after_created_at,
	counter_party_user_key AS onboarded_by_user_key,

FROM numbered_inbound_transactions
	JOIN ${ref("stg_user_accounts")} USING (user_key)

WHERE transaction_number = 1
