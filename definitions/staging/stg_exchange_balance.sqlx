config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH trading_bal AS (

	SELECT
		  recorded_at
		, ARRAY_AGG(balance IGNORE NULLS ORDER BY bill_id DESC LIMIT 1) [SAFE_OFFSET(0)] AS trading_balance
	FROM ${ref("stg_okex_bills_corrected")}
	GROUP BY recorded_at

), funding_bal AS (

	SELECT
		  recorded_at
		, ARRAY_AGG(after_balance IGNORE NULLS ORDER BY id DESC LIMIT 1) [SAFE_OFFSET(0)] AS funding_balance
	FROM ${ref("stg_okx_funding_account_history")}
	GROUP BY recorded_at

), joinned AS (

	SELECT
		  recorded_at
		, trading_balance
		, funding_balance
	FROM trading_bal
	FULL JOIN funding_bal USING (recorded_at)

), grouped AS (

	SELECT
		  TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute
		, ARRAY_AGG(funding_balance IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS funding_balance
		, ARRAY_AGG(trading_balance IGNORE NULLS ORDER BY recorded_at DESC LIMIT 1) [SAFE_OFFSET(0)] AS trading_balance
	FROM joinned
	GROUP BY minute

), final AS (

	SELECT
		  minute
		, LAST_VALUE(funding_balance IGNORE NULLS) OVER (w) AS funding_balance
		, LAST_VALUE(trading_balance IGNORE NULLS) OVER (w) AS trading_balance
	FROM grouped

	WINDOW w AS (
		ORDER BY minute
		ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
	)

)

SELECT
	  minute
	, COALESCE(funding_balance, 0) AS funding_balance
	, COALESCE(trading_balance, 0) AS trading_balance
  , COALESCE(funding_balance, 0) + COALESCE(trading_balance, 0) AS exchange_balance
FROM final
