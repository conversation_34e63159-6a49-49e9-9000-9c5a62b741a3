config {
	type: "table",
	assertions: {
		uniqueKey: ["user_key"]
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH funding AS (

	SELECT user_key,
		SUM(usd_stablesats_funding_revenue) AS usd_stablesats_funding_revenue,

	FROM ${ref("stg_daily_user_funding_revenue")}

	GROUP BY user_key

), quiz_rewards AS (

	SELECT user_key,
		COUNT(DISTINCT transaction_key) AS number_of_quiz_rewards,

	FROM ${ref("stg_user_wallet_transactions")}

	WHERE is_quiz_reward
		AND NOT voided
		AND NOT voider

	GROUP BY user_key

), simple AS (

	SELECT user_key,
		COUNT(DISTINCT DATE(recorded_at)) AS days_transacting,
		COUNT(DISTINCT DATE_TRUNC(DATE(recorded_at), WEEK)) AS weeks_transacting,
		COUNT(DISTINCT DATE_TRUNC(DATE(recorded_at), MONTH)) AS months_transacting,
		COUNTIF(
			user_direction != "Intra-user transfer"
		) AS number_of_transactions,
		COUNTIF(
			user_direction = "Inbound"
		) AS number_of_inbound_transactions,
		COUNTIF(
			user_direction = "Outbound"
		) AS number_of_outbound_transactions,
		COUNTIF(
			settlement_method = "Intra-ledger"
		) AS number_of_intraledger_transactions,
		COUNTIF(settlement_method = "Onchain") AS number_of_onchain_transactions,
		COUNTIF(settlement_method = "Lightning") AS number_of_lightning_transactions,
		SUM(IF(user_direction != "Intra-user transfer",
			usd_user_volume,
			0
		)) AS usd_user_volume,
		SUM(IF(currency="USD" AND user_direction != "Intra-user transfer",
			usd_user_volume,
			0
		)) AS usd_bitcoin_user_volume,
		SUM(IF(currency="BTC" AND user_direction != "Intra-user transfer",
			usd_user_volume,
			0
		)) AS usd_stablesats_user_volume,
		SUM(usd_bank_volume) AS usd_bank_volume,
		COALESCE(SUM(sat_onchain_fee_revenue), 0) AS sat_onchain_fee_revenue,
		COALESCE(SUM(sat_stablesats_spread_revenue), 0) AS sat_stablesats_spread_revenue,
		COALESCE(SUM(usd_onchain_fee_revenue), 0) AS usd_onchain_fee_revenue,
		COALESCE(SUM(usd_stablesats_spread_revenue), 0) AS usd_stablesats_spread_revenue,
		COUNT(DISTINCT counter_party_user_key) AS number_of_intraledger_counter_parties,
		SUM(IF(currency="USD", balance_change, 0)) / 100 AS usd_stablesats_balance,
		(SUM(IF(currency="BTC", balance_change, 0)) / (
			SELECT ANY_VALUE(sats_per_cent HAVING MAX timestamp)
			FROM ${ref("stg_coincap_prices")}
		) / 100) AS usd_bitcoin_balance,
		ANY_VALUE(settlement_method HAVING MIN recorded_at) AS first_settlement_method,
		ANY_VALUE(usd_user_volume HAVING MIN recorded_at) AS first_usd_user_volume,
		LOGICAL_OR(currency = "USD") AS used_stablesats,
		SUM(IF(currency="BTC", balance_change, 0)) AS sat_bitcoin_balance,

	FROM ${ref("stg_user_wallet_transactions")}

	WHERE NOT is_quiz_reward
		AND NOT voided
		AND NOT voider

	GROUP BY user_key

)

SELECT simple.*,
	COALESCE(usd_stablesats_funding_revenue, 0) AS usd_stablesats_funding_revenue,
	COALESCE(number_of_quiz_rewards, 0) AS number_of_quiz_rewards,

FROM simple
	LEFT JOIN funding USING (user_key)
	LEFT JOIN quiz_rewards USING (user_key)
