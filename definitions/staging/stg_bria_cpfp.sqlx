config {
	type: "table",
	assertions: {
		uniqueKey: ["child_batch_id", "payout_id"],
		nonNull: ["child_batch_id", "total_cpfp_fee_sats", "batch_id", "cpfp_fee_sats", "payout_id"],
	},
	bigquery: {
		clusterBy: ["payout_id"],
		labels: {
			execution_env: envs.current
		}
	}
}

pre_operations {
	CREATE TEMP FUNCTION extract_cpfp_bumped_batches(input JSON)
	RETURNS ARRAY<STRUCT<batch_id STRING, bump_fee INT64>>
	LANGUAGE js AS r"""
		let result = [];
		for (let key in input) {
			if (input.hasOwnProperty(key)) {
				for (let innerKey in input[key]) {
					if (input[key].hasOwnProperty(innerKey)) {
						let { batch_id, bump_fee } = input[key][innerKey];
						result.push({ batch_id, bump_fee });
					}
				}
			}
		}
		return result;
	""";
}

SELECT
	child.batch_id AS child_batch_id,
	cpfp_fee_sats AS total_cpfp_fee_sats,
	cpfp_details.batch_id,
	cpfp_details.bump_fee AS cpfp_fee_sats,
	payout_id,

FROM ${ref("stg_bria_batch_summaries")} AS child,
	UNNEST(extract_cpfp_bumped_batches(cpfp_details)) AS cpfp_details
	LEFT JOIN ${ref("stg_bria_payouts")} AS payouts ON cpfp_details.batch_id = payouts.batch_id

WHERE cpfp_fee_sats > 0
