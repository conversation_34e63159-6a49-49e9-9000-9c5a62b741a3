config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  },
}

WITH trades AS (

  SELECT
    *,
    SUM(sat_fee) OVER (PARTITION BY order_id) AS sum_sat_fee,
    SUM(sat_pnl) OVER (PARTITION BY order_id) AS sum_sat_pnl,
    MIN(price) OVER (PARTITION BY order_id) AS min_price,
    AVG(price) OVER (PARTITION BY order_id) AS avg_price,
    SUM(price * quantity) OVER (PARTITION BY order_id) / SUM(quantity) OVER (PARTITION BY order_id) AS vwap_price,
    MAX(price) OVER (PARTITION BY order_id) AS max_price,
    MIN(price) OVER (PARTITION BY order_id) != MAX(price) OVER (PARTITION BY order_id) AS slippage_in_order,
    SAFE_DIVIDE(1, (SAFE_DIVIDE(sat_pnl/100000000, quantity) + SAFE_DIVIDE(1, price))) as open_price,
  FROM ${ref("stg_okex_trades")}

), trades_with_slippage as (

  SELECT
    *,
    MIN(open_price) OVER (PARTITION BY order_id) as min_open_price,
    MAX(open_price) OVER (PARTITION BY order_id) as max_open_price,
    CASE WHEN sub_type = 'Buy'
        THEN sat_pnl - (SAFE_DIVIDE(1, MIN(open_price) OVER (PARTITION BY order_id)) - SAFE_DIVIDE(1, price)) * quantity * 100000000
        -- for sell slippage,
        -- instead of quantity it should be exposure
        -- as the penalty in slippage applies to all open contracts we need to buy back at the worsen price
        -- ELSE (SAFE_DIVIDE(1, MAX(open_price) OVER (PARTITION BY order_id)) - SAFE_DIVIDE(1, price)) * quantity * 100000000
        ELSE 0
    END AS slippage_sats,
  FROM trades

)

SELECT
  MAX(recorded_at) AS recorded_at,
  order_id,
  SUM(quantity) AS quantity,
  COUNT(*) AS trade_count,
  MAX(sum_sat_fee) AS sat_fee,
  MAX(sum_sat_pnl) AS sat_pnl,
  MAX(min_price) AS min_price,
  MAX(avg_price) AS avg_price,
  MAX(vwap_price) AS vwap_price,
  MAX(max_price) AS max_price,
  MAX(slippage_in_order) AS slippage_in_order,
  MIN(open_price) AS min_open_price,
  MAX(open_price) AS max_open_price,
  SUM(slippage_sats) AS slippage_sats,
  CASE WHEN sub_type = 'Buy'
      THEN SUM((price - min_price) * quantity)
      ELSE SUM((price - max_price) * quantity)
  END AS slippage_usd,
FROM trades_with_slippage
GROUP BY order_id, sub_type
