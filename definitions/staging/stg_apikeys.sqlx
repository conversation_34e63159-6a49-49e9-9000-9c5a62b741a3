config {
	type: "table",
	assertions: {
		uniqueKey: ["key_id"],
		nonNull: ["key_id", "kratos_user_id"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

SELECT
	identity_api_keys.id AS key_id,
	subject_id AS kratos_user_id,
	identities.created_at AS api_id_created_at,
	identity_id AS api_id,
	identity_api_keys.created_at,
	expires_at,
	revoked,
	revoked_at,
	read_only,

FROM ${ref({
		name: "identity_api_keys",
		schema: envs.currentSchema("_apikeys_raw")
	})} identity_api_keys
	LEFT JOIN ${ref({
		name: "identities",
		schema: envs.currentSchema("_apikeys_raw")
	})} identities ON identities.id = identity_id
