config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH minute_exposure AS (

  SELECT DISTINCT
    TIMESTAMP_TRUNC(TIMESTAMP_ADD(recorded_at, INTERVAL 30 SECOND), MINUTE, "UTC") AS minute,
    ANY_VALUE(ABS(exposure_usd) HAVING MAX recorded_at) AS exposure_usd,
	FROM ${ref("stg_okx_exposure")}
  GROUP BY minute

), minute_liability AS (

  SELECT
    minute,
    ABS(stablesats_usd_liability) AS stablesats_usd_liability,
    ABS(galoy_usd_liability) AS galoy_usd_liability,
  FROM ${ref("stg_liability_reconciliation")}

), exposure_liability_sync AS (

  SELECT
    minute,
    exposure_usd,
    stablesats_usd_liability,
    galoy_usd_liability,
  FROM minute_exposure
  FULL JOIN minute_liability USING (minute)

), exposure_liability_filled AS (

  SELECT
    minute,
    LAST_VALUE(exposure_usd IGNORE NULLS) OVER (w) AS exposure_usd,
    LAST_VALUE(stablesats_usd_liability IGNORE NULLS) OVER (w) AS stablesats_usd_liability,
    LAST_VALUE(galoy_usd_liability IGNORE NULLS) OVER (w) AS galoy_usd_liability,
    FROM exposure_liability_sync
  WINDOW w AS (
    ORDER BY minute
    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
  )

), exposure_liability AS (

  SELECT
    minute,
    exposure_usd,
    stablesats_usd_liability,
    galoy_usd_liability,
    SAFE_DIVIDE(exposure_usd, stablesats_usd_liability) AS stablesats_exposure_liability_ratio,
    SAFE_DIVIDE(exposure_usd, galoy_usd_liability) AS galoy_exposure_liability_ratio,
  FROM exposure_liability_filled
  WHERE stablesats_usd_liability != 0 OR galoy_usd_liability != 0

)

SELECT
  minute,
  exposure_usd,
  stablesats_usd_liability,
  galoy_usd_liability,
  stablesats_exposure_liability_ratio,
  galoy_exposure_liability_ratio,
FROM exposure_liability
