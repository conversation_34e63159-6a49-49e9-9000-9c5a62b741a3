config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT

	client_transfer_id,
	correlation_id,
	action,
	currency,
	CAST(amount AS BIGNUMERIC) AS amount,
	CAST(fee AS BIGNUMERIC) AS fee,
	transfer_from,
	transfer_to,
	CAST(target_usd_exposure AS NUMERIC) AS target_usd_exposure,
	CAST(current_usd_exposure AS NUMERIC) AS current_usd_exposure,
	CAST(trading_btc_used_balance AS NUMERIC) AS trading_btc_used_balance,
	CAST(trading_btc_total_balance AS NUMERIC) AS trading_btc_total_balance,
	CAST(current_usd_btc_price AS NUMERIC) AS current_usd_btc_price,
	CAST(funding_btc_total_balance AS NUMERIC) AS funding_btc_total_balance,
	lost,
	transfer_id,
	state,
	created_at,

FROM ${ref({
	name: "pg_stablesats_public_okex_transfers",
	schema: envs.currentSchema("_kafka_raw")
})}
WHERE _PARTITIONTIME > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
