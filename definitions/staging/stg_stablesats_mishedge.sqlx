config {
  type: "view",
  assertions: {
    uniqueKey: ["mishedge_id"],
    nonNull: ["mishedge_id","recorded_at","who_traded","deposit","balance"],
    rowConditions: [
      "deposit = short_deposit - long_deposit",
    ]
  },
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

-- todo assert that bill_id and journal_id are unique

SELECT *,
	long_balance - LAG(long_balance, 1, 0) OVER (ORDER BY mishedge_id) as long_deposit,
	short_balance - LAG(short_balance, 1, 0) OVER (ORDER BY mishedge_id) as short_deposit,
FROM (
	SELECT *,
		IF(balance < 0, -balance, 0) as long_balance,
		IF(balance > 0, balance, 0) as short_balance
	FROM (
		SELECT
			recorded_at,
			ROW_NUMBER() OVER(ORDER BY recorded_at) as mishedge_id,
			who_traded,
			journal_id,
			bill_id,
			deposit,
			SUM(deposit) OVER (
				ORDER BY recorded_at
				ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
			) as balance,
			sats_per_cent
		FROM (
			SELECT
				recorded_at,
				"User" as who_traded,
				journal_id,
				NULL as bill_id,
				dealer_cent_deposit AS deposit,
				(sats_per_cent_market_buy+sats_per_cent_market_sell)/2 AS sats_per_cent
			FROM ${ref("stg_trade_spreads")}
			UNION ALL
			SELECT
				recorded_at,
				"Exchange" as who_traded,
				NULL as journal_id,
				bill_id,
				COALESCE(hedge_cent_deposit, 0) AS deposit,
				sats_per_cent
			FROM ${ref("stg_stablesats_exchange_trades")}
		)
	)
)