config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT "Afghanistan" AS country_name, "Afghanistan" AS country_short_name, "004" AS numeric_code, "93" AS phone_number_code, "AF" AS alpha_2_code, "AFG" AS alpha_3_code UNION ALL
SELECT "Albania", "Albania", "008", "355", "AL", "ALB" UNION ALL
SELECT "Algeria", "Algeria", "012", "213", "DZ", "DZA" UNION ALL
SELECT "American Samoa", NULL,  "016", NULL,  "AS", "ASM" UNION ALL
SELECT "Andorra", "Andorra", "020", "376", "AD", "AND" UNION ALL
SELECT "Angola", "Angola", "024", "244", "AO", "AGO" UNION ALL
SELECT "Anguilla", NULL,  "660", NULL,  "AI", "AIA" UNION ALL
SELECT "Antarctica", "Antarctica", "010", "672", "AQ", "ATA" UNION ALL
SELECT "Antigua and Barbuda", NULL,  "028", NULL,  "AG", "ATG" UNION ALL
SELECT "Argentina", "Argentina", "032", "54", "AR", "ARG" UNION ALL
SELECT "Armenia", "Armenia", "051", "374", "AM", "ARM" UNION ALL
SELECT "Aruba", "Aruba", "533", "297", "AW", "ABW" UNION ALL
SELECT "Australia", "Australia", "036", "61", "AU", "AUS" UNION ALL
SELECT "Austria", "Austria", "040", "43", "AT", "AUT" UNION ALL
SELECT "Azerbaijan", "Azerbaijan", "031", "994", "AZ", "AZE" UNION ALL
SELECT "Bahamas (the)", NULL,  "044", NULL,  "BS", "BHS" UNION ALL
SELECT "Bahrain", "Bahrain", "048", "973", "BH", "BHR" UNION ALL
SELECT "Bangladesh", "Bangladesh", "050", "880", "BD", "BGD" UNION ALL
SELECT "Barbados", NULL,  "052", NULL,  "BB", "BRB" UNION ALL
SELECT "Belarus", "Belarus", "112", "375", "BY", "BLR" UNION ALL
SELECT "Belgium", "Belgium", "056", "32", "BE", "BEL" UNION ALL
SELECT "Belize", "Belize", "084", "501", "BZ", "BLZ" UNION ALL
SELECT "Benin", "Benin", "204", "229", "BJ", "BEN" UNION ALL
SELECT "Bermuda", NULL,  "060", NULL,  "BM", "BMU" UNION ALL
SELECT "Bhutan", "Bhutan", "064", "975", "BT", "BTN" UNION ALL
SELECT "Bolivia (Plurinational State of)", "Bolivia", "068", "591", "BO", "BOL" UNION ALL
SELECT "Bonaire, Sint Eustatius and Saba", NULL,  "535", NULL,  "BQ", "BES" UNION ALL
SELECT "Bosnia and Herzegovina", "Bosnia and Herzegovina", "070", "387", "BA", "BIH" UNION ALL
SELECT "Botswana", "Botswana", "072", "267", "BW", "BWA" UNION ALL
SELECT "Bouvet Island", NULL,  "074", NULL,  "BV", "BVT" UNION ALL
SELECT "Brazil", "Brazil", "076", "55", "BR", "BRA" UNION ALL
SELECT "British Indian Ocean Territory (the)", "British Indian Ocean Territory", "086", "246", "IO", "IOT" UNION ALL
SELECT "Brunei Darussalam", "Brunei", "096", "673", "BN", "BRN" UNION ALL
SELECT "Bulgaria", "Bulgaria", "100", "359", "BG", "BGR" UNION ALL
SELECT "Burkina Faso", "Burkina Faso", "854", "226", "BF", "BFA" UNION ALL
SELECT "Burundi", "Burundi", "108", "257", "BI", "BDI" UNION ALL
SELECT "Cabo Verde", "Cape Verde", "132", "238", "CV", "CPV" UNION ALL
SELECT "Cambodia", "Cambodia", "116", "855", "KH", "KHM" UNION ALL
SELECT "Cameroon", "Cameroon", "120", "237", "CM", "CMR" UNION ALL
SELECT "Canada", "Canada", "124", "1", "CA", "CAN" UNION ALL
SELECT "Cayman Islands (the)", NULL,  "136", NULL,  "KY", "CYM" UNION ALL
SELECT "Central African Republic (the)", "Central African Republic", "140", "236", "CF", "CAF" UNION ALL
SELECT "Chad", "Chad", "148", "235", "TD", "TCD" UNION ALL
SELECT "Chile", "Chile", "152", "56", "CL", "CHL" UNION ALL
SELECT "China", "China", "156", "86", "CN", "CHN" UNION ALL
SELECT "Christmas Island", "Christmas Island", "162", "61", "CX", "CXR" UNION ALL
SELECT "Cocos (Keeling) Islands (the)", "Cocos Islands", "166", "61", "CC", "CCK" UNION ALL
SELECT "Colombia", "Colombia", "170", "57", "CO", "COL" UNION ALL
SELECT "Comoros (the)", "Comoros", "174", "269", "KM", "COM" UNION ALL
SELECT "Congo (the Democratic Republic of the)", "Democratic Republic of the Congo", "180", "243", "CD", "COD" UNION ALL
SELECT "Congo (the)", "Republic of the Congo", "178", "242", "CG", "COG" UNION ALL
SELECT "Cook Islands (the)", "Cook Islands", "184", "682", "CK", "COK" UNION ALL
SELECT "Costa Rica", "Costa Rica", "188", "506", "CR", "CRI" UNION ALL
SELECT "Croatia", "Croatia", "191", "385", "HR", "HRV" UNION ALL
SELECT "Cuba", "Cuba", "192", "53", "CU", "CUB" UNION ALL
SELECT "Curaçao", "Curacao", "531", "599", "CW", "CUW" UNION ALL
SELECT "Cyprus", "Cyprus", "196", "357", "CY", "CYP" UNION ALL
SELECT "Czechia", "Czech Republic", "203", "420", "CZ", "CZE" UNION ALL
SELECT "Côte d'Ivoire", "Ivory Coast", "384", "225", "CI", "CIV" UNION ALL
SELECT "Denmark", "Denmark", "208", "45", "DK", "DNK" UNION ALL
SELECT "Djibouti", "Djibouti", "262", "253", "DJ", "DJI" UNION ALL
SELECT "Dominica", NULL,  "212", NULL,  "DM", "DMA" UNION ALL
SELECT "Dominican Republic (the)", "Dominican Republic",  "214", NULL,  "DO", "DOM" UNION ALL
SELECT "Ecuador", "Ecuador", "218", "593", "EC", "ECU" UNION ALL
SELECT "Egypt", "Egypt", "818", "20", "EG", "EGY" UNION ALL
SELECT "El Salvador", "El Salvador", "222", "503", "SV", "SLV" UNION ALL
SELECT "Equatorial Guinea", "Equatorial Guinea", "226", "240", "GQ", "GNQ" UNION ALL
SELECT "Eritrea", "Eritrea", "232", "291", "ER", "ERI" UNION ALL
SELECT "Estonia", "Estonia", "233", "372", "EE", "EST" UNION ALL
SELECT "Eswatini", "Swaziland", "748", "268", "SZ", "SWZ" UNION ALL
SELECT "Ethiopia", "Ethiopia", "231", "251", "ET", "ETH" UNION ALL
SELECT "Falkland Islands (the) [Malvinas]", "Falkland Islands", "238", "500", "FK", "FLK" UNION ALL
SELECT "Faroe Islands (the)", "Faroe Islands", "234", "298", "FO", "FRO" UNION ALL
SELECT "Fiji", "Fiji", "242", "679", "FJ", "FJI" UNION ALL
SELECT "Finland", "Finland", "246", "358", "FI", "FIN" UNION ALL
SELECT "France", "France", "250", "33", "FR", "FRA" UNION ALL
SELECT "French Guiana", NULL,  "254", NULL,  "GF", "GUF" UNION ALL
SELECT "French Polynesia", "French Polynesia", "258", "689", "PF", "PYF" UNION ALL
SELECT "French Southern Territories (the)", NULL,  "260", NULL,  "TF", "ATF" UNION ALL
SELECT "Gabon", "Gabon", "266", "241", "GA", "GAB" UNION ALL
SELECT "Gambia (the)", "Gambia", "270", "220", "GM", "GMB" UNION ALL
SELECT "Georgia", "Georgia", "268", "995", "GE", "GEO" UNION ALL
SELECT "Germany", "Germany", "276", "49", "DE", "DEU" UNION ALL
SELECT "Ghana", "Ghana", "288", "233", "GH", "GHA" UNION ALL
SELECT "Gibraltar", "Gibraltar", "292", "350", "GI", "GIB" UNION ALL
SELECT "Greece", "Greece", "300", "30", "GR", "GRC" UNION ALL
SELECT "Greenland", "Greenland", "304", "299", "GL", "GRL" UNION ALL
SELECT "Grenada", NULL,  "308", NULL,  "GD", "GRD" UNION ALL
SELECT "Guadeloupe", NULL,  "312", NULL,  "GP", "GLP" UNION ALL
SELECT "Guam", NULL,  "316", NULL,  "GU", "GUM" UNION ALL
SELECT "Guatemala", "Guatemala", "320", "502", "GT", "GTM" UNION ALL
SELECT "Guernsey", NULL,  "831", NULL,  "GG", "GGY" UNION ALL
SELECT "Guinea", "Guinea", "324", "224", "GN", "GIN" UNION ALL
SELECT "Guinea-Bissau", NULL,  "624", NULL,  "GW", "GNB" UNION ALL
SELECT "Guyana", "Guyana", "328", "592", "GY", "GUY" UNION ALL
SELECT "Haiti", "Haiti", "332", "509", "HT", "HTI" UNION ALL
SELECT "Heard Island and McDonald Islands", NULL,  "334", NULL,  "HM", "HMD" UNION ALL
SELECT "Holy See (the)", "Vatican", "336", "379", "VA", "VAT" UNION ALL
SELECT "Honduras", "Honduras", "340", "504", "HN", "HND" UNION ALL
SELECT "Hong Kong", "Hong Kong", "344", "852", "HK", "HKG" UNION ALL
SELECT "Hungary", "Hungary", "348", "36", "HU", "HUN" UNION ALL
SELECT "Iceland", "Iceland", "352", "354", "IS", "ISL" UNION ALL
SELECT "India", "India", "356", "91", "IN", "IND" UNION ALL
SELECT "Indonesia", "Indonesia", "360", "62", "ID", "IDN" UNION ALL
SELECT "Iran (Islamic Republic of)", "Iran", "364", "98", "IR", "IRN" UNION ALL
SELECT "Iraq", "Iraq", "368", "964", "IQ", "IRQ" UNION ALL
SELECT "Ireland", "Ireland", "372", "353", "IE", "IRL" UNION ALL
SELECT "Isle of Man", NULL,  "833", NULL,  "IM", "IMN" UNION ALL
SELECT "Israel", "Israel", "376", "972", "IL", "ISR" UNION ALL
SELECT "Italy", "Italy", "380", "39", "IT", "ITA" UNION ALL
SELECT "Jamaica", NULL,  "388", NULL,  "JM", "JAM" UNION ALL
SELECT "Japan", "Japan", "392", "81", "JP", "JPN" UNION ALL
SELECT "Jersey", NULL,  "832", NULL,  "JE", "JEY" UNION ALL
SELECT "Jordan", "Jordan", "400", "962", "JO", "JOR" UNION ALL
SELECT "Kazakhstan", "Kazakhstan", "398", "7", "KZ", "KAZ" UNION ALL
SELECT "Kenya", "Kenya", "404", "254", "KE", "KEN" UNION ALL
SELECT "Kiribati", "Kiribati", "296", "686", "KI", "KIR" UNION ALL
SELECT "Korea (the Democratic People's Republic of)", "North Korea", "408", "850", "KP", "PRK" UNION ALL
SELECT "Korea (the Republic of)", "South Korea", "410", "82", "KR", "KOR" UNION ALL
SELECT "Kuwait", "Kuwait", "414", "965", "KW", "KWT" UNION ALL
SELECT "Kyrgyzstan", "Kyrgyzstan", "417", "996", "KG", "KGZ" UNION ALL
SELECT "Lao People's Democratic Republic (the)", "Laos", "418", "856", "LA", "LAO" UNION ALL
SELECT "Latvia", "Latvia", "428", "371", "LV", "LVA" UNION ALL
SELECT "Lebanon", "Lebanon", "422", "961", "LB", "LBN" UNION ALL
SELECT "Lesotho", "Lesotho", "426", "266", "LS", "LSO" UNION ALL
SELECT "Liberia", "Liberia", "430", "231", "LR", "LBR" UNION ALL
SELECT "Libya", "Libya", "434", "218", "LY", "LBY" UNION ALL
SELECT "Liechtenstein", "Liechtenstein", "438", "423", "LI", "LIE" UNION ALL
SELECT "Lithuania", "Lithuania", "440", "370", "LT", "LTU" UNION ALL
SELECT "Luxembourg", "Luxembourg", "442", "352", "LU", "LUX" UNION ALL
SELECT "Macao", "Macau", "446", "853", "MO", "MAC" UNION ALL
SELECT "Madagascar", "Madagascar", "450", "261", "MG", "MDG" UNION ALL
SELECT "Malawi", "Malawi", "454", "265", "MW", "MWI" UNION ALL
SELECT "Malaysia", "Malaysia", "458", "60", "MY", "MYS" UNION ALL
SELECT "Maldives", "Maldives", "462", "960", "MV", "MDV" UNION ALL
SELECT "Mali", "Mali", "466", "223", "ML", "MLI" UNION ALL
SELECT "Malta", "Malta", "470", "356", "MT", "MLT" UNION ALL
SELECT "Marshall Islands (the)", "Marshall Islands", "584", "692", "MH", "MHL" UNION ALL
SELECT "Martinique", NULL,  "474", NULL,  "MQ", "MTQ" UNION ALL
SELECT "Mauritania", "Mauritania", "478", "222", "MR", "MRT" UNION ALL
SELECT "Mauritius", "Mauritius", "480", "230", "MU", "MUS" UNION ALL
SELECT "Mayotte", "Mayotte", "175", "262", "YT", "MYT" UNION ALL
SELECT "Mexico", "Mexico", "484", "52", "MX", "MEX" UNION ALL
SELECT "Micronesia (Federated States of)", "Micronesia", "583", "691", "FM", "FSM" UNION ALL
SELECT "Moldova (the Republic of)", "Moldova", "498", "373", "MD", "MDA" UNION ALL
SELECT "Monaco", "Monaco", "492", "377", "MC", "MCO" UNION ALL
SELECT "Mongolia", "Mongolia", "496", "976", "MN", "MNG" UNION ALL
SELECT "Montenegro", "Montenegro", "499", "382", "ME", "MNE" UNION ALL
SELECT "Montserrat", NULL,  "500", NULL,  "MS", "MSR" UNION ALL
SELECT "Morocco", "Morocco", "504", "212", "MA", "MAR" UNION ALL
SELECT "Mozambique", "Mozambique", "508", "258", "MZ", "MOZ" UNION ALL
SELECT "Myanmar", "Myanmar", "104", "95", "MM", "MMR" UNION ALL
SELECT "Namibia", "Namibia", "516", "264", "NA", "NAM" UNION ALL
SELECT "Nauru", "Nauru", "520", "674", "NR", "NRU" UNION ALL
SELECT "Nepal", "Nepal", "524", "977", "NP", "NPL" UNION ALL
SELECT "Netherlands (the)", "Netherlands", "528", "31", "NL", "NLD" UNION ALL
SELECT "New Caledonia", "New Caledonia", "540", "687", "NC", "NCL" UNION ALL
SELECT "New Zealand", "New Zealand", "554", "64", "NZ", "NZL" UNION ALL
SELECT "Nicaragua", "Nicaragua", "558", "505", "NI", "NIC" UNION ALL
SELECT "Niger (the)", "Niger", "562", "227", "NE", "NER" UNION ALL
SELECT "Nigeria", "Nigeria", "566", "234", "NG", "NGA" UNION ALL
SELECT "Niue", "Niue", "570", "683", "NU", "NIU" UNION ALL
SELECT "Norfolk Island", NULL,  "574", NULL,  "NF", "NFK" UNION ALL
SELECT "Northern Mariana Islands (the)", NULL,  "580", NULL,  "MP", "MNP" UNION ALL
SELECT "Norway", "Norway", "578", "47", "NO", "NOR" UNION ALL
SELECT "Oman", "Oman", "512", "968", "OM", "OMN" UNION ALL
SELECT "Pakistan", "Pakistan", "586", "92", "PK", "PAK" UNION ALL
SELECT "Palau", "Palau", "585", "680", "PW", "PLW" UNION ALL
SELECT "Palestine, State of", "Palestine", "275", "970", "PS", "PSE" UNION ALL
SELECT "Panama", "Panama", "591", "507", "PA", "PAN" UNION ALL
SELECT "Papua New Guinea", "Papua New Guinea", "598", "675", "PG", "PNG" UNION ALL
SELECT "Paraguay", "Paraguay", "600", "595", "PY", "PRY" UNION ALL
SELECT "Peru", "Peru", "604", "51", "PE", "PER" UNION ALL
SELECT "Philippines (the)", "Philippines", "608", "63", "PH", "PHL" UNION ALL
SELECT "Pitcairn", "Pitcairn", "612", "64", "PN", "PCN" UNION ALL
SELECT "Poland", "Poland", "616", "48", "PL", "POL" UNION ALL
SELECT "Portugal", "Portugal", "620", "351", "PT", "PRT" UNION ALL
SELECT "Puerto Rico", NULL,  "630", NULL,  "PR", "PRI" UNION ALL
SELECT "Qatar", "Qatar", "634", "974", "QA", "QAT" UNION ALL
SELECT "Republic of North Macedonia", "Macedonia", "807", "389", "MK", "MKD" UNION ALL
SELECT "Romania", "Romania", "642", "40", "RO", "ROU" UNION ALL
SELECT "Russian Federation (the)", "Russia", "643", "7", "RU", "RUS" UNION ALL
SELECT "Rwanda", "Rwanda", "646", "250", "RW", "RWA" UNION ALL
SELECT "Réunion", "Reunion", "638", "262", "RE", "REU" UNION ALL
SELECT "Saint Barthélemy", "Saint Barthelemy", "652", "590", "BL", "BLM" UNION ALL
SELECT "Saint Helena, Ascension and Tristan da Cunha", "Saint Helena", "654", "290", "SH", "SHN" UNION ALL
SELECT "Saint Kitts and Nevis", NULL,  "659", NULL,  "KN", "KNA" UNION ALL
SELECT "Saint Lucia", NULL,  "662", NULL,  "LC", "LCA" UNION ALL
SELECT "Saint Martin (French part)", "Saint Martin", "663", "590", "MF", "MAF" UNION ALL
SELECT "Saint Pierre and Miquelon", "Saint Pierre and Miquelon", "666", "508", "PM", "SPM" UNION ALL
SELECT "Saint Vincent and the Grenadines", NULL,  "670", NULL,  "VC", "VCT" UNION ALL
SELECT "Samoa", "Samoa", "882", "685", "WS", "WSM" UNION ALL
SELECT "San Marino", "San Marino", "674", "378", "SM", "SMR" UNION ALL
SELECT "Sao Tome and Principe", "Sao Tome and Principe", "678", "239", "ST", "STP" UNION ALL
SELECT "Saudi Arabia", "Saudi Arabia", "682", "966", "SA", "SAU" UNION ALL
SELECT "Senegal", "Senegal", "686", "221", "SN", "SEN" UNION ALL
SELECT "Serbia", "Serbia", "688", "381", "RS", "SRB" UNION ALL
SELECT "Seychelles", "Seychelles", "690", "248", "SC", "SYC" UNION ALL
SELECT "Sierra Leone", "Sierra Leone", "694", "232", "SL", "SLE" UNION ALL
SELECT "Singapore", "Singapore", "702", "65", "SG", "SGP" UNION ALL
SELECT "Sint Maarten (Dutch part)", NULL,  "534", NULL,  "SX", "SXM" UNION ALL
SELECT "Slovakia", "Slovakia", "703", "421", "SK", "SVK" UNION ALL
SELECT "Slovenia", "Slovenia", "705", "386", "SI", "SVN" UNION ALL
SELECT "Solomon Islands", "Solomon Islands", "090", "677", "SB", "SLB" UNION ALL
SELECT "Somalia", "Somalia", "706", "252", "SO", "SOM" UNION ALL
SELECT "South Africa", "South Africa", "710", "27", "ZA", "ZAF" UNION ALL
SELECT "South Georgia and the South Sandwich Islands", NULL,  "239", NULL,  "GS", "SGS" UNION ALL
SELECT "South Sudan", "South Sudan", "728", "211", "SS", "SSD" UNION ALL
SELECT "Spain", "Spain", "724", "34", "ES", "ESP" UNION ALL
SELECT "Sri Lanka", "Sri Lanka", "144", "94", "LK", "LKA" UNION ALL
SELECT "Sudan (the)", "Sudan", "729", "249", "SD", "SDN" UNION ALL
SELECT "Suriname", "Suriname", "740", "597", "SR", "SUR" UNION ALL
SELECT "Svalbard and Jan Mayen", "Svalbard and Jan Mayen", "744", "47", "SJ", "SJM" UNION ALL
SELECT "Sweden", "Sweden", "752", "46", "SE", "SWE" UNION ALL
SELECT "Switzerland", "Switzerland", "756", "41", "CH", "CHE" UNION ALL
SELECT "Syrian Arab Republic", "Syria", "760", "963", "SY", "SYR" UNION ALL
SELECT "Taiwan (Province of China)", "Taiwan", "158", "886", "TW", "TWN" UNION ALL
SELECT "Tajikistan", "Tajikistan", "762", "992", "TJ", "TJK" UNION ALL
SELECT "Tanzania, United Republic of", "Tanzania", "834", "255", "TZ", "TZA" UNION ALL
SELECT "Thailand", "Thailand", "764", "66", "TH", "THA" UNION ALL
SELECT "Timor-Leste", "East Timor", "626", "670", "TL", "TLS" UNION ALL
SELECT "Togo", "Togo", "768", "228", "TG", "TGO" UNION ALL
SELECT "Tokelau", "Tokelau", "772", "690", "TK", "TKL" UNION ALL
SELECT "Tonga", "Tonga", "776", "676", "TO", "TON" UNION ALL
SELECT "Trinidad and Tobago", NULL,  "780", NULL,  "TT", "TTO" UNION ALL
SELECT "Tunisia", "Tunisia", "788", "216", "TN", "TUN" UNION ALL
SELECT "Turkey", "Turkey", "792", "90", "TR", "TUR" UNION ALL
SELECT "Turkmenistan", "Turkmenistan", "795", "993", "TM", "TKM" UNION ALL
SELECT "Turks and Caicos Islands (the)", NULL,  "796", NULL,  "TC", "TCA" UNION ALL
SELECT "Tuvalu", "Tuvalu", "798", "688", "TV", "TUV" UNION ALL
SELECT "Uganda", "Uganda", "800", "256", "UG", "UGA" UNION ALL
SELECT "Ukraine", "Ukraine", "804", "380", "UA", "UKR" UNION ALL
SELECT "United Arab Emirates (the)", "United Arab Emirates", "784", "971", "AE", "ARE" UNION ALL
SELECT "United Kingdom of Great Britain and Northern Ireland (the)", "United Kingdom", "826", "44", "GB", "GBR" UNION ALL
SELECT "United States Minor Outlying Islands (the)", NULL,  "581", NULL,  "UM", "UMI" UNION ALL
SELECT "United States of America (the)", "United States", "840", "1", "US", "USA" UNION ALL
SELECT "Uruguay", "Uruguay", "858", "598", "UY", "URY" UNION ALL
SELECT "Uzbekistan", "Uzbekistan", "860", "998", "UZ", "UZB" UNION ALL
SELECT "Vanuatu", "Vanuatu", "548", "678", "VU", "VUT" UNION ALL
SELECT "Venezuela (Bolivarian Republic of)", "Venezuela", "862", "58", "VE", "VEN" UNION ALL
SELECT "Viet Nam", "Vietnam", "704", "84", "VN", "VNM" UNION ALL
SELECT "Virgin Islands (British)", NULL,  "092", NULL,  "VG", "VGB" UNION ALL
SELECT "Virgin Islands (U.S.)", NULL,  "850", NULL,  "VI", "VIR" UNION ALL
SELECT "Wallis and Futuna", "Wallis and Futuna", "876", "681", "WF", "WLF" UNION ALL
SELECT "Western Sahara", "Western Sahara", "732", "212", "EH", "ESH" UNION ALL
SELECT "Yemen", "Yemen", "887", "967", "YE", "YEM" UNION ALL
SELECT "Zambia", "Zambia", "894", "260", "ZM", "ZMB" UNION ALL
SELECT "Zimbabwe", "Zimbabwe", "716", "263", "ZW", "ZWE" UNION ALL
SELECT "Åland Islands", NULL,  "248", NULL,  "AX", "ALA" UNION ALL
SELECT NULL,  "Netherlands Antilles", NULL,  "599", "AN", "ANT" UNION ALL
SELECT NULL,  "Kosovo", NULL,  "383", "XK", "XKX"
