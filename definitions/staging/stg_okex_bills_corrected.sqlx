config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH history AS (

  SELECT
    bill_id,
    CASE WHEN sub_type IN ('Funding fee expense', 'Funding fee income') THEN 0 ELSE order_id END AS order_id,
  FROM ${ref("stg_okx_trading_account_history")}
  WHERE type != 'transfer'

), bills AS (

  SELECT
    bill_id,
    COALESCE(order_id, 0) AS order_id
  FROM ${ref("stg_okex_bills_archive")}
  WHERE type != 'transfer'
  AND bill_id >= 506618300210769921
  AND bill_id <= 640943374828498953

), ids_to_be_removed_from_db AS (

  --------------------------------------------------------------------------
  -- bills in our db that don't exist in okex official account history
  --------------------------------------------------------------------------
  SELECT
    bill_id,
    order_id,
  FROM bills

  EXCEPT DISTINCT

  SELECT
    bill_id,
    order_id,
  FROM history

), ids_to_be_removed_manually AS (

  --------------------------------------------------------------------------
  -- bills in our db that don't match for odd reasons
  --------------------------------------------------------------------------
  SELECT 475815339230654470 AS bill_id, 0 AS order_id UNION ALL

  -- 2023-12-02 22:34:28.316000 UTC
  SELECT 651310266596388874, 651310266588000260 UNION ALL
  SELECT 651310266596388875, 651310266588000260 UNION ALL
  SELECT 651310266596388876, 651310266588000260 UNION ALL

  -- 2023-12-17 22:36:13.221000 UTC
  SELECT 656746524583841799, 656746524571258885 UNION ALL

  -- 2024-01-01 20:03:48.496000 UTC
  SELECT **********11203592, **********02814980 UNION ALL
  SELECT **********11203594, **********02814980 UNION ALL
  SELECT **********11203595, **********02814980 UNION ALL
  SELECT **********11203596, **********02814980 UNION ALL
  SELECT **********11203597, **********02814980 UNION ALL

  -- 2024-01-12 03:23:22.662000 UTC
  SELECT 665878486737149979, 665878486724567082 UNION ALL

  -- 2024-01-31 17:47:33.572000 UTC
  SELECT 672981334662770719, 672981334654382095 UNION ALL
  SELECT 672981334662770726, 672981334654382095 UNION ALL
  SELECT 672981334662770733, 672981334654382095 UNION ALL
  SELECT 672981334662770737, 672981334654382095 UNION ALL
  SELECT 672981334662770738, 672981334654382095 UNION ALL

  -- 2024-02-05 03:55:50.392000 UTC
  SELECT 674583964883283994, 674583964870701066

), ids_to_be_added AS (

  --------------------------------------------------------------------------
  -- bills in our db that we are missing from okex official account history
  --------------------------------------------------------------------------
  SELECT
    bill_id,
    order_id,
  FROM history

  EXCEPT DISTINCT

  SELECT
    bill_id,
    order_id,
  FROM bills

), rows_to_be_added AS (

  SELECT h.*
  FROM ${ref("stg_okx_trading_account_history")} h
  JOIN ids_to_be_added a
    ON a.bill_id = h.bill_id
    AND a.order_id = h.order_id

), patched_bills AS (

  SELECT
    *
  FROM ${ref("stg_okex_bills_archive")}
  WHERE COALESCE(bill_id, 0) || '_' || COALESCE(order_id, 0)
  NOT IN
  (
      SELECT
        COALESCE(bill_id, 0) || '_' || COALESCE(order_id, 0)
      FROM ids_to_be_removed_from_db

      UNION ALL

      SELECT
        COALESCE(bill_id, 0) || '_' || COALESCE(order_id, 0)
      from ids_to_be_removed_manually
  )

  UNION ALL

  SELECT
    *
  FROM rows_to_be_added

), with_exposure AS (

  SELECT
    *,
    CASE WHEN TYPE = 'fee' THEN -size * 100 ELSE 0 END AS usd_exposure_reported_from_fees,
    SUM(CASE WHEN type = 'trade' THEN CASE WHEN sub_type = 'Sell' THEN -size * 100 ELSE size * 100 END ELSE 0 END) OVER (ORDER BY recorded_at) AS usd_exposure_calculated_from_trades,
    CASE WHEN TYPE = 'trade' THEN CASE WHEN sub_type = 'Sell' THEN -size * 100 ELSE size * 100 END ELSE 0 END AS usd_exposure_change_from_trades,
  FROM patched_bills

)

SELECT
  *
FROM with_exposure
