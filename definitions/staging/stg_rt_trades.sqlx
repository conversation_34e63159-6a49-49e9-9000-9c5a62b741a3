config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	journal_id,
	ANY_VALUE(recorded_at) AS recorded_at,
	ARRAY_AGG(IF(currency="BTC", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)] AS dealer_sat_deposit,
	ARRAY_AGG(IF(currency="USD", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)] AS dealer_cent_deposit,
	SAFE_DIVIDE(
		ABS(ARRAY_AGG(IF(currency="BTC", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)]),
		ABS(ARRAY_AGG(IF(currency="USD", deposit, NULL) IGNORE NULLS LIMIT 1) [SAFE_OFFSET(0)])
	) AS sats_per_cent

FROM (
	SELECT *, COUNT(DISTINCT(currency)) OVER (PARTITION BY journal_id) > 1 AS cross_asset,
	FROM ${ref("stg_rt_journal_entries")}
)

WHERE recorded_at > "2022-06-01"
	AND wallet_id IN (
		SELECT wallet_id
		FROM ${ref("stg_static_wallets")}
		WHERE account_name="dealer"
	)
	AND cross_asset

GROUP BY journal_id

HAVING COUNT(1) = 2
