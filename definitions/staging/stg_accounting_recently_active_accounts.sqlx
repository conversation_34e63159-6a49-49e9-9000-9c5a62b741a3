config {
	type: "table",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	},
}

WITH recently_active AS (

	SELECT DISTINCT account_id
	FROM ${ref("stg_accounting_recent_transactions")}

), transaction_summaries AS (

	SELECT account_id,
		ANY_VALUE(level) AS level,
		COUNT(DISTINCT journal_id) AS number_of_transactions,
		SUM(cents_amount) AS cent_volume,
		SUM(IF(
			user_direction="Outbound",
			cents_amount,
		0)) AS outbound_volume,
		SUM(IF(
			user_direction="Inbound",
			cents_amount,
		0)) AS inbound_volume,

	FROM ${ref("stg_accounting_recent_transactions")}
		JOIN ${ref("stg_user_accounts")} USING (account_id)

	GROUP BY account_id

), ranks AS (

	SELECT account_id,
		DENSE_RANK() OVER (ORDER BY number_of_transactions DESC) rank_by_number_of_transactions,
		DENSE_RANK() OVER (ORDER BY cent_volume DESC) rank_by_volume,
		DENSE_RANK() OVER (ORDER BY inbound_volume DESC) rank_by_inbound_volume,
		DENSE_RANK() OVER (ORDER BY outbound_volume DESC) rank_by_outbound_volume,
		DENSE_RANK() OVER (
			PARTITION BY level
			ORDER BY cent_volume DESC
		) rank_by_volume_by_level,

	FROM transaction_summaries

)

SELECT
	account_id,
	created_at,
	merchant,
	level,
	language,
	kratos_user_id,
	username,
	phone_number_country_name,
	display_currency,
	currency AS default_wallet_currency,
	COALESCE(
		username,
		role,
		wallet_id
	) AS account_name,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_brand, "New account")
		ELSE COALESCE(device_brand, "No GA data")
	END AS device_brand,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_model, "New account")
		ELSE COALESCE(device_model, "No GA data")
	END AS device_model,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_os, "New account")
		ELSE COALESCE(device_os, "No GA data")
	END AS device_os,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_os_version, "New account")
		ELSE COALESCE(device_os_version, "No GA data")
	END AS device_os_version,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_language, "New account")
		ELSE COALESCE(device_language, "No GA data")
	END AS device_language,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_continent, "New account")
		ELSE COALESCE(device_continent, "No GA data")
	END AS device_continent,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_sub_continent, "New account")
		ELSE COALESCE(device_sub_continent, "No GA data")
	END AS device_sub_continent,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_country, "New account")
		ELSE COALESCE(device_country, "No GA data")
	END AS device_country,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_city, "New account")
		ELSE COALESCE(device_city, "No GA data")
	END AS device_city,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(device_metro, "New account")
		ELSE COALESCE(device_metro, "No GA data")
	END AS device_metro,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(app_version, "New account")
		ELSE COALESCE(app_version, "No GA data")
	END AS app_version,
	CASE
		WHEN DATE(created_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 3 DAY)
		THEN COALESCE(app_install_source, "New account")
		ELSE COALESCE(app_install_source, "No GA data")
	END AS app_install_source,
	COALESCE(attribution_medium, "Unknown") AS attribution_medium,
	COALESCE(attribution_source, "Unknown") AS attribution_source,
	COALESCE(attribution_term, "Unknown") AS attribution_term,
	COALESCE(attribution_campaign, "Unknown") AS attribution_campaign,
	COALESCE(attribution_content, "Unknown") AS attribution_content,
	rank_by_number_of_transactions,
	rank_by_volume,
	rank_by_inbound_volume,
	rank_by_outbound_volume,

FROM recently_active
	LEFT JOIN ranks USING (account_id)
	JOIN ${ref("stg_accounts")} AS accounts USING (account_id)
	JOIN (
		SELECT username, role, id AS account_id
		FROM ${ref({
			name: "accounts",
			schema: envs.currentSchema("_galoy_raw")
		})}
	) USING (account_id)
	LEFT JOIN (SELECT account_id, user_key FROM ${ref("stg_user_accounts")}) USING (account_id)
	LEFT JOIN (SELECT currency, wallet_id FROM ${ref("stg_wallets")}) ON default_wallet_id=wallet_id
	LEFT JOIN ${ref("stg_user_app_info")} USING (user_key)
	LEFT JOIN ${ref("stg_user_attribution")} USING (user_key)
