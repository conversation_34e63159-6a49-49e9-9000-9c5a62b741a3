config {
	type: "table",
	assertions: {
		nonNull: [
			"account_oid",
			"account_id",
		],
		uniqueKey: ["account_id"],
	},
	bigquery: {
		clusterBy: ["created_at"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	oid AS account_oid,
	role,
	contactEnabled AS contact_enabled,
	id AS account_id,
	kratosUserId AS kratos_user_id,
	displayCurrency AS display_currency,
	ARRAY(
		SELECT AS STRUCT updatedAt AS updated_at, status,
		FROM UNNEST(statusHistory)
	) AS status_history,
	lastConnection AS last_connection,
	username IS NOT NULL as username_set,
	created_at,
	level,
	defaultWalletId AS default_wallet_id,
	revision,
	notificationSettings.push.enabled AS notifications_enabled,
	notificationSettings.push.disabledCategories AS notifications_disabled_categories,

FROM ${ref({
	name: "accounts",
	schema: envs.currentSchema("_galoy_raw")
})}
