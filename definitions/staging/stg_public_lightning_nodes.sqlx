config {
	type: "incremental",
	uniqueKey: ["pubkey"],
	assertions: {
		nonNull: ["pubkey"],
		uniqueKey: ["pubkey"],
	},
	bigquery: {
		clusterBy: ["pubkey"],
		labels: {
			execution_env: envs.current
		}
	},
	description: "Parse node announcement messages as defined in: https://github.com/lightning/bolts/blob/master/07-routing-gossip.md#the-node_announcement-message",
}

pre_operations {
	DECLARE last_sync_timestamp DEFAULT (
		${when(incremental(),
			`SELECT MAX(sync_timestamp) FROM ${self()}`,
			`SELECT 0`
		)}
	);
}

WITH announcements AS (

	SELECT *,
		TO_CODE_POINTS(message) AS message_code_points,

	FROM ${ref({
		name: "gossip_messages",
		schema: envs.currentSchema("_functions_raw")
	})}

	WHERE timestamp > last_sync_timestamp
		AND type = 257

), parsed AS (

	SELECT timestamp AS sync_timestamp,
		SUBSTR(message, 2 + 64 + 2 + 1, 256 * message_code_points[OR<PERSON><PERSON><PERSON>(67)] + message_code_points[ORDINAL(68)]) AS features,
		RTRIM(CAST(SUBSTR(message, 2 + 64 + 2 + 256 * message_code_points[ORDINAL(67)] + message_code_points[ORDINAL(68)] + 4 + 33 + 3 + 1, 32) AS STRING), '\u0000') AS alias,
		CONCAT('#', TO_HEX(SUBSTR(message, 2 + 64 + 2 + 256 * message_code_points[ORDINAL(67)] + message_code_points[ORDINAL(68)] + 4 + 33 + 1, 3))) AS color,
		SUBSTR(message, 2 + 64 + 2 + 256 * message_code_points[ORDINAL(67)] + message_code_points[ORDINAL(68)] + 4 + 1, 33) AS pubkey,
		TIMESTAMP_SECONDS((
			SELECT SUM(CAST(POW(256, 3-o) * x AS INTEGER))
			FROM UNNEST(TO_CODE_POINTS(SUBSTR(message, 2 + 64 + 2 + 256 * message_code_points[ORDINAL(67)] + message_code_points[ORDINAL(68)] + 1, 4))) AS x WITH OFFSET o
		)) AS message_timestamp,

	FROM announcements

)

SELECT TO_HEX(pubkey) as pubkey,
	MAX(sync_timestamp) AS sync_timestamp,
	MAX(message_timestamp) AS updated_at,
	ANY_VALUE(features HAVING MAX message_timestamp) AS features,
	ANY_VALUE(alias HAVING MAX message_timestamp) AS raw_alias,
	ANY_VALUE(color HAVING MAX message_timestamp) AS color,
	ANY_VALUE(IF(alias="", TO_HEX(SUBSTR(pubkey,1,12)), alias) HAVING MAX message_timestamp) AS alias,

FROM parsed

GROUP BY pubkey
