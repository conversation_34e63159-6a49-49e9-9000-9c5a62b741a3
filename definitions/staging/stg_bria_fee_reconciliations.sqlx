config {
	type: "table",
	assertions: {
		uniqueKey: ["payout_id"],
		nonNull: ["payout_id", "sat_bria_fee_reconciliation"],
	},
	bigquery: {
		clusterBy: ["payout_id"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT payout_id,
	deposit AS sat_bria_fee_reconciliation,

FROM ${ref("stg_journal_entries")}

WHERE payout_id IS NOT NULL
	AND type = "onchain_payment"
	AND entries_in_journal = 2
	AND wallet_id = (
		SELECT btc_wallet_id
		FROM ${ref("stg_static_accounts")}
		WHERE account_name = "bankowner"
	)
	-- exception for duplicate reconcilations:
	AND journal_id NOT IN ("64981441f5302a5b49c5a955", "6553e35eaf471048a2aa1574")
