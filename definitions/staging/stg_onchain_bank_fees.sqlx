config {
	type: "table",
	assertions: {
		uniqueKey: ["journal_id"],
		nonNull: ["journal_id", "sat_onchain_fee_revenue"],
	},
	bigquery: {
		clusterBy: ["journal_id"],
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	journal_id,
	deposit as sat_onchain_fee_revenue

FROM ${ref("stg_bankowner_journal_entries")}

WHERE type IN ("onchain_payment", "onchain_receipt")
	AND deposit > 0
	AND NOT voided
