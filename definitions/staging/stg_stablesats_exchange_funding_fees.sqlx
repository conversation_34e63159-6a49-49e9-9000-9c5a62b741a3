config {
	type: "view",
	assertions: {
		uniqueKey: ["bill_id"],
	},
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

SELECT
	bill_id,
	recorded_at,
	sat_pnl AS sat_funding_pnl

FROM ${ref("stg_okex_bills_corrected")}

WHERE type = "fee"
	AND recorded_at > "2022-06-01"
	AND status = "ok"
	AND margin_mode = "cross"
	AND instrument_type = "SWAP"
	AND currency = "BTC"
	AND instrument_id = "BTC-USD-SWAP"
	AND sub_type in UNNEST(["Funding fee expense", "Funding fee income"])
