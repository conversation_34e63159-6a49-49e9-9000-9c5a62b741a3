config {
	type: "incremental",
	bigquery: {
		clusterBy: ["recorded_at", "user_id"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["user_id", "recorded_at"]
	},
}

pre_operations {
	DECLARE suffix_from DEFAULT (
		${when(incremental(),
			`SELECT MAX(table_suffix) FROM ${self()}`,
			`SELECT "20221218"`
		)}
	);
	DECLARE suffix_to DEFAULT (
		FORMAT_DATE("%Y%m%d", DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY))
	);
}

SELECT
	TIMESTAMP_MICROS(event_timestamp) AS recorded_at,
	_TABLE_SUFFIX AS table_suffix,
	user_id,
	(
		SELECT value.int_value
		FROM unnest(event_params)
		WHERE key = "ga_session_id"
	) AS session_id,
	(
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "ga_session_number"
	) AS session_number,
	(
		SELECT value.int_value
		FROM UNNEST(event_params)
		WHERE key = "engaged_session_event"
	) AS session_event_number,

FROM `${envs.project}.${envs.currentAnalyticsSchema()}.events_*`

WHERE (_TABLE_SUFFIX > suffix_from AND _TABLE_SUFFIX < suffix_to)
	AND event_name = "app_remove"
	AND EXISTS (
		SELECT 1
		FROM UNNEST(user_properties)
		WHERE key = "network" AND value.string_value = "${envs.currentNetwork()}"
	)
	AND user_id IS NOT NULL
	AND EXISTS(
		SELECT 1
		FROM UNNEST(event_params)
		WHERE key = "ga_session_id" and value.int_value IS NOT NULL
	)
