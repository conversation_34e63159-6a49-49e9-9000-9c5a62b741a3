config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

WITH input_arrays AS (
  SELECT
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 3) AS funding_rates_1d,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 21) AS funding_rates_1w,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 42) AS funding_rates_2w,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 63) AS funding_rates_3w,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 90) AS funding_rates_1m,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 180) AS funding_rates_2m,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 270) AS funding_rates_3m,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 540) AS funding_rates_6m,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 1095) AS funding_rates_1y,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 2191) AS funding_rates_2y,
    ARRAY_AGG(CAST(funding_rate AS FLOAT64) ORDER BY funding_time DESC LIMIT 3287) AS funding_rates_3y,
  FROM
    ${
      ref({
        name: "okex_funding_rates",
        schema: envs.currentSchema("_functions_raw")
      })
    }    
)

SELECT
  ${ref("udf_funding_yield")}(funding_rates_1d) AS funding_yield_1d,
  ${ref("udf_funding_yield")}(funding_rates_1w) AS funding_yield_1w,
  ${ref("udf_funding_yield")}(funding_rates_2w) AS funding_yield_2w,
  ${ref("udf_funding_yield")}(funding_rates_3w) AS funding_yield_3w,
  ${ref("udf_funding_yield")}(funding_rates_1m) AS funding_yield_1m,
  ${ref("udf_funding_yield")}(funding_rates_2m) AS funding_yield_2m,
  ${ref("udf_funding_yield")}(funding_rates_3m) AS funding_yield_3m,
  ${ref("udf_funding_yield")}(funding_rates_6m) AS funding_yield_6m,
  ${ref("udf_funding_yield")}(funding_rates_1y) AS funding_yield_1y,
  ${ref("udf_funding_yield")}(funding_rates_2y) AS funding_yield_2y,
  ${ref("udf_funding_yield")}(funding_rates_3y) AS funding_yield_3y,
FROM input_arrays
