config {
  type: "view",
  bigquery: {
    labels: {
      execution_env: envs.current
    }
  }
}

SELECT *,
	CASE
		WHEN long_deposit < 0 THEN
			abs(long_deposit) * sats_per_cent - abs(long_deposit) * long_avg_open_sats_per_cent
	END AS sat_long_pnl,
	CASE
		WHEN short_deposit < 0 THEN
			abs(short_deposit) * sats_per_cent - abs(short_deposit) * short_avg_open_sats_per_cent
	END AS sat_short_pnl
FROM ${ref("stg_stablesats_mishedge")}
  LEFT JOIN ${ref("stg_stablesats_mishedge_avg_open_prices")} USING (mishedge_id)
