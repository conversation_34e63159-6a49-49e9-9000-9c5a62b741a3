config {
	type: "view",
	bigquery: {
		labels: {
			execution_env: envs.current
		}
	}
}

WITH unified AS (

	SELECT
		recorded_at,
		journal_id,
		journal_entry_id,
		transaction_hash,
		wallet_id,
		deposit,
		currency,
		settlement_method,
		bank_direction,
		wallet_direction,
		sats_amount,
		cents_amount,
		accounts,
		type,
		payee_addresses[SAFE_ORDINAL(1)] AS payee_address,
	FROM ${ref("stg_journal_entries")}
	WHERE DATE(recorded_at) >= '2024-09-01'
    AND DATE(recorded_at) <= CURRENT_DATE

	UNION ALL

	SELECT
		recorded_at,
		journal_id,
		journal_entry_id,
		"" AS transaction_hash,
		wallet_id,
		deposit,
		currency,
		settlement_method,
		bank_direction,
		wallet_direction,
		sats_amount,
		cents_amount,
		accounts,
		type,
		payee_address,
	FROM ${ref("stg_rt_journal_entries")}
	WHERE TIMESTAMP_TRUNC(recorded_at, SECOND) > (
		SELECT MAX(recorded_at)
		FROM ${ref("stg_journal_entries")}
	)


)

SELECT
	recorded_at,
	journal_id,
	journal_entry_id,
	transaction_hash,
	wallet_id,
	deposit,
	currency,
	settlement_method,
	wallet_direction,
	sats_amount,
	cents_amount,
	accounts,
	type,
	account_id,
	CASE
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT account_id) OVER (PARTITION BY journal_id) = 1
			AND COUNT(DISTINCT currency) OVER (PARTITION BY journal_id) = 2
			AND type != "fee_reimbursement"
			THEN "Intra-user transfer"
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT account_id) OVER (PARTITION BY journal_id) = 2
			THEN "Inter-user transfer"
		WHEN bank_direction = "Inter-wallet transfer"
			THEN "Other Intra-ledger"
		ELSE bank_direction
	END AS bank_direction,
	CASE
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT account_id) OVER (PARTITION BY journal_id) = 1
			AND COUNT(DISTINCT currency) OVER (PARTITION BY journal_id) = 2
			AND type != "fee_reimbursement"
			THEN "Intra-user transfer"
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT account_id) OVER (PARTITION BY journal_id) = 2
			AND deposit > 0
			THEN "Inbound"
		WHEN bank_direction = "Inter-wallet transfer"
			AND COUNT(DISTINCT account_id) OVER (PARTITION BY journal_id) = 2
			AND deposit < 0
			THEN "Outbound"
		WHEN bank_direction = "Inter-wallet transfer"
			THEN "Other Intra-ledger"
		ELSE bank_direction
	END AS user_direction,
	SUM(deposit) OVER (
		PARTITION BY accounts
		ORDER BY recorded_at, journal_id, journal_entry_id
		ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
	) as relative_balance,
	payee_address,

FROM unified
	LEFT JOIN ${ref("stg_wallets")} USING (wallet_id, currency)
	LEFT JOIN ${ref("stg_accounts")} USING (account_id)
