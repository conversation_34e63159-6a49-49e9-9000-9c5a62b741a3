config {
	type: "incremental",
	bigquery: {
		clusterBy: ["inserted_at"],
		labels: {
			execution_env: envs.current
		}
	},
	assertions: {
		nonNull: ["inserted_at"]
	},
}

pre_operations {
	DECLARE last_inserted_at DEFAULT (
		${when(incremental(),
			`SELECT MAX(inserted_at) FROM ${self()}`,
			`SELECT TIMESTAMP("0001-01-01 00:00:00")`
		)}
	);
}

WITH parsed AS (

	SELECT inserted_at,
		ARRAY(
			SELECT AS STRUCT
				LAX_INT64(o[0]) AS order_id,
				LAX_FLOAT64(o[1]) AS price,
				ABS(LAX_FLOAT64(o[2])) AS amount,
				IF(LAX_FLOAT64(o[2]) > 0, "Bid", "Ask") AS direction,
			FROM UNNEST(JSON_EXTRACT_ARRAY(response)) AS o
		) AS book

	FROM ${ref({
		name: "bitfinex_book",
		schema: envs.currentSchema("_functions_raw"),
	})}

	WHERE inserted_at > last_inserted_at

)

SELECT inserted_at,
	ARRAY(
		SELECT AS STRUCT
			order_id,
			price,
			amount,
			SUM(amount) OVER (ORDER BY price DESC, order_id) AS cum_amount,
		FROM UNNEST(book) AS o
		WHERE direction = "Bid"
	) AS bids,
	ARRAY(
		SELECT AS STRUCT
			order_id,
			price,
			amount,
			SUM(amount) OVER (ORDER BY price ASC, order_id) AS cum_amount,
		FROM UNNEST(book) AS o
		WHERE direction = "Ask"
	) AS asks,

FROM parsed
