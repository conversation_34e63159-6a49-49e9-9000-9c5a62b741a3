name: Dataform Compile
on:
  pull_request:
    branches: [main]

jobs:
  compile:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code into workspace directory
        uses: actions/checkout@v2
      - name: Install project dependencies
        uses: docker://dataformco/dataform:latest
        with:
          args: install
      - name: Run dataform compile
        uses: docker://dataformco/dataform:latest
        with:
          args: compile
