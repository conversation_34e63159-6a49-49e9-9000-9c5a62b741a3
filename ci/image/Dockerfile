FROM node:18-bullseye

<PERSON><PERSON> apt-get update && apt-get install -y \
  make \
  curl \
  jq \
  apt-transport-https \
  ca-certificates \
  gnupg

RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] http://packages.cloud.google.com/apt cloud-sdk main" \
  | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list \
  && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg \
  | apt-key --keyring /usr/share/keyrings/cloud.google.gpg  add - \
  && apt-get update -y \
  && apt-get install google-cloud-cli -y

RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | gpg --dearmor -o /usr/share/keyrings/githubcli-archive-keyring.gpg \
  && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" \
  | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
  && apt update && apt install gh

ARG CEPLER_VERSION=0.7.11
RUN wget https://github.com/bodymindarts/cepler/releases/download/v${CEPLER_VERSION}/cepler-x86_64-unknown-linux-musl-${CEPLER_VERSION}.tar.gz \
  && tar -zxvf cepler-x86_64-unknown-linux-musl-${CEPLER_VERSION}.tar.gz \
  && mv cepler-x86_64-unknown-linux-musl-${CEPLER_VERSION}/cepler /usr/local/bin \
  && chmod +x /usr/local/bin/cepler \
  && rm -rf ./cepler-*

RUN npm i -g @dataform/cli

RUN wget -O ghtoken \
    https://raw.githubusercontent.com/Link-/gh-token/main/gh-token && \
    echo "6a6b111355432e08dd60ac0da148e489cdb0323a059ee8cbe624fd37bf2572ae  ghtoken" | \
    shasum -c - && \
    chmod u+x ./ghtoken && \
    mv ./ghtoken /usr/local/bin/ghtoken

RUN wget https://github.com/mike-engel/jwt-cli/releases/download/4.0.0/jwt-linux.tar.gz \
  && echo "6b0740c3f4c7134a0cbcf802b95b033bd2246d592ad16aa2ee2d80e5b289b4d6  jwt-linux.tar.gz" > jwt-linux.sha256 \
  && shasum --check --status ./jwt-linux.sha256 \
  && tar -xzf jwt-linux.tar.gz \
  && mv jwt /usr/local/bin/jwt
