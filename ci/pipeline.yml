#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "import_resource_name",
#@   "tf_import_resource_name",
#@   "galoy_import_task_image_config",
#@   "task_image_config",
#@   "cepler_config",
#@   "cepler_in",
#@   "cepler_out",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "pipeline_image",
#@   "env_value",
#@   "gates_file",
#@   "tf_import_components"
#@  )

#@ load("import.lib.yml",
#@  "import_regular",
#@  "import_galoy",
#@ )
#@ load("functions.lib.yml",
#@  "functions_job",
#@  "tf_functions_resource_name"
#@ )
#@ load("kafka.lib.yml",
#@  "kafka_job",
#@  "tf_kafka_resource_name"
#@ )
#@ load("dataform.lib.yml",
#@  "trigger_dataform_job",
#@  "daily_dataform_job"
#@ )
#@ load("bump.lib.yml",
#@   "open_pr_job",
#@   "ungated_resource_name",
#@   "ungated_cepler_in"
#@ )

#@ environments = ["galoy-staging", "galoy-bbw"]

groups:
- name: all
  jobs:
#@ for env in environments:
  - #@ "trigger-" + env + "-dataform"
  - #@ "daily-" + env + "-dataform"
  - #@ "import-" + env + "-galoy"
  - #@ env + "-functions"
  - #@ env + "-kafka"
#@ for component in tf_import_components:
  - #@ "import-" + env + "-" + component
#@ end
#@ end
  - bump-galoy-bbw-dataform
  - bump-galoy-bbw-functions
  - bump-galoy-bbw-kafka
  - import-volcano-staging-lava_bank

- name: all-dataform
  jobs:
  - bump-galoy-bbw-dataform
#@ for env in environments:
  - #@ "trigger-" + env + "-dataform"
  - #@ "daily-" + env + "-dataform"
#@ end
- name: all-imports
  jobs:
#@ for env in environments:
  - #@ "import-" + env + "-galoy"
#@ for component in tf_import_components:
  - #@ "import-" + env + "-" + component
#@ end
#@ end

- name: all-functions
  jobs:
#@ for env in environments:
  - #@ env + "-functions"
#@ end
  - bump-galoy-bbw-functions

- name: all-kafka
  jobs:
#@ for env in environments:
  - #@ env + "-kafka"
#@ end
  - bump-galoy-bbw-kafka

- name: image
  jobs: [build-pipeline-image]

jobs:
#@ for env in environments:
#@ for component in tf_import_components:
- #@ import_regular(env, component)
#@ end
- #@ functions_job(env)
- #@ kafka_job(env)
- #@ import_galoy(env)
- #@ trigger_dataform_job(env)
- #@ daily_dataform_job(env)
#@ end
- #@ open_pr_job("functions", "galoy-bbw")
- #@ open_pr_job("kafka", "galoy-bbw")
- #@ open_pr_job("dataform", "galoy-bbw")
- #@ import_regular("volcano-staging", "lava_bank", "hourly")

- name: build-pipeline-image
  serial: true
  plan:
  - {get: pipeline-image-def, trigger: true}
  - task: build
    privileged: true
    config:
      platform: linux
      image_resource:
        type: registry-image
        source:
          repository: vito/oci-build-task
      inputs:
      - name: pipeline-image-def
      outputs:
      - name: image
      params:
        CONTEXT: pipeline-image-def/ci/image
      run:
        path: build
  - put: pipeline-image
    params:
      image: image/image.tar

#@ def import_resource_git(env, component):
name: #@ import_resource_name(env, component)
type: git
source:
  files:
  - #@ "imports/" + env + "/*"
  - imports/raw_dataset/*
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def import_resource_tf(env, component):
name: #@ tf_import_resource_name(env, component)
type: terraform
source:
  env_name: #@ env
  backend_type: gcs
  backend_config:
    bucket: #@ data.values.tf_state_bucket_name
    prefix: #@ "imports/" + component
    credentials: #@ data.values.gcp_creds
  env:
    GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
#@ end

resources:
- #@ import_resource_git("volcano-staging", "lava_bank")
- #@ import_resource_tf("volcano-staging", "lava_bank")
#@ for env in environments:
#@ for component in tf_import_components:
- #@ import_resource_git(env, component)
- #@ import_resource_tf(env, component)
#@ end
- name: #@ import_resource_name(env, "galoy")
  type: git
  source:
    files:
    - #@ "imports/" + env + "/*"
    - #@ "imports/raw_dataset/*"
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key
- name: #@ tf_import_resource_name(env, "galoy-prep")
  type: terraform
  source:
    env_name: #@ env
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.tf_state_bucket_name
      prefix: imports/galoy/prep
      credentials: #@ data.values.gcp_creds
    env:
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
- name: #@ tf_import_resource_name(env, "galoy")
  type: terraform
  source:
    env_name: #@ env
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.tf_state_bucket_name
      prefix: imports/galoy
      credentials: #@ data.values.gcp_creds
    env:
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
- name: #@ tf_functions_resource_name(env)
  type: terraform
  source:
    env_name: #@ env
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.tf_state_bucket_name
      prefix: functions
      credentials: #@ data.values.gcp_creds
    env:
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
- name: #@ tf_kafka_resource_name(env)
  type: terraform
  source:
    env_name: #@ env
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.tf_state_bucket_name
      prefix: kafka
      credentials: #@ data.values.gcp_creds
    env:
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds

- #@ cepler_in("dataform", env)
- #@ cepler_in("functions", env)
- #@ cepler_in("kafka", env)
#@ end

- #@ ungated_cepler_in("dataform", "galoy-bbw")
- #@ ungated_cepler_in("functions", "galoy-bbw")
- #@ ungated_cepler_in("kafka", "galoy-bbw")
- #@ cepler_out("dataform")
- #@ cepler_out("functions")
- #@ cepler_out("kafka")

- name: hourly-trigger
  type: time
  source:
    interval: 1h

- name: daily-trigger
  type: time
  source:
    start: 5:00 AM
    stop: 6:00 AM
    initial_version: true

- name: pipeline-tasks
  type: git
  source:
    paths: [ci/vendor/*, ci/tasks/*, ci/config/*, Makefile]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: pipeline-image-def
  type: git
  source:
    paths: [ci/image/Dockerfile]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: pipeline-image
  type: registry-image
  source:
    tag: latest
    username: #@ data.values.docker_registry_user
    password: #@ data.values.docker_registry_password
    repository: #@ pipeline_image()

- name: gates-branch
  type: git
  source:
    uri: #@ data.values.git_uri
    branch: #@ data.values.gates_branch
    private_key: #@ data.values.github_private_key

- name: bot-push
  type: git
  source:
    uri: #@ data.values.git_uri
    private_key: #@ data.values.github_private_key

resource_types:
- name: terraform
  type: docker-image
  source:
    repository: ljfranklin/terraform-resource
    tag: latest

- name: cepler-in
  type: registry-image
  source:
    repository: cepler/cepler-concourse-resource
    tag: latest

- name: cepler-out
  type: registry-image
  source:
    repository: cepler/cepler-concourse-resource
    tag: latest
