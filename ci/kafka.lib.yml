#@ load("shared.lib.yml",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "env_value",
#@  )

#@ def kafka_resource_name(env):
#@   return env + "-kafka"
#@ end

#@ def tf_kafka_resource_name(env):
#@   return env + "-kafka-tf"
#@ end

#@ def kafka_job(env):
#@ job_name = env + "-kafka"
name: #@ job_name
serial: true
plan:
- in_parallel:
  - get: repo
    resource: #@ kafka_resource_name(env)
    trigger: true
- put: #@ tf_kafka_resource_name(env)
  params:
    terraform_source: repo/kafka
    vars:
      environment: #@ env
- put: #@ cepler_out_name("kafka")
  params:
    repository: repo
    environment: #@ env
#@ end
