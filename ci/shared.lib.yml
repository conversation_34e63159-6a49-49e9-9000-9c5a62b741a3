#@ load("@ytt:data", "data")

#@ tf_import_components = ["stablesats", "bria", "kratos", "blink_kyc", "apikeys"]

#@ def import_resource_name(env, comp):
#@   return env + "-" + comp + "-import"
#@ end

#@ def tf_import_resource_name(env, comp):
#@   return env + "-" + comp + "-import-tf"
#@ end

#@ def env_value(env, value):
#@   return getattr(data.values, env.replace("-", "_") + "_" + value)
#@ end

#@ def galoy_import_task_image_config():
type: registry-image
source:
  username: ((docker-creds.username))
  password: ((docker-creds.password))
  repository: gcr.io/galoy-org/galoy-data-sync
  tag: edge
#@ end

#@ def pipeline_image():
#@   return data.values.docker_registry + "/galoy-reporting-pipeline"
#@ end

#@ def task_image_config():
type: registry-image
source:
  username: #@ data.values.docker_registry_user
  password: #@ data.values.docker_registry_password
  repository: #@ pipeline_image()
#@ end

#@ def cepler_config(deployment):
#@   return "cepler/" + deployment + ".yml"
#@ end

#@ def cepler_resource_name(deployment, env):
#@   return env + "-" + deployment
#@ end

#@ def cepler_out_name(deployment):
#@   return deployment + "-out"
#@ end

#@ def gates_file(deployment):
#@   return "cepler-gates/" + deployment + ".yml"
#@ end

#@ def cepler_in(deployment, env):
name: #@ cepler_resource_name(deployment, env)
type: cepler-in
source:
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
  environment: #@ env
  config: #@ cepler_config(deployment)
  gates_branch: #@ data.values.gates_branch
  gates_file: #@ gates_file(deployment)
#@ end

#@ def cepler_out(deployment):
name: #@ cepler_out_name(deployment)
type: cepler-out
source:
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
  config: #@ cepler_config(deployment)
  gates_branch: #@ data.values.gates_branch
  gates_file: #@ gates_file(deployment)
#@ end
