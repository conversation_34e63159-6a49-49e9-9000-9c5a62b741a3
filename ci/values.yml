#@data/values
---
git_uri: **************:blinkbitcoin/blink-reporting.git
git_branch: main
github_private_key: ((github-blinkbitcoin.private_key))
github_token: ((github-blinkbitcoin.api_token))
github_app_id: ((github-blinkbitcoin.github_app_id))
github_app_private_key: ((github-blinkbitcoin.github_app_private_key))
gates_branch: cepler-gates

docker_registry: gcr.io/galoy-org
docker_registry_user: ((docker-creds.username))
docker_registry_password: ((docker-creds.password))

tf_state_bucket_name: galoy-reporting-tf-state
gcp_creds: ((gcp-creds.creds_json))

galoy_staging_bria_connection: "galoy-staging.us.da7af1af-eff0-4f2d-abfb-0db979e81417"
galoy_staging_stablesats_connection: "galoy-staging.us.1e0d9e1b-2c27-48e0-b1f9-ed33ee1e330f"
galoy_staging_kratos_connection: "galoy-staging.us.8a3d9089-9d8b-45af-9032-b07bfc642760"
galoy_staging_blink_kyc_connection: "galoy-staging.us.fa80c015-1df6-4e48-ab17-ad3bcb8ddaa5"
galoy_staging_apikeys_connection: "galoy-staging.us.61ada610-549a-4230-9d6e-782aed5a6cc4"
galoy_staging_mongodb_pw: ((galoy-mongodb.staging_password))
galoy_staging_okex_api_key: ((okex-creds.staging_api_key))
galoy_staging_okex_passphrase: ((okex-creds.staging_passphrase))
galoy_staging_okex_secret_key: ((okex-creds.staging_secret_key))
galoy_staging_galoy_session_token: ((statuspage-creds.staging_galoy_session_token))
galoy_staging_statuspage_api_key: ((statuspage-creds.staging_statuspage_api_key))
galoy_staging_onfido_api_key: ((onfido-api-key.staging_onfido_api_key))
galoy_staging_twilio_account: ((twilio-creds.staging_twilio_account))
galoy_staging_twilio_account_sid: ((twilio-creds.staging_twilio_account_sid))
galoy_staging_twilio_auth_token: ((twilio-creds.staging_twilio_auth_token))

galoy_bbw_bria_connection: "galoy-bitcoinbeach.us.190a0b40-8d78-47d8-ace9-21d76a028ac2"
galoy_bbw_stablesats_connection: "galoy-bitcoinbeach.us.f29cbf7f-64ef-4c9a-9355-853e8b14c536"
galoy_bbw_kratos_connection: "galoy-bitcoinbeach.us.90abc6a7-e793-4973-9dbd-587c14c0dba0"
galoy_bbw_blink_kyc_connection: "galoy-bitcoinbeach.us.9079a16d-67a5-48c2-a14d-67acf8a3736f"
galoy_bbw_apikeys_connection: "galoy-bitcoinbeach.us.edcb37c1-1c6e-44dd-a8c5-836695e3cfd2"
galoy_bbw_mongodb_pw: ((galoy-mongodb.bbw_password))
galoy_bbw_okex_api_key: ((okex-creds.bbw_api_key))
galoy_bbw_okex_passphrase: ((okex-creds.bbw_passphrase))
galoy_bbw_okex_secret_key: ((okex-creds.bbw_secret_key))
galoy_bbw_galoy_session_token: ((statuspage-creds.bbw_galoy_session_token))
galoy_bbw_statuspage_api_key: ((statuspage-creds.bbw_statuspage_api_key))
galoy_bbw_onfido_api_key: ((onfido-api-key.bbw_onfido_api_key))
galoy_bbw_twilio_account: ((twilio-creds.bbw_twilio_account))
galoy_bbw_twilio_account_sid: ((twilio-creds.bbw_twilio_account_sid))
galoy_bbw_twilio_auth_token: ((twilio-creds.bbw_twilio_auth_token))

volcano_staging_lava_bank_connection: "volcano-staging.eu.aa7238fd-41fc-495e-9e91-649b1212040d"
