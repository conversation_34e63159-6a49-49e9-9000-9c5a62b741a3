#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "import_resource_name",
#@   "tf_import_resource_name",
#@   "galoy_import_task_image_config",
#@   "task_image_config",
#@   "cepler_config",
#@   "cepler_in",
#@   "cepler_out",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "pipeline_image",
#@   "env_value",
#@   "gates_file",
#@   "tf_import_components"
#@  )

#@ def import_regular(env, component, trigger_interval="daily"):
#@ job_name = "import-" + env + "-" + component
name: #@ job_name
serial: true
plan:
- in_parallel:
  - get: repo
    resource: #@ import_resource_name(env, component)
  - #@ { "get": trigger_interval + "-trigger", "trigger": True }
- put: #@ tf_import_resource_name(env, component)
  params:
    terraform_source: #@ "repo/imports/" + component
    #@ vars = {
    #@   "environment": env,
    #@   component + "_connection": env_value(env, component + "_connection")
    #@ }
    vars: #@ vars
#@ end

#@ def import_galoy(env):
name: #@ "import-" + env + "-galoy"
serial: true
plan:
- in_parallel:
  - get: pipeline-tasks
  - get: repo
    resource: #@ import_resource_name(env, "galoy")
  - { get: daily-trigger, trigger: true }
- put: #@ tf_import_resource_name(env, "galoy-prep")
  params:
    terraform_source: repo/imports/galoy/prep
    vars:
      environment: #@ env
- task: #@ "import-" + env + "-galoy"
  tags:
  - #@ env
  config:
    platform: linux
    image_resource: #@ galoy_import_task_image_config()
    inputs:
      - name: pipeline-tasks
      - name: repo
      - name: #@ tf_import_resource_name(env, "galoy-prep")
        path: tf
    params:
      ENVIRONMENT: #@ env
      MONGODB_PASSWORD: #@ env_value(env, "mongodb_pw")
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
    run:
      path: pipeline-tasks/ci/tasks/execute-galoy-import.sh
- put: #@ tf_import_resource_name(env, "galoy")
  params:
    terraform_source: repo/imports/galoy
    var_files:
    - #@ tf_import_resource_name(env, "galoy-prep") + "/metadata"
    vars:
      environment: #@ env
      kratos_connection: #@ env_value(env, "kratos_connection")
#@ end
