#@ load("shared.lib.yml",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "env_value",
#@  )

#@ def functions_resource_name(env):
#@   return env + "-functions"
#@ end

#@ def tf_functions_resource_name(env):
#@   return env + "-functions-tf"
#@ end

#@ def functions_job(env):
#@ job_name = env + "-functions"
name: #@ job_name
serial: true
plan:
- in_parallel:
  - get: repo
    resource: #@ functions_resource_name(env)
    trigger: true
- put: #@ tf_functions_resource_name(env)
  params:
    terraform_source: repo/functions
    vars:
      environment: #@ env
      okex_api_key: #@ env_value(env, "okex_api_key")
      okex_passphrase: #@ env_value(env, "okex_passphrase")
      okex_secret_key: #@ env_value(env, "okex_secret_key")
      statuspage_api_key: #@ env_value(env, "statuspage_api_key")
      galoy_session_token: #@ env_value(env, "galoy_session_token")
      onfido_api_key: #@ env_value(env, "onfido_api_key")
      twilio_account: #@ env_value(env, "twilio_account")
      twilio_account_sid: #@ env_value(env, "twilio_account_sid")
      twilio_auth_token: #@ env_value(env, "twilio_auth_token")
- put: #@ cepler_out_name("functions")
  params:
    repository: repo
    environment: #@ env
#@ end
