#!/bin/bash

set -e

dataset_id=$(cat tf/metadata | jq -r '.dataset_id')
timestamp=$(cat tf/metadata | jq -r '.timestamp')

cat <<EOF > gcp-creds.json
${GOOGLE_CREDENTIALS}
EOF
export SERVICE_ACCOUNT_CREDS="$(pwd)/gcp-creds.json"

cat <<EOF > sync-config.yml
mongodb:
  skip_collections:
  - phonecodes
  - accountapikeys
  - system.views
  - system.profile
  - view_users
  - view_medici_transactions
  - view_wallets
  - view_lnpayments
  - view_medici_transaction_metadatas
  hosts:
  - galoy-mongodb-0.galoy-mongodb-headless.${ENVIRONMENT}-galoy.svc.cluster.local
  - galoy-mongodb-1.galoy-mongodb-headless.${ENVIRONMENT}-galoy.svc.cluster.local
  - galoy-mongodb-2.galoy-mongodb-headless.${ENVIRONMENT}-galoy.svc.cluster.local
  port: 27017
  database: galoy
  username: testGaloy

big_query:
  timestamp: "${timestamp}"
  environment: ${ENVIRONMENT}
  deployment: galoy
  dataset_id: "${dataset_id}"
  project_id: "galoy-reporting"
EOF

cat sync-config.yml
galoy-data-sync sync
