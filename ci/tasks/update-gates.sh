#!/bin/bash

set -eu

ref=$(cat trigger/.git/cepler_trigger)
env=$(cat trigger/.git/cepler_environment)

pushd gates-branch

bump_message="chore: Bump '${DEPLOYMENT}' in '${env}' to '${ref:0:7}'"
set +e
if grep ${env} ${CEPLER_GATES}; then
  sed -i''  "s/${env}:.*/${env}: ${ref}/" ${CEPLER_GATES}
else
  echo "${env}: ${ref}" >> ${CEPLER_GATES}
  bump_message="Add '${env}' to '${DEPLOYMENT}'"
fi
set -e

if [[ -z $(git config --global user.email) ]]; then
  git config --global user.email "<EMAIL>"
fi
if [[ -z $(git config --global user.name) ]]; then
  git config --global user.name "CI Bot"
fi

(
  cd $(git rev-parse --show-toplevel)
  git merge --no-edit ${GATES_BRANCH}
  git add -A
  git status
  git commit -m "${bump_message}"
)

popd

pushd repo

set +e
cepler reproduce -e ${env} --force-clean 2> ../err-out
if grep "No state recorded for" ../err-out > /dev/null ; then
  echo "NO DIFF - first deploy" > deployments-diff-url
else
  set -e
  (
    git add -A
    git status
    git commit -m "Commit current state of '${DEPLOYMENT}' in '${env}'"
  )
  
  deployments_current_head=$(git rev-parse --short HEAD)

  unset GATES_BRANCH
  cepler --ignore-queue -g ../gates-branch/${CEPLER_GATES} prepare -e ${env} --force-clean
  (
    git add -A
    git status
    git commit -m "Commit prepared state of '${DEPLOYMENT}' in '${env}'"
  )

  deployments_prepared_head=$(git rev-parse --short HEAD)
  echo "https://github.com/blinkbitcoin/blink-reporting/compare/${deployments_current_head}...${deployments_prepared_head}" > deployments-diff-url
fi

