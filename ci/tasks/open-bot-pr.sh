#!/bin/bash

set -eu

pushd trigger

IGNORE="--ignore-queue"
trigger=$(cat .git/cepler_trigger)
env=$(cat .git/cepler_environment)

cepler --ignore-queue -g ../gates-branch/cepler-gates/${DEPLOYMENT}.yml \
  check -e ${env} > ../cepler-check-out 2>&1

cat <<EOF >> ../body.md
# Propagate '${DEPLOYMENT}' changes to '${env}'

A diff of all changes that will be rolled out: $(cat ../repo/deployments-diff-url)

## Verify locally
To check locally run:
$(echo '```')
$ git fetch origin
$ cepler ${IGNORE} -g ${CEPLER_GATES} --gates-branch ${BOT_BRANCH} check -e ${env}
$(cat ../cepler-check-out)
$ cepler ${IGNORE} -g ${CEPLER_GATES} --gates-branch ${BOT_BRANCH} prepare -e ${env}
$(echo '```')
EOF

export GH_TOKEN="$(ghtoken generate -b "${GH_APP_PRIVATE_KEY}" -i "${GH_APP_ID}" | jq -r '.token')"

gh pr close ${BOT_BRANCH} || true
gh pr create \
  --title bump-${env}-${DEPLOYMENT}-${trigger:0:7} \
  --body-file ../body.md \
  --base ${BRANCH} \
  --head ${BOT_BRANCH} \
  --label galoybot \
  --label ${env}
