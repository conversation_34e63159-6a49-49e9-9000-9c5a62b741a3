#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "task_image_config",
#@   "cepler_config",
#@   "cepler_in",
#@   "cepler_out",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "pipeline_image",
#@   "tf_import_components",
#@   "env_value",
#@   "gates_file",
#@  )

#@ def trigger_dataform_job(env):
name: #@ "trigger-" + env + "-dataform"
serial: true
plan:
- get: #@ cepler_resource_name("dataform", env)
  trigger: true
- task: dataform
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: #@ cepler_resource_name("dataform", env)
      path: repo
    params:
      ENVIRONMENT: #@ env
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
    run:
      path: repo/ci/tasks/execute-dataform.sh
- put: #@ cepler_out_name("dataform")
  params:
    repository: #@ cepler_resource_name("dataform", env)
    environment: #@ env
#@ end

#@ def daily_dataform_job(env):
name: #@ "daily-" + env + "-dataform"
serial: true
plan:
- in_parallel:
  - get: daily-trigger
    trigger: true
    passed:
    #@ for component in tf_import_components:
    - #@ "import-" + env + "-" + component
    #@ end
    - #@ "import-" + env + "-galoy"
  - get: #@ cepler_resource_name("dataform", env)
    passed:
    - #@ "trigger-" + env + "-dataform"
    params:
      prepare: false
- task: dataform
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: #@ cepler_resource_name("dataform", env)
      path: repo
    params:
      ENVIRONMENT: #@ env
      GOOGLE_CREDENTIALS: #@ data.values.gcp_creds
    run:
      path: repo/ci/tasks/execute-dataform.sh
#@ end
