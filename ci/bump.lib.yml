#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "cepler_config",
#@   "gates_file")

#@ def ungated_resource_name(deployment, env):
#@   return env + "-" + deployment + "-ungated"
#@ end

#@ def open_pr_job(deployment, env):
name: #@ "bump-" + env + "-" + deployment
plan:
- in_parallel:
  - get: #@ ungated_resource_name(deployment, env)
    trigger: true
  - { get: gates-branch }
  - { get: pipeline-tasks }
- task: update-gates
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: #@ ungated_resource_name(deployment, env)
      path: trigger
    - name: #@ ungated_resource_name(deployment, env)
      path: repo
    - name: gates-branch
    outputs:
    - name: gates-branch
    - name: repo
    params:
      GATES_BRANCH: #@ data.values.gates_branch
      CEPLER_GATES: #@ gates_file(deployment)
      CEPLER_CONF: #@ cepler_config(deployment)
      DEPLOYMENT: #@ deployment
    run:
      path: pipeline-tasks/ci/tasks/update-gates.sh
- in_parallel:
  - put: bot-push
    params:
      repository: gates-branch
      branch: #@ "bump-" + env + "-" + deployment + "-gate"
      force: true
  - put: bot-push
    params:
      repository: repo
      branch: #@ env + "-" + deployment + "-diff"
      force: true
- task: open-pr
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: gates-branch
    - name: #@ ungated_resource_name(deployment, env)
      path: trigger
    params:
      GH_APP_ID: #@ data.values.github_app_id
      GH_APP_PRIVATE_KEY: #@ data.values.github_app_private_key
      BRANCH: #@ data.values.gates_branch
      BOT_BRANCH: #@ "bump-" + env + "-" + deployment + "-gate"
      DEPLOYMENT: #@ deployment
      CEPLER_GATES: #@ gates_file(deployment)
      CEPLER_CONF: #@ cepler_config(deployment)
    run:
      path: pipeline-tasks/ci/tasks/open-bot-pr.sh
#@ end

#@ def ungated_cepler_in(deployment, env):
name: #@ ungated_resource_name(deployment, env)
type: cepler-in
source:
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
  environment: #@ env
  config: #@ cepler_config(deployment)
  ignore_queue: true
#@ end
