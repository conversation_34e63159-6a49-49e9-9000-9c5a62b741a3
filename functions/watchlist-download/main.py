import requests
import urllib3
import ssl
from os import environ
import pandas as pd
import numpy as np
from google.cloud import bigquery
from functions_framework import cloud_event
from io import StringIO

from pympler import asizeof

class CustomHttpAdapter (requests.adapters.HTTPAdapter):
    # "Transport adapter" that allows to use custom ssl_context.

    def __init__(self, ssl_context=None, **kwargs):
        self.ssl_context = ssl_context
        super().__init__(**kwargs)

    def init_poolmanager(self, connections, maxsize, block=False):
        self.poolmanager = urllib3.poolmanager.PoolManager(
            num_pools=connections, maxsize=maxsize,
            block=block, ssl_context=self.ssl_context)

def get_legacy_session():
    ctx = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
    ctx.options |= 0x4  # OP_LEGACY_SERVER_CONNECT
    session = requests.session()
    session.mount('https://', CustomHttpAdapter(ctx))
    return session

class Ofac:
	def __init__(self):
		self.s = requests.Session()

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_dataframe(self, timestamp):
		########### OFAC ###########
		# Overview: https://ofac.treasury.gov/specially-designated-nationals-list-data-formats-data-schemas
		# XML File: https://www.treasury.gov/ofac/downloads/sdn.xml
		############################
		ofac_xml_url ='https://www.treasury.gov/ofac/downloads/sdn.xml'

		ofac_xml_df = pd.DataFrame()
		try:
			ofac_xml_df = pd.read_xml(ofac_xml_url)
			print(f"Successfully read {len(ofac_xml_df)} rows from OFAC list (size={asizeof.asizeof(ofac_xml_df)})")
		except Exception as e:
			print(f"Error while fetching {ofac_xml_url} file: {e}")
			return ofac_xml_df

		ofac_df = ofac_xml_df[ofac_xml_df['sdnType'] == 'Individual']

		ofac_df = ofac_df.loc[:, [
			'uid',
			'firstName',
			'lastName',
		]]

		ofac_df.rename(columns={
			'uid': 'watchlist_id',
			'firstName': 'first_name',
			'lastName': 'second_name',
		}, inplace=True)

		ofac_df.dropna(subset=['watchlist_id'], inplace=True)

		ofac_df['watchlist_id'] = ofac_df['watchlist_id'].map(np.int64)

		for col in ['first_name', 'second_name']:
			ofac_df[col] = ofac_df[col].apply(lambda x: str(x).upper() if x else '')

		ofac_df['third_name'] = ''
		ofac_df['fourth_name'] = ''
		ofac_df['watchlist'] = 'OFAC'
		ofac_df['created_at'] = timestamp

		return ofac_df

class Un:
	def __init__(self):
		self.s = requests.Session()

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_dataframe(self, timestamp):
		########### UN ###########
		# Overview: https://www.un.org/securitycouncil/content/un-sc-consolidated-list
		# XML File: https://scsanctions.un.org/resources/xml/en/consolidated.xml
		############################
		un_xml_url ='https://scsanctions.un.org/resources/xml/en/consolidated.xml'

		un_xml_df = pd.DataFrame()
		try:
			# Can't use pandas to access the file directly,
			# it fails with this error: "<urlopen error [SSL: UNSAFE_LEGACY_RENEGOTIATION_DISABLED] unsafe legacy renegotiation disabled (_ssl.c:1007)>"
			# un_xml_df = pd.read_xml(un_xml_url, xpath='/CONSOLIDATED_LIST/INDIVIDUALS/*')
			# One solution is to create a session
			response = get_legacy_session().get(un_xml_url)
			if response.ok:
				data = response.content.decode('utf8')
				un_xml_df = pd.read_xml(StringIO(data), xpath='/CONSOLIDATED_LIST/INDIVIDUALS/*')
				print(f"Successfully read {len(un_xml_df)} rows from UN list (size={asizeof.asizeof(un_xml_df)})")
			else:
				print(f"Error while fetching {un_xml_url} file: {response}")
				return un_xml_df
		except Exception as e:
			print(f"Error while fetching {un_xml_url} file: {e}")
			return un_xml_df

		un_df = un_xml_df.loc[:, [
			'DATAID',
			'FIRST_NAME',
			'SECOND_NAME',
			'THIRD_NAME',
			'FOURTH_NAME',
		]]

		un_df.rename(columns={
			'DATAID': 'watchlist_id',
			'FIRST_NAME': 'first_name',
			'SECOND_NAME': 'second_name',
			'THIRD_NAME': 'third_name',
			'FOURTH_NAME': 'fourth_name',
		}, inplace=True)

		un_df['watchlist_id'] = un_df['watchlist_id'].map(np.int64)

		for col in ['first_name', 'second_name', 'third_name', 'fourth_name']:
			un_df[col] = un_df[col].apply(lambda x: str(x).upper() if x else '')

		un_df['watchlist'] = 'UN'
		un_df['created_at'] = timestamp

		return un_df

class Pep:
	def __init__(self):
		self.s = requests.Session()

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_dataframe(self, timestamp):
		########### PEP ###########
		# Overview: public google sheet
		# Sheet Name: galoy google spreadsheet on PEP tab
		############################
		sheet_id = '1bpfiRvlD9F68JrGmPCNa3N_hl1oF3_my'
		sheet_name = 'PEP'
		pep_sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/gviz/tq?tqx=out:csv&sheet={sheet_name}"

		pep_sheet_df = pd.DataFrame()
		try:
			pep_sheet_df = pd.read_csv(pep_sheet_url)
			print(f"Successfully read {len(pep_sheet_df)} rows from {sheet_name} list (size={asizeof.asizeof(pep_sheet_df)})")
		except Exception as e:
			print(f"Error while fetching {pep_sheet_url} file: {e}")
			return pep_sheet_df

		pep_df = pep_sheet_df.loc[:, [
			'#',
			'Name',
		]]

		pep_df.rename(columns={
			'#': 'watchlist_id',
			'Name': 'first_name',
		}, inplace=True)

		pep_df['watchlist_id'] = pep_df['watchlist_id'].map(np.int64)

		for col in ['first_name']:
			pep_df[col] = pep_df[col].apply(lambda x: str(x).upper() if x else '')

		pep_df['second_name'] = ''
		pep_df['third_name'] = ''
		pep_df['fourth_name'] = ''
		pep_df['watchlist'] = 'PEP'
		pep_df['created_at'] = timestamp

		return pep_df

class Noticias:
	def __init__(self):
		self.s = requests.Session()

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_dataframe(self, timestamp):
		########### Noticias ###########
		# Overview: public google sheet
		# Sheet Name: galoy google spreadsheet on Noticias tab
		############################
		sheet_id = '1bpfiRvlD9F68JrGmPCNa3N_hl1oF3_my'
		sheet_name = 'Noticias'
		noticias_sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/gviz/tq?tqx=out:csv&sheet={sheet_name}"

		noticias_sheet_df = pd.DataFrame()
		try:
			noticias_sheet_df = pd.read_csv(noticias_sheet_url)
			print(f"Successfully read {len(noticias_sheet_df)} rows from {sheet_name} list (size={asizeof.asizeof(noticias_sheet_df)})")
		except Exception as e:
			print(f"Error while fetching {noticias_sheet_url} file: {e}")
			return noticias_sheet_df

		noticias_df = noticias_sheet_df.loc[:, [
			'Persona involucrada en noticia',
		]]

		noticias_df.rename(columns={
			'Persona involucrada en noticia': 'first_name',
		}, inplace=True)

		noticias_df['watchlist_id'] = noticias_df['first_name'].index.map(str)
		noticias_df['watchlist_id'] = noticias_df['watchlist_id'].map(np.int64)

		for col in ['first_name']:
			noticias_df[col] = noticias_df[col].apply(lambda x: str(x) if x else '')

		noticias_df['second_name'] = ''
		noticias_df['third_name'] = ''
		noticias_df['fourth_name'] = ''
		noticias_df['watchlist'] = 'NOTICIAS'
		noticias_df['created_at'] = timestamp

		return noticias_df

class Lecat:
	def __init__(self):
		self.s = requests.Session()

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_dataframe(self, timestamp):
		########### LECAT ###########
		# Overview: public google sheet
		# Sheet Name: galoy google spreadsheet on LECAT tab
		############################
		sheet_id = '1bpfiRvlD9F68JrGmPCNa3N_hl1oF3_my'
		sheet_name = 'LECAT'
		lecat_sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/gviz/tq?tqx=out:csv&sheet={sheet_name}"

		lecat_sheet_df = pd.DataFrame()
		try:
			lecat_sheet_df = pd.read_csv(lecat_sheet_url)
			print(f"Successfully read {len(lecat_sheet_df)} rows from {sheet_name} list (size={asizeof.asizeof(lecat_sheet_df)})")
		except Exception as e:
			print(f"Error while fetching {lecat_sheet_url} file: {e}")
			return lecat_sheet_df

		lecat_df = lecat_sheet_df.loc[:, [
			'Nombre Imputado',
		]]

		lecat_df.rename(columns={
			'Nombre Imputado': 'first_name',
		}, inplace=True)

		lecat_df['watchlist_id'] = lecat_df['first_name'].index.map(str)
		lecat_df['watchlist_id'] = lecat_df['watchlist_id'].map(np.int64)

		for col in ['first_name']:
			lecat_df[col] = lecat_df[col].apply(lambda x: str(x) if x else '')

		lecat_df['second_name'] = ''
		lecat_df['third_name'] = ''
		lecat_df['fourth_name'] = ''
		lecat_df['watchlist'] = 'LECAT'
		lecat_df['created_at'] = timestamp

		return lecat_df

schema = [
	bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
	bigquery.SchemaField("watchlist", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("watchlist_id", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("first_name", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("second_name", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("third_name", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("fourth_name", "STRING", mode="REQUIRED"),
]

@cloud_event
def pollWatchlistDownload(cloud_event):
	timestamp = pd.Timestamp.now(tz='UTC')
	ofac_df = Ofac().get_dataframe(timestamp)
	un_df = Un().get_dataframe(timestamp)
	pep_df = Pep().get_dataframe(timestamp)
	noticias_df = Noticias().get_dataframe(timestamp)
	lecat_df = Lecat().get_dataframe(timestamp)

	df = pd.concat([ofac_df, un_df, pep_df, noticias_df, lecat_df], ignore_index=True)
	if len(df) > 0:
		client = bigquery.Client()
		table_id = f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"

		table = bigquery.Table(table_id, schema=schema)
		table.clustering_fields = ["created_at"]
		table = client.create_table(table, exists_ok=True)
		job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
		job = client.load_table_from_dataframe(df, table, job_config=job_config)
		try:
			job.result()
			print(f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id} (size={asizeof.asizeof(df)})")
		except Exception as e:
			print(f"Could not load {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id} (size={asizeof.asizeof(df)})")
			print(f"Exception: {e}")
			print(job.errors)

	print("job done")
