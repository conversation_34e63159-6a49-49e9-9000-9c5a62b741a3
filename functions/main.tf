variable "environment" {}
variable "okex_api_key" {}
variable "okex_passphrase" {}
variable "okex_secret_key" {}
variable "statuspage_api_key" {}
variable "galoy_session_token" {}
variable "onfido_api_key" {}
variable "twilio_account" {}
variable "twilio_account_sid" {}
variable "twilio_auth_token" {}

locals {
  functions_sa     = "<EMAIL>"
  project          = "galoy-reporting"
  code_bucket_name = "reporting-cloud-functions"
  minutes_topic    = "minutes"
  hours_topic      = "hours"
  days_topic       = "days"
  environment      = var.environment

  okex_api_key_id    = "${local.environment}-okex-api-key"
  okex_passphrase_id = "${local.environment}-okex-passphrase-id"
  okex_secret_key_id = "${local.environment}-okex-secret-key-id"

  galoy_session_token = var.galoy_session_token
  statuspage_api_key  = var.statuspage_api_key
  galoy_api_endpoint  = local.environment == "galoy-staging" ? "https://api.staging.galoy.io:443/graphql" : "https://api.mainnet.galoy.io:443/graphql"

  onfido_api_key_id = "${local.environment}-onfido-api-key"

  twilio_account_id     = "${local.environment}-twilio-account"
  twilio_account_sid_id = "${local.environment}-twilio-account-sid"
  twilio_auth_token_id  = "${local.environment}-twilio-auth-token"
}

data "google_pubsub_topic" "minutes" {
  project = local.project
  name    = local.minutes_topic
}

data "google_pubsub_topic" "hours" {
  project = local.project
  name    = local.hours_topic
}

data "google_pubsub_topic" "days" {
  project = local.project
  name    = local.days_topic
}

module "dataset" {
  source        = "../imports/raw_dataset"
  environment   = local.environment
  component     = "functions"
  view_all_envs = true
}

resource "google_bigquery_dataset_iam_member" "functions" {
  project    = local.project
  dataset_id = module.dataset.dataset_id
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:${local.functions_sa}"
}


# ========================
# Okex bid / ask
# ========================

data "archive_file" "okex_bid_ask" {
  type        = "zip"
  source_dir  = "${path.module}/okex-bid-ask"
  output_path = "${path.module}/tmp/okex-bid-ask.zip"
}

resource "google_storage_bucket_object" "okex_bid_ask" {
  name   = "okex-bid-ask.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okex_bid_ask.output_path
}

resource "google_cloudfunctions_function" "okex_bid_ask" {
  project     = local.project
  region      = "us-east1"
  name        = "${local.environment}-pollOkexBidAsk-${substr(base64encode(google_storage_bucket_object.okex_bid_ask.crc32c), 0, 6)}"
  entry_point = "pollOkexBidAsk"
  description = "Poll okex bid and ask prices for ${local.environment}"
  runtime     = "nodejs20"

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okex_bid_ask.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okex_bid_ask"
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Coincap Price
# ================
data "archive_file" "coincap_price" {
  type        = "zip"
  source_dir  = "${path.module}/coincap-price"
  output_path = "${path.module}/tmp/coincap-price.zip"
}

resource "google_storage_bucket_object" "coincap_price" {
  name   = "coincap-price.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.coincap_price.output_path
}

resource "google_cloudfunctions_function" "coincap_price" {
  project     = local.project
  region      = "us-east1"
  name        = "${local.environment}-pollCoincapPrice-${substr(base64encode(google_storage_bucket_object.coincap_price.crc32c), 0, 6)}"
  entry_point = "pollCoincapPrice"
  description = "Poll coincap.io prices for ${local.environment}"
  runtime     = "nodejs20"

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.coincap_price.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "coincap_price"
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okex bills
# ================

data "archive_file" "okex_bills" {
  type        = "zip"
  source_dir  = "${path.module}/okex-bills"
  output_path = "${path.module}/tmp/okex-bills.zip"
}

resource "google_storage_bucket_object" "okex_bills" {
  name   = "okex-bills.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okex_bills.output_path
}

resource "google_cloudfunctions_function" "okex_bills" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOKXBills-${substr(base64encode(google_storage_bucket_object.okex_bills.crc32c), 0, 6)}"
  entry_point         = "pollOKXBills"
  description         = "Poll okex bills for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okex_bills.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okex_bills"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okex bills archive
# ================

data "archive_file" "okex_bills_archive" {
  type        = "zip"
  source_dir  = "${path.module}/okex-bills-archive"
  output_path = "${path.module}/tmp/okex-bills-archive.zip"
}

resource "google_storage_bucket_object" "okex_bills_archive" {
  name   = "okex-bills-archive.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okex_bills_archive.output_path
}

resource "google_cloudfunctions_function" "okex_bills_archive" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOKXBillsArchive-${substr(base64encode(google_storage_bucket_object.okex_bills_archive.crc32c), 0, 6)}"
  entry_point         = "pollOKXBillsArchive"
  description         = "Poll okex bills archive for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okex_bills_archive.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okex_bills_archive"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okx Trading Account History
# ================

data "archive_file" "okx_trading_account_history" {
  type        = "zip"
  source_dir  = "${path.module}/okx-trading-account-history"
  output_path = "${path.module}/tmp/okx-trading-account-history.zip"
}

resource "google_storage_bucket_object" "okx_trading_account_history" {
  name   = "okx-trading-account-history.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okx_trading_account_history.output_path
}

resource "google_cloudfunctions_function" "okx_trading_account_history" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOkxTradingAccountHistory-${substr(base64encode(google_storage_bucket_object.okx_trading_account_history.crc32c), 0, 6)}"
  entry_point         = "pollOkxTradingAccountHistory"
  description         = "Poll okx trading account history for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okx_trading_account_history.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okx_trading_account_history"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okx Funding Account History
# ================

data "archive_file" "okx_funding_account_history" {
  type        = "zip"
  source_dir  = "${path.module}/okx-funding-account-history"
  output_path = "${path.module}/tmp/okx-funding-account-history.zip"
}

resource "google_storage_bucket_object" "okx_funding_account_history" {
  name   = "okx-funding-account-history.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okx_funding_account_history.output_path
}

resource "google_cloudfunctions_function" "okx_funding_account_history" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOkxFundingAccountHistory-${substr(base64encode(google_storage_bucket_object.okx_funding_account_history.crc32c), 0, 6)}"
  entry_point         = "pollOkxFundingAccountHistory"
  description         = "Poll okx funding account history for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okx_funding_account_history.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okx_funding_account_history"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okex balances
# ================

data "archive_file" "okex_balances" {
  type        = "zip"
  source_dir  = "${path.module}/okex-balances"
  output_path = "${path.module}/tmp/okex-balances.zip"
}

resource "google_storage_bucket_object" "okex_balances" {
  name   = "okex-balances.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okex_balances.output_path
}

resource "google_cloudfunctions_function" "okex_balances" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOKXBalances-${substr(base64encode(google_storage_bucket_object.okex_balances.crc32c), 0, 6)}"
  entry_point         = "pollOKXBalances"
  description         = "Poll okex balances for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okex_balances.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okex_balances"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okex position
# ================

data "archive_file" "okex_position" {
  type        = "zip"
  source_dir  = "${path.module}/okex-position"
  output_path = "${path.module}/tmp/okex-position.zip"
}

resource "google_storage_bucket_object" "okex_position" {
  name   = "okex-position.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okex_position.output_path
}

resource "google_cloudfunctions_function" "okex_position" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOKXPosition-${substr(base64encode(google_storage_bucket_object.okex_position.crc32c), 0, 6)}"
  entry_point         = "pollOKXPosition"
  description         = "Poll okex position for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okex_position.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okex_position"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Okex funding rates
# ================

data "archive_file" "okex_funding_rates" {
  type        = "zip"
  source_dir  = "${path.module}/okex-funding-rates"
  output_path = "${path.module}/tmp/okex-funding-rates.zip"
}

resource "google_storage_bucket_object" "okex_funding_rates" {
  name   = "okex-funding-rates.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.okex_funding_rates.output_path
}

resource "google_cloudfunctions_function" "okex_funding_rates" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOKXFundingRates-${substr(base64encode(google_storage_bucket_object.okex_funding_rates.crc32c), 0, 6)}"
  entry_point         = "pollOKXFundingRates"
  description         = "Poll okex funding rates for ${local.environment}"
  runtime             = "python310"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.okex_funding_rates.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "okex_funding_rates"
  }

  secret_environment_variables {
    key     = "OKEX_API_KEY"
    secret  = local.okex_api_key_id
    version = google_secret_manager_secret_version.okex_api_key.version
  }

  secret_environment_variables {
    key     = "OKEX_SECRET_KEY"
    secret  = local.okex_secret_key_id
    version = google_secret_manager_secret_version.okex_secret_key.version
  }

  secret_environment_variables {
    key     = "OKEX_PASSPHRASE"
    secret  = local.okex_passphrase_id
    version = google_secret_manager_secret_version.okex_passphrase.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ========================
# Statuspage
# ========================

data "archive_file" "statuspage" {
  type        = "zip"
  source_dir  = "${path.module}/statuspage"
  output_path = "${path.module}/tmp/statuspage.zip"
}

resource "google_storage_bucket_object" "statuspage" {
  name   = "statuspage.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.statuspage.output_path
}

resource "google_cloudfunctions_function" "statuspage" {
  project     = local.project
  region      = "us-east1"
  name        = "${local.environment}-updateStatuspage-${substr(base64encode(google_storage_bucket_object.statuspage.crc32c), 0, 6)}"
  entry_point = "updateStatuspage"
  description = "Update statuspage for ${local.environment}"
  runtime     = "nodejs20"

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.statuspage.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    GALOY_API_ENDPOINT = local.galoy_api_endpoint
    STATUSPAGE_API_KEY = local.statuspage_api_key
    SESSION_TOKEN      = local.galoy_session_token
    ENVIRONMENT        = local.environment
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "functions"
  }
}

# ================
# Gossip
# ================

data "archive_file" "gossip" {
  type        = "zip"
  source_dir  = "${path.module}/gossip"
  output_path = "${path.module}/tmp/gossip.zip"
}

resource "google_storage_bucket_object" "gossip" {
  name   = "gossip.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.gossip.output_path
}

resource "google_cloudfunctions_function" "gossip" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollGossip-${substr(base64encode(google_storage_bucket_object.gossip.crc32c), 0, 6)}"
  entry_point         = "pollGossip"
  description         = "Poll lightning network gossip updates ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.gossip.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT  = local.environment
    DATASET_ID   = module.dataset.dataset_id
    TABLE_PREFIX = "gossip"
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Onfido Workflow Runs
# ================

data "archive_file" "onfido_workflow_runs" {
  type        = "zip"
  source_dir  = "${path.module}/onfido-workflow-runs"
  output_path = "${path.module}/tmp/onfido-workflow-runs.zip"
}

resource "google_storage_bucket_object" "onfido_workflow_runs" {
  name   = "onfido-workflow-runs.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.onfido_workflow_runs.output_path
}

resource "google_cloudfunctions_function" "onfido_workflow_runs" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollOnfidoWorkflowRuns-${substr(base64encode(google_storage_bucket_object.onfido_workflow_runs.crc32c), 0, 6)}"
  entry_point         = "pollOnfidoWorkflowRuns"
  description         = "Poll onfido workflow runs for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.onfido_workflow_runs.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "onfido_workflow_runs"
  }

  secret_environment_variables {
    key     = "ONFIDO_API_KEY"
    secret  = local.onfido_api_key_id
    version = google_secret_manager_secret_version.onfido_api_key.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# AML Watchlist Download
# ================

data "archive_file" "watchlist_download" {
  type        = "zip"
  source_dir  = "${path.module}/watchlist-download"
  output_path = "${path.module}/tmp/watchlist-download.zip"
}

resource "google_storage_bucket_object" "watchlist_download" {
  name   = "watchlist-download.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.watchlist_download.output_path
}

resource "google_cloudfunctions_function" "watchlist_download" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollWatchlistDownload-${substr(base64encode(google_storage_bucket_object.watchlist_download.crc32c), 0, 6)}"
  entry_point         = "pollWatchlistDownload"
  description         = "Poll aml watchlist download for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 2048
  timeout             = 540

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.watchlist_download.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "watchlist"
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# AML Watchlist Compare
# ================

data "archive_file" "watchlist_compare" {
  type        = "zip"
  source_dir  = "${path.module}/watchlist-compare"
  output_path = "${path.module}/tmp/watchlist-compare.zip"
}

resource "google_storage_bucket_object" "watchlist_compare" {
  name   = "watchlist-compare.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.watchlist_compare.output_path
}

resource "google_cloudfunctions_function" "watchlist_compare" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollWatchlistCompare-${substr(base64encode(google_storage_bucket_object.watchlist_compare.crc32c), 0, 6)}"
  entry_point         = "pollWatchlistCompare"
  description         = "Poll aml watchlist compare for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 8192
  timeout             = 540

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.watchlist_compare.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "watchlist_compare"
  }

  secret_environment_variables {
    key     = "ONFIDO_API_KEY"
    secret  = local.onfido_api_key_id
    version = google_secret_manager_secret_version.onfido_api_key.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Gossip2
# ================

data "archive_file" "gossip2" {
  type        = "zip"
  source_dir  = "${path.module}/gossip2"
  output_path = "${path.module}/tmp/gossip2.zip"
}

resource "google_storage_bucket_object" "gossip2" {
  name   = "gossip2.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.gossip2.output_path
}

resource "google_cloudfunctions_function" "gossip2" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollGossip2-${substr(base64encode(google_storage_bucket_object.gossip2.crc32c), 0, 6)}"
  entry_point         = "pollGossip2"
  description         = "Poll full lightning network gossip updates ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.gossip2.name

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.hours.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Bitfinex order book
# ================

data "archive_file" "bitfinex_book" {
  type        = "zip"
  source_dir  = "${path.module}/bitfinex-book"
  output_path = "${path.module}/tmp/bitfinex-book.zip"
}

resource "google_storage_bucket_object" "bitfinex_book" {
  name   = "bitfinex-book.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.bitfinex_book.output_path
}

resource "google_cloudfunctions_function" "bitfinex_book" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollBitfinexBook-${substr(base64encode(google_storage_bucket_object.bitfinex_book.crc32c), 0, 6)}"
  entry_point         = "pollBitfinexBook"
  description         = "Poll bitfinex book for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.bitfinex_book.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "bitfinex_book"
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Bitfinex order trades
# ================

data "archive_file" "bitfinex_trades" {
  type        = "zip"
  source_dir  = "${path.module}/bitfinex-trades"
  output_path = "${path.module}/tmp/bitfinex-trades.zip"
}

resource "google_storage_bucket_object" "bitfinex_trades" {
  name   = "bitfinex-trades.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.bitfinex_trades.output_path
}

resource "google_cloudfunctions_function" "bitfinex_trades" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollBitfinexTrades-${substr(base64encode(google_storage_bucket_object.bitfinex_trades.crc32c), 0, 6)}"
  entry_point         = "pollBitfinexTrades"
  description         = "Poll bitfinex trades for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.bitfinex_trades.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.minutes.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "bitfinex_trades"
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Twilio Billing
# ================

data "archive_file" "twilio_billing" {
  type        = "zip"
  source_dir  = "${path.module}/twilio-billing"
  output_path = "${path.module}/tmp/twilio-billing.zip"
}

resource "google_storage_bucket_object" "twilio_billing" {
  name   = "twilio-billing.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.twilio_billing.output_path
}

resource "google_cloudfunctions_function" "twilio_billing" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollTwilioBilling-${substr(base64encode(google_storage_bucket_object.twilio_billing.crc32c), 0, 6)}"
  entry_point         = "pollTwilioBilling"
  description         = "Poll Twilio usage reports for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.twilio_billing.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.days.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "twilio_billing"
  }

  secret_environment_variables {
    key     = "TWILIO_ACCOUNT"
    secret  = local.twilio_account_id
    version = google_secret_manager_secret_version.twilio_account.version
  }

  secret_environment_variables {
    key     = "TWILIO_ACCOUNT_SID"
    secret  = local.twilio_account_sid_id
    version = google_secret_manager_secret_version.twilio_account_sid.version
  }

  secret_environment_variables {
    key     = "TWILIO_AUTH_TOKEN"
    secret  = local.twilio_auth_token_id
    version = google_secret_manager_secret_version.twilio_auth_token.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}

# ================
# Twilio Verifications
# ================

data "archive_file" "twilio_verifications" {
  type        = "zip"
  source_dir  = "${path.module}/twilio-verifications"
  output_path = "${path.module}/tmp/twilio-verifications.zip"
}

resource "google_storage_bucket_object" "twilio_verifications" {
  name   = "twilio-verifications.zip"
  bucket = local.code_bucket_name
  source = data.archive_file.twilio_verifications.output_path
}

resource "google_cloudfunctions_function" "twilio_verifications" {
  project             = local.project
  region              = "us-east1"
  name                = "${local.environment}-pollTwilioVerifications-${substr(base64encode(google_storage_bucket_object.twilio_verifications.crc32c), 0, 6)}"
  entry_point         = "pollTwilioVerifications"
  description         = "Poll Twilio verifications history for ${local.environment}"
  runtime             = "python311"
  available_memory_mb = 512

  source_archive_bucket = local.code_bucket_name
  source_archive_object = google_storage_bucket_object.twilio_verifications.name
  docker_registry       = "CONTAINER_REGISTRY"

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = data.google_pubsub_topic.days.id
  }

  environment_variables = {
    ENVIRONMENT = local.environment
    DATASET_ID  = module.dataset.dataset_id
    TABLE_ID    = "twilio_verifications"
  }

  secret_environment_variables {
    key     = "TWILIO_ACCOUNT"
    secret  = local.twilio_account_id
    version = google_secret_manager_secret_version.twilio_account.version
  }

  secret_environment_variables {
    key     = "TWILIO_ACCOUNT_SID"
    secret  = local.twilio_account_sid_id
    version = google_secret_manager_secret_version.twilio_account_sid.version
  }

  secret_environment_variables {
    key     = "TWILIO_AUTH_TOKEN"
    secret  = local.twilio_auth_token_id
    version = google_secret_manager_secret_version.twilio_auth_token.version
  }

  service_account_email = local.functions_sa

  lifecycle {
    create_before_destroy = true
  }
}
