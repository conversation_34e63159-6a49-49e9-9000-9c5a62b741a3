import requests
from datetime import datetime
from os import environ
from google.cloud import bigquery
from functions_framework import cloud_event

schema = [
	bigquery.Schema<PERSON>ield("response", "JSON", mode="REQUIRED"),
	bigquery.<PERSON>hema<PERSON>ield("inserted_at", "TIMESTAMP", mode="REQUIRED"),
]

@cloud_event
def pollBitfinexBook(cloud_event):
	client = bigquery.Client()
	table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"

	response = requests.get(
		"https://api-pub.bitfinex.com/v2/book/tBTCUSD/R0?len=100",
		headers={"accept": "application/json"},
	)
	response.raise_for_status()
	book = response.json()

	print(f"Inserting {len(book)} price points.")

	table = bigquery.Table(table_id, schema=schema)
	table.clustering_fields = ["inserted_at"]
	table = client.create_table(table, exists_ok=True)
	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
	job = client.load_table_from_json([{
		"response": book,
		"inserted_at": datetime.now().isoformat()
	}], table, job_config=job_config)
	job.result()
