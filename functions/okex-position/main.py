#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os
import pandas as pd
import numpy as np

from decimal import Decimal

import ccxt

def fetch_build_position_df(exchange):
    #
    # get position
    #
    position = exchange.fetch_position('BTC-USD-SWAP')

    if not position:
        print("No position")
        return pd.DataFrame(
            [[np.uint64(pd.Timestamp.now(tz='UTC').timestamp() * 1000), None, None, None, None, None, 0, None, None, None, None, None, None,]],
            index=['timestamp'],
            columns=['timestamp', 'last', 'notional_usd', 'margin', 'auto_deleveraging_indicator', 'liquidation_price', 'position_quantity',
            'average_open_price', 'unrealized_pnl', 'unrealized_pnl_ratio', 'margin_ratio', 'maintenance_margin_requirement', 'exchange_leverage',
            ])

    # convert nested list of dictionaries to pandas DataFrame
    df = pd.json_normalize(position["info"])

    if len(df.index) == 0:
        print("No data")
        return df

    df = df.loc[:, ['uTime','last','notionalUsd','imr','adl','liqPx','pos','avgPx','upl','uplRatio','mgnRatio','mmr','lever']]

    # replace timestamp with current time
    df['uTime'] = np.uint64(pd.Timestamp.now(tz='UTC').timestamp() * 1000)

    # convert object types to required types (int64, float, decimal, string)
    for c in ['last', 'notionalUsd', 'imr', 'liqPx', 'avgPx', 'upl', 'uplRatio', 'mgnRatio', 'mmr']:
        df[c] = df[c].apply(lambda x: Decimal(x) if x else None)

    for c in ['adl','pos','lever']:
        df[c] = df[c].apply(lambda x: np.int64(x) if x else None)

    df.rename(columns={
        'uTime':'timestamp',
        'last':'last',
        'notionalUsd':'notional_usd',
        'imr':'margin',
        'adl': 'auto_deleveraging_indicator',
        'liqPx':'liquidation_price',
        'pos':'position_quantity',
        'avgPx':'average_open_price',
        'upl': 'unrealized_pnl',
        'uplRatio': 'unrealized_pnl_ratio',
        'mgnRatio': 'margin_ratio',
        'mmr': 'maintenance_margin_requirement',
        'lever':'exchange_leverage',
        }, inplace=True)

    df.set_index('timestamp', inplace=True)

    return df

@functions_framework.cloud_event
def pollOKXPosition(cloud_event):
    """https://www.okx.com/docs-v5/en/#rest-api-account-get-positions"""
    apikey = os.environ.get('OKEX_API_KEY')
    secretkey = os.environ.get('OKEX_SECRET_KEY')
    passphrase = os.environ.get('OKEX_PASSPHRASE')
    environment = os.environ.get('ENVIRONMENT')
    okx = ccxt.okex5({
        'apiKey':  apikey,
        'secret':  secretkey,
        'password':  passphrase,
    })

    if environment and 'staging' in environment.lower():
        okx.set_sandbox_mode(True)
    else:
        okx.set_sandbox_mode(False)

    client = bigquery.Client()
    table_id =  f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("last", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("notional_usd", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("margin", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("auto_deleveraging_indicator", "INT64", mode="NULLABLE"),
        bigquery.SchemaField("liquidation_price", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("position_quantity", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("average_open_price", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("unrealized_pnl", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("unrealized_pnl_ratio", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("margin_ratio", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("maintenance_margin_requirement", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("exchange_leverage", "INT64", mode="NULLABLE"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")
    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

    df = fetch_build_position_df(exchange=okx)

    if len(df) > 0:

        job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
        job_config.schema_update_options = [bigquery.SchemaUpdateOption.ALLOW_FIELD_RELAXATION]
        job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()

        table = client.get_table(table_id)  # Make an API request
        print(f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id}")

    print("job done")
