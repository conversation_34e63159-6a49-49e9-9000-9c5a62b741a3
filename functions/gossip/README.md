
# Persisting gossip data in bigquery

The topology of the lightning network changes every day.
These changes are a valuable source of information on
other participants in the network.
Currently, we take advantage of this data
in research on lightning network liquidity
(liquidity ads, average channel lifetimes, average fee rates)
but it will be valuable for liquidity management as well.
The only source we had before for this data was Christian Deckers
gossip archive (https://github.com/lnresearch/topology).
This cloud function will make a similar archive and persist it to bigquery
so that we always have the latest data and don't have to rely on
Christian releasing regular updates.

We get the data efficiently by querying LDK's rapidsync
endpoint (https://rapidsync.lightningdevkit.org/) and parsing
the results into a schema optimized for bigquery.
The code to run the rapidsync server is open source
(https://github.com/lightningdevkit/rapid-gossip-sync-server)
so if the endpoint maintained by the LDK team ever goes offline
we can spin up our own.

