import requests
from io import Bytes<PERSON>
from base64 import b64encode
from struct import unpack
from google.cloud import bigquery
from os import environ
from functions_framework import cloud_event
import sys

GOSSIP_PREFIX = b'LDK\x01'
MAX_INITIAL_NODE_ID_VECTOR_CAPACITY = 50_000

def parse_bigsize(b):
	(n,) = unpack("!B", b.read(1))
	if n == 0xFF:
		(x,) = unpack("!Q", b.read(8))
		if x < 0x100000000:
			raise ValueError("Invalid value")
		return x
	if n == 0xFE:
		(x,) = unpack("!L", b.read(4))
		if x < 0x10000:
			raise ValueError("Invalid value")
		return x
	if n == 0xFD:
		(x,) = unpack("!H", b.read(2))
		if x < 0xFD:
			raise ValueError("Invalid value")
		return x
	return n

def direction(standard_channel_flags):
        return standard_channel_flags & 0x01

def update_network_graph_from_byte_stream(b):

	prefix = b.read(4)
	if prefix != GOSSIP_PREFIX:
		raise ValueError("Invalid gossip prefix")
	
	chain_hash = b.read(32)

	(latest_seen_timestamp,) = unpack('!L', b.read(4))
	backdated_timestamp = max(0, latest_seen_timestamp - 24 * 3600 * 7)

	(node_id_count,) = unpack('!L', b.read(4))
	node_ids = []
	for i in range(node_id_count):
		node_ids.append(b.read(33))

	(announcement_count,) = unpack('!L', b.read(4))
	announcements = []
	previous_scid = 0
	for i in range(announcement_count):

		(features_size,) = unpack('!H', b.read(2))
		features = b.read(features_size)

		scid_delta = parse_bigsize(b)
		short_channel_id = previous_scid + scid_delta
		previous_scid = short_channel_id

		node_id_1_index = parse_bigsize(b)
		node_id_2_index = parse_bigsize(b)
		if max(node_id_1_index, node_id_2_index) >= node_id_count:
			raise ValueError("Invalid node_id")
		node_id_1 = node_ids[node_id_1_index]
		node_id_2 = node_ids[node_id_2_index]

		announcements.append({
			"short_channel_id": b64encode(short_channel_id.to_bytes(8,'big')).decode(),
			"timestamp": backdated_timestamp,
			"features": b64encode(features).decode(),
			"node_id_1": b64encode(node_id_1).decode(),
			"node_id_2": b64encode(node_id_2).decode(),
		})

	(update_count,) = unpack('!L', b.read(4))
	if update_count == 0:
		return latest_seen_timestamp, announcements, []

	(default_cltv_expiry_delta,) = unpack('!H', b.read(2))
	(default_htlc_minimum_msat,) = unpack('!Q', b.read(8))
	(default_fee_base_msat,) = unpack('!L', b.read(4))
	(default_fee_proportional_millionths,) = unpack('!L', b.read(4))
	(default_htlc_maximum_msat,) = unpack('!Q', b.read(8))

	updates = []
	previous_scid = 0
	for i in range(update_count):

		scid_delta = parse_bigsize(b)
		short_channel_id = previous_scid + scid_delta
		previous_scid = short_channel_id

		(channel_flags,) = unpack("!B", b.read(1))

		standard_channel_flags = channel_flags & 0b_0000_0011

		# incremental update, field flags will indicate mutated values
		if channel_flags & 0b_1000_0000 != 0:
			synthetic_update = {
				"chain_hash": b64encode(chain_hash).decode(),
				"short_channel_id": b64encode(short_channel_id.to_bytes(8,'big')).decode(),
				"timestamp": backdated_timestamp,
				"flags": standard_channel_flags,
				"is_node_1": direction(standard_channel_flags)==0,
				"cltv_expiry_delta": None,
				"htlc_minimum_msat": None,
				"htlc_maximum_msat": None,
				"fee_base_msat": None,
				"fee_proportional_millionths": None,
			}
		# full update, field flags will indicate differences from defaults
		else:
			synthetic_update = {
				"chain_hash": b64encode(chain_hash).decode(),
				"short_channel_id": b64encode(short_channel_id.to_bytes(8,'big')).decode(),
				"timestamp": backdated_timestamp,
				"flags": standard_channel_flags,
				"is_node_1": direction(standard_channel_flags)==0,
				"cltv_expiry_delta": default_cltv_expiry_delta,
				"htlc_minimum_msat": default_htlc_minimum_msat,
				"htlc_maximum_msat": default_htlc_maximum_msat,
				"fee_base_msat": default_fee_base_msat,
				"fee_proportional_millionths": default_fee_proportional_millionths,
			}

		if channel_flags & 0b_0100_0000 > 0:
			(cltv_expiry_delta,) = unpack("!H", b.read(2))
			synthetic_update["cltv_expiry_delta"] = cltv_expiry_delta;

		if channel_flags & 0b_0010_0000 > 0:
			(htlc_minimum_msat,) = unpack("!Q", b.read(8))
			synthetic_update["htlc_minimum_msat"] = htlc_minimum_msat

		if channel_flags & 0b_0001_0000 > 0:
			(fee_base_msat,) = unpack("!L", b.read(4))
			synthetic_update["fee_base_msat"] = fee_base_msat

		if channel_flags & 0b_0000_1000 > 0:
			(fee_proportional_millionths,) = unpack("!L", b.read(4))
			synthetic_update["fee_proportional_millionths"] = fee_proportional_millionths

		if channel_flags & 0b_0000_0100 > 0:
			(htlc_maximum_msat,) = unpack("!Q", b.read(8))
			synthetic_update["htlc_maximum_msat"] = htlc_maximum_msat

		updates.append(synthetic_update)

	return latest_seen_timestamp, announcements, updates

timestamp_schema = [
	bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
]
announcements_schema = [
	bigquery.SchemaField("short_channel_id", "BYTES", mode="REQUIRED"),
	bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("features", "BYTES", mode="REQUIRED"),
	bigquery.SchemaField("node_id_1", "BYTES", mode="REQUIRED"),
	bigquery.SchemaField("node_id_2", "BYTES", mode="REQUIRED"),
]
updates_schema = [
	bigquery.SchemaField("chain_hash", "BYTES", mode="REQUIRED"),
	bigquery.SchemaField("short_channel_id", "BYTES", mode="REQUIRED"),
	bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("flags", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("is_node_1", "BOOL", mode="REQUIRED"),
	bigquery.SchemaField("cltv_expiry_delta", "INT64", mode="NULLABLE"),
	bigquery.SchemaField("htlc_minimum_msat", "INT64", mode="NULLABLE"),
	bigquery.SchemaField("htlc_maximum_msat", "INT64", mode="NULLABLE"),
	bigquery.SchemaField("fee_base_msat", "INT64", mode="NULLABLE"),
	bigquery.SchemaField("fee_proportional_millionths", "INT64", mode="NULLABLE"),
]

@cloud_event
def pollGossip(cloud_event):

	client = bigquery.Client()
	timestamp_table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_PREFIX')}_timestamp"
	announcements_table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_PREFIX')}_announcements"
	updates_table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_PREFIX')}_updates"

	timestamp_table = bigquery.Table(timestamp_table_id, schema=timestamp_schema)
	announcements_table = bigquery.Table(announcements_table_id, schema=announcements_schema)
	announcements_table.clustering_fields = ["timestamp"]
	updates_table = bigquery.Table(updates_table_id, schema=updates_schema)
	updates_table.clustering_fields = ["timestamp"]

	try:
		result = client.query(f"SELECT timestamp FROM `{timestamp_table_id}` LIMIT 1").result()
		(timestamp,) = next(result)
	except:
		print("Could not find last timestamp")
		timestamp = 0

	client.delete_table(timestamp_table_id, not_found_ok=True)
	timestamp_table = client.create_table(timestamp_table)
	announcements_table = client.create_table(announcements_table, exists_ok=True)
	updates_table = client.create_table(updates_table, exists_ok=True)

	timestamp_job_config = bigquery.LoadJobConfig(schema=timestamp_schema, autodetect=False)
	announcements_job_config = bigquery.LoadJobConfig(schema=announcements_schema, autodetect=False)
	updates_job_config = bigquery.LoadJobConfig(schema=updates_schema, autodetect=False)

	r = requests.get(f"https://rapidsync.lightningdevkit.org/snapshot/{timestamp}", stream=True)
	try:
		new_timestamp, announcements, updates = update_network_graph_from_byte_stream(BytesIO(r.content))
	except Exception as err:
		print("Error while requesting rapid sync:", err)
		print([(i, b) for (i, b) in enumerate(r.content)])
		return


	job = client.load_table_from_json(announcements, announcements_table, job_config=announcements_job_config)
	try:
		job.result()
	except:
		print(job.errors)
		sys.exit("load errors")

	job = client.load_table_from_json(updates, updates_table, job_config=updates_job_config)
	try:
		job.result()
	except:
		print(job.errors)
		sys.exit("load errors")

	job = client.load_table_from_json([{"timestamp": new_timestamp}], timestamp_table, job_config=timestamp_job_config)
	try:
		job.result()
	except:
		print(job.errors)
		sys.exit("load errors")
