import requests
from os import environ
import itertools
import datetime
from dateutil import parser
from google.cloud import bigquery
from functions_framework import cloud_event

class WorkflowRuns:
	def __init__(self):
		self.s = requests.Session()
		api_key = environ.get('ONFIDO_API_KEY')
		self.s.headers.update({'Authorization': f'Token token={api_key}'})

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_page(self, page):
		r = self.s.get(
			'https://api.eu.onfido.com/v3.6/workflow_runs',
			params={'page': page},
		)
		return r.json()

	def get_iter(self):
		"Lazily get all workflow runs handling paging correctly."
		return itertools.chain.from_iterable(
			itertools.takewhile(
				lambda x: len(x) > 0, 
				(self.get_page(i) for i in itertools.count(1))
			)
		)

schema = [
	bigquery.SchemaField("applicant_id", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
	bigquery.SchemaField("dashboard_url", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("reasons", "STRING", mode="REPEATED"),
	bigquery.SchemaField("status", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("tags", "STRING", mode="REPEATED"),
	bigquery.SchemaField("updated_at", "TIMESTAMP", mode="REQUIRED"),
	bigquery.SchemaField("workflow_id", "STRING", mode="REQUIRED"),
	bigquery.SchemaField("workflow_version_id", "INT64", mode="REQUIRED"),

	bigquery.SchemaField("document_issuing_country", "STRING", mode="NULLABLE"),
	bigquery.SchemaField("document_type", "STRING", mode="NULLABLE"),
]

@cloud_event
def pollOnfidoWorkflowRuns(cloud_event):
	client = bigquery.Client()
	table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"
	try:
		result = client.query(f"SELECT MAX(updated_at) FROM `{table_id}` LIMIT 1").result()
		(last_updated_at,) = next(result)
	except:
		print("Could not find last updated_at")
		last_updated_at = datetime.datetime(1, 1, 1, tzinfo=datetime.timezone.utc)

	new_rows = []
	wfrs = WorkflowRuns()
	for wfr in wfrs.get_iter():
		if parser.parse(wfr['updated_at']) < last_updated_at:
			break
		row = {}
		row['applicant_id'] = wfr.get('applicant_id')
		row['created_at'] = wfr.get('created_at')
		row['dashboard_url'] = wfr.get('dashboard_url')
		row['id'] = wfr.get('id')
		row['reasons'] = wfr.get('reasons')
		row['status'] = wfr.get('status')
		row['tags'] = wfr.get('tags')
		row['updated_at'] = wfr.get('updated_at')
		row['workflow_id'] = wfr.get('workflow_id')
		row['workflow_version_id'] = wfr.get('workflow_version_id')
		row['document_issuing_country'] = (wfr.get("output") or {}).get('document_issuing_country')
		row['document_type'] = (wfr.get("output") or {}).get('document_type')

		new_rows.append(row)

	print(f"Appending {len(new_rows)} rows.")

	table = bigquery.Table(table_id, schema=schema)
	table.clustering_fields = ["updated_at"]
	table = client.create_table(table, exists_ok=True)
	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
	job = client.load_table_from_json(new_rows, table, job_config=job_config)
	try:
		job.result()
	except:
		print("Could not load")
		print(job.errors)
