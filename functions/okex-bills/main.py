#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os
import pandas as pd

from decimal import Decimal
from datetime import datetime

import ccxt
import numpy as np

def todate(ts):
    return datetime.utcfromtimestamp(int(ts)/1000).isoformat()

def fetch_build_billing_df(exchange, since=0):
    # get last 7 days bills details from exchange
    bills = exchange.fetch_ledger(since=since, params={'mgnMode':'cross', 'instType':'SWAP'})

    # convert nested list of dictionaries to pandas DataFrame
    df = pd.json_normalize(bills)
    df.rename(columns={'info.type':'info_type', 'fee.cost':'fee_cost', 'fee.currency':'fee_currency'}, inplace=True)
    df.rename(columns={c:c.removeprefix('info.') for c in df.columns}, inplace=True)

    # convert object types to required types (uint64, float, decimal, string)
    for c in ['id', 'ts', 'timestamp', 'subType', 'sz', 'info_type']:
        df[c] = pd.to_numeric(df[c], errors='coerce', downcast='unsigned')

    for c in ['amount', 'before', 'after', 'bal', 'balChg', 'fee', 'pnl', 'posBal', 'posBalChg', 'fee_cost']:
        df[c] = df[c].apply(lambda x: Decimal(str(x)) if x is not None else x)

    for c in ['ordId', 'to']:
        df[c] = df[c].apply(lambda x: np.uint64(x) if x != '' else None)

    # referenceId not always present, billId is
    df['referenceId'] = df['referenceId'].apply(lambda x: np.uint64(x) if x is not None else x)
    df['billId'] = df.billId.astype(np.uint64)

    df['datetime'] = pd.to_datetime(df.datetime)

    # Only None accepted by BigQuery for NULL
    df.replace('', None, inplace=True)

    return df[[
        "id",
        "timestamp",
        "datetime",
        "account",
        "referenceId",
        "referenceAccount",
        "type",
        "currency",
        "symbol",
        "amount",
        "before",
        "after",
        "status",
        "bal",
        "balChg",
        "billId",
        "ccy",
        "execType",
        "fee",
        "from",
        "instId",
        "instType",
        "mgnMode",
        "notes",
        "ordId",
        "pnl",
        "posBal",
        "posBalChg",
        "subType",
        "sz",
        "to",
        "ts",
        "info_type",
        "fee_cost",
        "fee_currency",
    ]]

@functions_framework.cloud_event
def pollOKXBills(cloud_event):
    apikey = os.environ.get('OKEX_API_KEY')
    secretkey = os.environ.get('OKEX_SECRET_KEY')
    passphrase = os.environ.get('OKEX_PASSPHRASE')
    okx = ccxt.okex5({
        'apiKey':  apikey,
        'secret':  secretkey,
        'password':  passphrase,
    })

    if 'staging' in os.environ.get('ENVIRONMENT').lower():
        okx.set_sandbox_mode(True)
    else:
        okx.set_sandbox_mode(False)

    client = bigquery.Client()
    table_id =  f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("id", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("datetime", "DATETIME", mode="REQUIRED"), # DATETIME?
        bigquery.SchemaField("account", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("referenceId", "INT64", mode="NULLABLE"),
        bigquery.SchemaField("referenceAccount", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("type", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("currency", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("symbol", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("amount", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("before", "BIGNUMERIC", mode="NULLABLE"),  # weird no?
        bigquery.SchemaField("after", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("status", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("bal", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("balChg", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("billId", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("ccy", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("execType", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("fee", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("from", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("instId", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("instType", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("mgnMode", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("notes", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("ordId", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("pnl", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("posBal", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("posBalChg", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("subType", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("sz", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("to", "INT64", mode="NULLABLE"),
        bigquery.SchemaField("ts", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("info_type", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("fee_cost", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("fee_currency", "STRING", mode="REQUIRED"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")

        sql_query = f"SELECT billId, timestamp FROM `{table_id}` ORDER BY timestamp DESC LIMIT 1"
        query_job = client.query(sql_query)
        extable = query_job.result()

        if extable.total_rows == 0:
            remote_timestamp = 0
            remote_billId = 0
            print("Empty table? No entry found")
        else:
            for r in extable:
                remote_billId = np.uint64(r['billId'])
                remote_timestamp = r['timestamp']
            print(f"Most recent entry in cloud table: {todate(remote_timestamp)}")

        df = fetch_build_billing_df(exchange=okx, since=remote_timestamp).query("id>@remote_billId").drop_duplicates()

    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

        df = fetch_build_billing_df(exchange=okx)

    if len(df) > 0:

        job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
        job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()

        table = client.get_table(table_id)  # Make an API request
        print(f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id}")

    print("job done")
