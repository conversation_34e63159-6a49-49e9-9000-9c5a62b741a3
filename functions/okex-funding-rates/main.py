#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os
import pandas as pd

from decimal import Decimal
from datetime import datetime

import ccxt
import numpy as np

def todate(ts):
    return datetime.utcfromtimestamp(int(ts)/1000).isoformat()

def fetch_build_funding_rates_df(exchange, since=0):
    #
    # get last 3 months funding rates details from exchange
    #
    funding_rates = exchange.fetch_funding_rate_history('BTC-USD-SWAP', params={'before': since})

    # convert nested list of dictionaries to pandas DataFrame
    df = pd.json_normalize(funding_rates)

    if len(df.index) == 0:
        return df

    df.rename(columns={
        'info.fundingTime': 'funding_time',
        'info.instId': 'instrument_id',
        'info.realizedRate': 'funding_rate',
    }, inplace=True)
    df = df.loc[:, ['funding_time', 'instrument_id', 'funding_rate']]

    # convert object types to required types (int64, float, decimal, string)
    df['funding_rate'] = df['funding_rate'].apply(
        lambda x: Decimal(str(x)) if x is not None else x)
    df['funding_time'] = df.funding_time.astype(np.uint64)

    df.set_index('funding_time', inplace=True)

    return df

@functions_framework.cloud_event
def pollOKXFundingRates(cloud_event):
    """https://www.okx.com/docs-v5/en/#rest-api-public-data-get-funding-rate-history"""
    apikey = os.environ.get('OKEX_API_KEY')
    secretkey = os.environ.get('OKEX_SECRET_KEY')
    passphrase = os.environ.get('OKEX_PASSPHRASE')
    environment = os.environ.get('ENVIRONMENT')
    okx = ccxt.okex5({
        'apiKey':  apikey,
        'secret':  secretkey,
        'password':  passphrase,
    })

    if environment and 'staging' in environment.lower():
        okx.set_sandbox_mode(True)
    else:
        okx.set_sandbox_mode(False)

    client = bigquery.Client()
    table_id =  f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("funding_time", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("instrument_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("funding_rate", "BIGNUMERIC", mode="REQUIRED"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")

        sql_query = f"SELECT funding_time FROM `{table_id}` ORDER BY funding_time DESC LIMIT 1"
        query_job = client.query(sql_query)
        extable = query_job.result()

        if extable.total_rows == 0:
            remote_funding_time = 0
            print("Empty table? No entry found")
        else:
            for r in extable:
                remote_funding_time = np.uint64(r['funding_time'])
            print(f"Most recent entry in cloud table: {todate(remote_funding_time)}")

        df = fetch_build_funding_rates_df(exchange=okx, since=remote_funding_time)

    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

        df = fetch_build_funding_rates_df(exchange=okx)

    if len(df) > 0:

        job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
        job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()

        table = client.get_table(table_id)  # Make an API request
        print(f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id}")

    print("job done")
