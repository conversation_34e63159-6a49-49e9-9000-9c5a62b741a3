import pandas as pd
from os import environ
from datetime import datetime
from google.cloud import bigquery
from functions_framework import cloud_event


class ReportList:
	def __init__(self):
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.stg_regulatory_report_list"
		print(self.table_id)

	def get_distinct_reports_list_df(self):
		try:
			sql_query = f'''
			SELECT DISTINCT report_norm, report_id, report_name, report_format, report_filename
			FROM `{self.table_id}`
			WHERE report_enabled
			ORDER BY report_norm, report_id
			;
			'''

			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()

			if len(df) <= 0:
				print(f"No report list data from query='{sql_query}'")
				return None
			print(f"Successfully read {len(df)} rows from {self.table_id}")
			return df
		except Exception as e:
			print(f"Could not get report list data from {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			return None

	def get_current_report_df(self, table_name):
		try:
			table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.{table_name}"
			sql_query = f"""
			SELECT *
			FROM `{table_id}`
			WHERE created_at = (SELECT MAX(created_at) FROM {table_id})
			ORDER BY 1
			;
			"""

			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()

			if len(df) <= 0:
				print(f"No report data from query='{sql_query}'")
				return None
			print(f"Successfully read {len(df)} rows from {table_id}")
			return df
		except Exception as e:
			print(f"Could not get report data from {table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			return None

class RegulatoryReportExport:
	def __init__(self):
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"
		self.schema = [
			bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
			# bigquery.SchemaField("report_created_at", "TIMESTAMP", mode="REQUIRED"),
			bigquery.SchemaField("report_created_at", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("report_norm", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("report_id", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("report_name", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("report_filename", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("xml_data", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("csv_data", "STRING", mode="REQUIRED"),
		]

	def save_reports(self, data):
		table = bigquery.Table(self.table_id, schema=self.schema)
		table.clustering_fields = ["created_at"]
		table = self.client.create_table(table, exists_ok=True)
		job_config = bigquery.LoadJobConfig(schema=self.schema, autodetect=False)
		job = self.client.load_table_from_json(data, table, job_config=job_config)
		try:
			job.result()
			print(f"Loaded {len(data)} {'rows' if len(data)>1 else 'row'} with {len(table.schema)} columns to {self.table_id}")
		except Exception as e:
			print(f"Could not load {len(data)} {'rows' if len(data)>1 else 'row'} with {len(table.schema)} columns to {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)


@cloud_event
def pollRegulatoryReportExport(cloud_event):
	################################
	# get report list
	################################
	report_list = ReportList()
	# list of reports to iterate over
	distinct_reports_list_df = report_list.get_distinct_reports_list_df()
	if (distinct_reports_list_df is None) or len(distinct_reports_list_df) <=0:
		print("Job failed")
		return

	for distinct_reports_row in distinct_reports_list_df.itertuples():
		table_name = f'stg_{distinct_reports_row.report_name.replace(".", "_")}'
		current_report_df = report_list.get_current_report_df(table_name)
		if (current_report_df is None) or len(current_report_df) <=0:
			print("Job failed")
			return

		# drop 'created_at' col
		report_created_at = current_report_df['created_at'][0]
		del current_report_df['created_at']

		################################
		# TODO: choose how and where to save report files, local | google drive | table
		################################

		# save report as local files
		if distinct_reports_row.report_format == 'XML':
			current_report_df.to_xml(distinct_reports_row.report_filename, index=False, root_name='data', row_name='row')
		else:
			current_report_df.to_csv(f'{distinct_reports_row.report_filename}.csv', index=False)

		# save report as string in bq
		xml_data = current_report_df.to_xml(None, index=False, root_name='data', row_name='row')
		csv_data = current_report_df.to_csv(None, index=False)
		data = [{
			"created_at": datetime.now().isoformat(),
			"report_created_at": report_created_at.isoformat(),
			"report_norm": distinct_reports_row.report_norm,
			"report_id": str(distinct_reports_row.report_id),
			"report_name": distinct_reports_row.report_name,
			"report_filename": distinct_reports_row.report_filename,
			"xml_data": str(xml_data),
			"csv_data": str(csv_data)
		}]
		exporter = RegulatoryReportExport()
		exporter.save_reports(data)

		# save report as files in google drive

	print("Job done")
