const functions = require('@google-cloud/functions-framework');
const { BigQuery } = require('@google-cloud/bigquery');
const axios = require("axios");

functions.cloudEvent('updateStatuspage', async (cloudEvent) => {
  console.log("HELLO WORLD");
  // const datasetId = process.env.DATASET_ID
  // const tableId = process.env.TABLE_ID
  // const bigquery = new BigQuery();

  // try {
  //   await bigquery.dataset(datasetId).table(tableId).get();
  //   console.log(`Table ${tableId} exists.`);
  // } catch (e) {
  //   console.log(`Creating table ${tableId}.`);
  //   schema = [
  //     { name: "timestamp", type: "TIMESTAMP", },
  //     { name: "price_usd", type: "STRING", },
  //   ];
  //   await bigquery.dataset(datasetId).createTable(tableId, { schema, location: 'US' });
  //   await new Promise(resolve => setTimeout(resolve, 10000));
  // }

  // const res = await axios.get('https://api.coincap.io/v2/assets/bitcoin');

  // const priceUsd = res.data.data.priceUsd;
  // const timestamp = res.data.timestamp;
  // const rows = [
  //   {
  //     price_usd: priceUsd,
  //     timestamp: Math.floor(timestamp / 1000),
  //   }
  // ];
  // console.log("Uploading rows:")
  // console.log(JSON.stringify(rows));
  // await bigquery
  //   .dataset(datasetId)
  //   .table(tableId)
  //   .insert(rows);
});
