import { cloudEvent } from '@google-cloud/functions-framework';

import { getLatencyAuthed, getLatencyUnauthed } from './galoy';
import { submitStatusPage } from './statuspage';

cloudEvent('updateStatuspage', async () => {
  try {
    const res = await getLatencyUnauthed()
    const duration = res.headers['request-duration'] as number;
    console.log(duration);
    await submitStatusPage({ value: duration, type: 'unauthed' });
  } catch (error) {
    console.error(error);
  }
  
  try {
    const res = await getLatencyAuthed()
    console.log(JSON.stringify(res.data));
    const duration = res.headers['request-duration'] as number;
    console.log(duration);
    await submitStatusPage({ value: duration, type: 'authed' });
  } catch (error) {
    console.error(error);
  }
})
