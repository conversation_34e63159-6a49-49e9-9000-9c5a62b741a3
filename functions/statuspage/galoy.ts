import axios, { AxiosResponse, AxiosInstance } from 'axios';

const createInstance = (): AxiosInstance => {
  const instance = axios.create();

  instance.interceptors.request.use((config) => {
    config.headers['request-startTime'] = new Date().valueOf();
    return config;
  });

  instance.interceptors.response.use((response: AxiosResponse) => {
    const currentTime = new Date().valueOf();      
    const startTime = response.config.headers['request-startTime'];      
    response.headers['request-duration'] = currentTime - startTime;
    return response;
  });

  return instance;
};

const queryUnauthed = JSON.stringify({
  query: `query globals {
    globals {
        nodesIds
    }
}`,
  variables: {}
});

const queryAuthed = JSON.stringify({
    query: `query meTest {
      me {
          id
          phone
          username
          language
          contacts {
              id
              username
              alias
              transactionsCount
          }
          createdAt
          defaultAccount {
              id
              defaultWalletId
              wallets {
                  id
                  walletCurrency
                  balance
              }
          }
      }
  }`,
})

const request = (data: string, authorization?: string) => ({
  method: 'post',
  url: process.env.GALOY_API_ENDPOINT,
  headers: { 
    ...authorization ? { 'Authorization': `Bearer ${authorization}` } : null,
    'Content-Type': 'application/json',
  },
  data,
})

console.log(request(queryAuthed, process.env.SESSION_TOKEN))

const instance = createInstance()
export const getLatencyUnauthed = () => instance(request(queryUnauthed));
export const getLatencyAuthed = () => instance(request(queryAuthed, process.env.SESSION_TOKEN));
