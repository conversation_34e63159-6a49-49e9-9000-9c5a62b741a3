import typescript from '@rollup/plugin-typescript';

export default [
  {
    input: 'index.ts',
    output: {
      file: 'dist/index.cjs',
      format: 'cjs'
    },
    plugins: [
      typescript({
        tsconfig: './tsconfig.json'
      })
    ]
  },
  {
    input: 'galoy.ts',
    output: {
      file: 'dist/galoy.js',
      format: 'cjs'
    },
    plugins: [
      typescript({
        tsconfig: './tsconfig.json'
      })
    ]
  },
  {
    input: 'statuspage.ts',
    output: {
      file: 'dist/statuspage.js',
      format: 'cjs'
    },
    plugins: [
      typescript({
        tsconfig: './tsconfig.json'
      }),
    ]
  },
];
