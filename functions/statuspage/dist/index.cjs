'use strict';

var functionsFramework = require('@google-cloud/functions-framework');
var axios = require('axios');

const createInstance = () => {
    const instance = axios.create();
    instance.interceptors.request.use((config) => {
        config.headers['request-startTime'] = new Date().valueOf();
        return config;
    });
    instance.interceptors.response.use((response) => {
        const currentTime = new Date().valueOf();
        const startTime = response.config.headers['request-startTime'];
        response.headers['request-duration'] = currentTime - startTime;
        return response;
    });
    return instance;
};
const queryUnauthed = JSON.stringify({
    query: `query globals {
    globals {
        nodesIds
    }
}`,
    variables: {}
});
const queryAuthed = JSON.stringify({
    query: `query meTest {
      me {
          id
          phone
          username
          language
          contacts {
              id
              username
              alias
              transactionsCount
          }
          createdAt
          defaultAccount {
              id
              defaultWalletId
              wallets {
                  id
                  walletCurrency
                  balance
              }
          }
      }
  }`,
});
const request = (data, authorization) => ({
    method: 'post',
    url: process.env.GALOY_API_ENDPOINT,
    headers: Object.assign(Object.assign({}, authorization ? { 'Authorization': `Bearer ${authorization}` } : null), { 'Content-Type': 'application/json' }),
    data,
});
console.log(request(queryAuthed, process.env.SESSION_TOKEN));
const instance = createInstance();
const getLatencyUnauthed = () => instance(request(queryUnauthed));
const getLatencyAuthed = () => instance(request(queryAuthed, process.env.SESSION_TOKEN));

const apiKey = process.env.STATUSPAGE_API_KEY;
const pageId = '7f7x3850vx56';
const apiBase = 'https://api.statuspage.io/v1';
const authHeader = { 'Authorization': `OAuth ${apiKey}` };
const options = { method: 'POST', headers: authHeader };
const bbwMappingMetricId = {
    unauthed: '2fzxp71557s5',
    authed: 'b8r6z0xn4ts2'
};
const stagingMappingMetricId = {
    unauthed: '2fzxp71557s5',
    authed: 'b8r6z0xn4ts2'
};
const mappingMetricId = process.env.ENVIRONMENT == "galoy-staging" ? stagingMappingMetricId : bbwMappingMetricId;
async function submitStatusPage({ value, type }) {
    const metricId = mappingMetricId[type];
    const url = `${apiBase}/pages/${pageId}/metrics/${metricId}/data.json`;
    const currentTimestamp = Math.floor(new Date().valueOf() / 1000);
    const data = {
        timestamp: currentTimestamp,
        value,
    };
    const res = await axios.post(url, { data }, options);
    if (res.status === 401) {
        const errorMessage = "Error: Unauthorized. Please check your API key and page ID.";
        console.error(errorMessage);
        throw new Error(errorMessage);
    }
    console.log(`Submitted point with data: ${JSON.stringify(data)}`);
}

functionsFramework.cloudEvent('updateStatuspage', async () => {
    try {
        const res = await getLatencyUnauthed();
        const duration = res.headers['request-duration'];
        console.log(duration);
        await submitStatusPage({ value: duration, type: 'unauthed' });
    }
    catch (error) {
        console.error(error);
    }
    try {
        const res = await getLatencyAuthed();
        console.log(JSON.stringify(res.data));
        const duration = res.headers['request-duration'];
        console.log(duration);
        await submitStatusPage({ value: duration, type: 'authed' });
    }
    catch (error) {
        console.error(error);
    }
});
