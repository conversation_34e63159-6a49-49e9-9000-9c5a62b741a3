'use strict';

var axios = require('axios');

const apiKey = process.env.STATUSPAGE_API_KEY;
const pageId = '7f7x3850vx56';
const apiBase = 'https://api.statuspage.io/v1';
const authHeader = { 'Authorization': `OAuth ${apiKey}` };
const options = { method: 'POST', headers: authHeader };
const bbwMappingMetricId = {
    unauthed: '2fzxp71557s5',
    authed: 'b8r6z0xn4ts2'
};
const stagingMappingMetricId = {
    unauthed: '2fzxp71557s5',
    authed: 'b8r6z0xn4ts2'
};
const mappingMetricId = process.env.ENVIRONMENT == "galoy-staging" ? stagingMappingMetricId : bbwMappingMetricId;
async function submitStatusPage({ value, type }) {
    const metricId = mappingMetricId[type];
    const url = `${apiBase}/pages/${pageId}/metrics/${metricId}/data.json`;
    const currentTimestamp = Math.floor(new Date().valueOf() / 1000);
    const data = {
        timestamp: currentTimestamp,
        value,
    };
    const res = await axios.post(url, { data }, options);
    if (res.status === 401) {
        const errorMessage = "Error: Unauthorized. Please check your API key and page ID.";
        console.error(errorMessage);
        throw new Error(errorMessage);
    }
    console.log(`Submitted point with data: ${JSON.stringify(data)}`);
}

exports.submitStatusPage = submitStatusPage;
