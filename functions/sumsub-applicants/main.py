import requests
import hashlib
import hmac
import time
from os import environ
import itertools
import pandas as pd
from google.cloud import bigquery
from functions_framework import cloud_event


class LavaBankCustomers:
	def __init__(self):
		self.client = bigquery.Client(location = 'EU')
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.lava_bank_customers"

	def get_applicant_ids(self):
		try:
			sql_query = f"SELECT DISTINCT id FROM `{self.table_id}`"
			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()
			print(f"Successfully read {len(df)} rows from {self.table_id}")
			return df
		except Exception as e:
			print(f"Could not get user ids from {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			print("job failed")
			return None

class SumsubApplicants:
	def __init__(self):
		self.s = requests.Session()
		self.app_token = environ.get('SUMSUB_APP_TOKEN')
		self.secret_key = environ.get('SUMSUB_SECRET_KEY')
		self.base_url = "https://api.sumsub.com"
		self.request_timeout = 60

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_applicant_from_external_id(self, external_id):
		# https://docs.sumsub.com/reference/get-applicant-data-via-externaluserid
		url = f"{self.base_url}/resources/applicants/-;externalUserId={external_id}/one"
		req = self.sign_request(requests.Request('GET', url))
		response = self.s.send(req, timeout=self.request_timeout)
		return response

	def sign_request(self, request: requests.Request) -> requests.PreparedRequest:
		prepared_request = request.prepare()
		now = int(time.time())
		method = request.method.upper()
		path_url = prepared_request.path_url  # includes encoded query params
		# could be None so we use an empty **byte** string here
		body = b'' if prepared_request.body is None else prepared_request.body
		if type(body) == str:
			body = body.encode('utf-8')
		data_to_sign = str(now).encode('utf-8') + method.encode('utf-8') + path_url.encode('utf-8') + body
		# hmac needs bytes
		signature = hmac.new(
			self.secret_key.encode('utf-8'),
			data_to_sign,
			digestmod=hashlib.sha256
		)
		prepared_request.headers['X-App-Token'] = self.app_token
		prepared_request.headers['X-App-Access-Ts'] = str(now)
		prepared_request.headers['X-App-Access-Sig'] = signature.hexdigest()
		return prepared_request

class BqApplicants:
	def __init__(self, timestamp):
		self.timestamp = timestamp
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"
		self.schema = [
			bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),

			bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("sumsub_id", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("first_name", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("last_name", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("gender", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("dob", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("country", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("nationality", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("tin", "STRING", mode="REQUIRED"),
		]

	def save_applicants(self, applicants_df):
		# date creation with current execution time
		applicants_df['created_at'] = self.timestamp
		table = bigquery.Table(self.table_id, schema=self.schema)
		table.clustering_fields = ["created_at"]
		table = self.client.create_table(table, exists_ok=True)
		job_config = bigquery.LoadJobConfig(schema=self.schema, autodetect=False)
		job_config.schema_update_options = [bigquery.SchemaUpdateOption.ALLOW_FIELD_RELAXATION, bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION]
		job = self.client.load_table_from_dataframe(applicants_df, table, job_config=job_config)
		try:
			job.result()
			print(f"Loaded {applicants_df.shape[0]} {'rows' if applicants_df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {self.table_id}")
		except Exception as e:
			print(f"Could not load {applicants_df.shape[0]} {'rows' if applicants_df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			print("job failed")

@cloud_event
def pollSumsubApplicants(cloud_event):
	################################
	# execution time
	################################
	timestamp = pd.Timestamp.now(tz='UTC')

	################################
	# get lava bank customers
	################################
	lava_bank_customers = LavaBankCustomers()
	customers_df = lava_bank_customers.get_applicant_ids()
	if (customers_df is None) or len(customers_df) <=0:
		print("Error: no lava bank customers data")
		print("job failed")
		return

	################################
	# init bq applicants with execution time
	################################
	bq_applicants = BqApplicants(timestamp)

	################################
	# get lava bank customers sumsub info
	################################
	applicants_rows = []
	applicants = SumsubApplicants()

	for customers_row in customers_df.itertuples():
		external_id = customers_row.id
		try:
			response = applicants.get_applicant_from_external_id(external_id)
			if response.status_code == 404:
				print(f"Could not fetch external_user_id={external_id} from sumsub")
				continue

			response.raise_for_status()

			response = response.json()

			applicant_id = response.get('id')
			row = {}

			for info_key in ['fixedInfo', 'info']:
				nest_dict = response.get(info_key)
				if nest_dict:
					for key in ['firstName', 'lastName', 'gender', 'dob', 'country', 'nationality', 'tin']:
						row[key] = nest_dict.get(key) or row.get(key)

			if row:
				row['id'] = external_id
				row['sumsub_id'] = applicant_id
				if 'firstName' in row: row['first_name'] = row.pop('firstName')
				if 'lastName' in row: row['last_name'] = row.pop('lastName')
				applicants_rows.append(row)

		except Exception as e:
			print(f"Error while fetching applicant_id='{applicant_id}' from sumsub: {e}")

	################################
	# process matches
	################################
	applicants_df = pd.DataFrame(applicants_rows)
	print(f"Found {len(applicants_df)} matches between {len(customers_df)} lava bank customers sumsub applicants information.")

	if len(applicants_df) == 0:
		print("job failed")
		return

	# save data
	bq_applicants.save_applicants(applicants_df)

	print("job done")
