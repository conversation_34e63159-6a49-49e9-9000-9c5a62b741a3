#!/bin/bash

# Deploy to staging environment
echo "Deploying to staging environment..."
gcloud functions deploy galoy-staging-pollCoincapPrice-ZmtkUX \
  --region=us-east1 \
  --source=. \
  --entry-point=pollCoincapPrice \
  --trigger-topic=minutes \
  --runtime=nodejs20 \
  --memory=512MB \
  --env-vars-file=staging-env.yaml \
  --service-account=<EMAIL>

# Wait a bit before deploying to BBW to avoid rate limits
sleep 5

# Deploy to BBW environment
echo "Deploying to BBW environment..."
gcloud functions deploy galoy-bbw-pollCoincapPrice-RTNWQX \
  --region=us-east1 \
  --source=. \
  --entry-point=pollCoincapPrice \
  --trigger-topic=minutes \
  --runtime=nodejs20 \
  --memory=512MB \
  --env-vars-file=bbw-env.yaml \
  --service-account=<EMAIL>

echo "Deployment completed!"
