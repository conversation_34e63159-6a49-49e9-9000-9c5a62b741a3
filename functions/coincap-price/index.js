const functions = require("@google-cloud/functions-framework");
const { BigQuery } = require("@google-cloud/bigquery");
const { SecretManagerServiceClient } = require("@google-cloud/secret-manager");
const axios = require("axios");

// NOTE: Coincap API V2 was deprecated on March 31, 2025
// This function has been updated to use the V3 API
// Documentation: https://docs.coincap.io/
//
// Configuration:
// - Set COINCAP_API_KEY environment variable with your Coincap API key, or
// - Configure the 'coincap-api-key' secret in Secret Manager

// Helper function to sleep for a specified number of milliseconds
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to get API key from environment variable or Secret Manager
async function getApiKey() {
  try {
    // First check if API key is available in environment variables
    if (process.env.COINCAP_API_KEY) {
      console.log("Using API key from environment variable");
      return process.env.COINCAP_API_KEY;
    }

    // If not in env, get the API key from Secret Manager
    console.log(
      "API key not found in environment variables. Attempting to get from Secret Manager"
    );
    const client = new SecretManagerServiceClient();
    const projectId = process.env.PROJECT_ID || "galoy-reporting";

    // Log information about the environment
    try {
      // Log the service account that was set during deployment
      console.log(`Service account (from deployment): <EMAIL>`);

      // Log the project and environment for debugging
      console.log(`Project ID: ${projectId}`);
      console.log(`Environment: ${process.env.ENVIRONMENT || 'Unknown'}`);

      // Log additional information that might be helpful
      console.log(`Function name: ${process.env.FUNCTION_NAME || 'Unknown'}`);
      console.log(`Function region: ${process.env.FUNCTION_REGION || 'Unknown'}`);
    } catch (err) {
      console.warn("Error logging environment information:", err.message);
    }

    // Try with hyphen format (coincap-api-key)
    const secretNameHyphen = `projects/${projectId}/secrets/coincap-api-key/versions/latest`;
    try {
      console.log(`Attempting to access secret: ${secretNameHyphen}`);
      const [version] = await client.accessSecretVersion({ name: secretNameHyphen });
      if (version && version.payload && version.payload.data) {
        const apiKey = version.payload.data.toString("utf8");
        console.log("Successfully retrieved API key from Secret Manager (hyphen format)");

        // Check if the API key contains any problematic characters
        if (/[\x00-\x1F\x7F]/.test(apiKey)) {
          console.warn("Warning: API key contains control characters that might cause issues");
          // Log the hex representation of the API key for debugging
          console.log("API key hex representation:", Buffer.from(apiKey).toString('hex'));
        }

        return apiKey;
      } else {
        console.warn("Retrieved empty API key from Secret Manager (hyphen format)");
      }
    } catch (secretError) {
      console.warn(
        `Error accessing secret ${secretNameHyphen}:`,
        secretError.message
      );

      // Try with underscore format (coincap_api_key) as fallback
      const secretNameUnderscore = `projects/${projectId}/secrets/coincap_api_key/versions/latest`;
      try {
        console.log(`Attempting to access secret with alternative name: ${secretNameUnderscore}`);
        const [altVersion] = await client.accessSecretVersion({
          name: secretNameUnderscore,
        });
        if (altVersion && altVersion.payload && altVersion.payload.data) {
          const apiKey = altVersion.payload.data.toString("utf8");
          console.log("Successfully retrieved API key from Secret Manager (underscore format)");

          // Check if the API key contains any problematic characters
          if (/[\x00-\x1F\x7F]/.test(apiKey)) {
            console.warn("Warning: API key contains control characters that might cause issues");
            // Log the hex representation of the API key for debugging
            console.log("API key hex representation:", Buffer.from(apiKey).toString('hex'));
          }

          return apiKey;
        } else {
          console.warn("Retrieved empty API key from Secret Manager (underscore format)");
        }
      } catch (altError) {
        console.warn(`Error accessing alternative secret ${secretNameUnderscore}:`, altError.message);
      }
    }

    // If we get here, we couldn't get the API key from any source
    console.error("Failed to retrieve API key from any source");
    console.error("Please ensure one of the following:");
    console.error("1. Set the COINCAP_API_KEY environment variable");
    console.error("2. Create a secret named coincap-api-key in Secret Manager");
    console.error("3. Create a secret named coincap_api_key in Secret Manager");
    console.error("4. Ensure the function has permission to access Secret Manager");

    return null;
  } catch (error) {
    console.warn("Failed to get API key:", error.message);
    return null; // Return null if API key is not available
  }
}

// Helper function to create an axios instance with retry logic
async function createAxiosInstance() {
  try {
    const apiKey = await getApiKey();

    if (!apiKey) {
      throw new Error("No API key available for Coincap V3 API");
    }

    // Sanitize the API key to ensure it doesn't contain invalid characters
    const sanitizedApiKey = apiKey.trim();

    // Log the length and first/last few characters for debugging
    console.log(`API key length: ${sanitizedApiKey.length}`);
    if (sanitizedApiKey.length > 8) {
      console.log(`API key prefix: ${sanitizedApiKey.substring(0, 4)}...`);
      console.log(`API key suffix: ...${sanitizedApiKey.substring(sanitizedApiKey.length - 4)}`);
    }

    // For BBW environment, always use the API key as a URL parameter
    // This avoids issues with invalid characters in headers
    const environment = process.env.ENVIRONMENT || 'unknown';
    if (environment === 'bbw') {
      console.log("BBW environment detected - using API key as URL parameter for safety");
      const headers = {
        Accept: "application/json",
        "Accept-Encoding": "gzip",
      };

      return axios.create({
        baseURL: "https://rest.coincap.io",
        timeout: 15000,
        headers,
        params: {
          apiKey: sanitizedApiKey
        }
      });
    }

    // For other environments, check for invalid characters
    if (/[\x00-\x1F\x7F]/.test(sanitizedApiKey)) {
      console.error("API key contains invalid control characters. Attempting to clean...");
      // Remove control characters
      const cleanedApiKey = sanitizedApiKey.replace(/[\x00-\x1F\x7F]/g, '');
      console.log(`Cleaned API key length: ${cleanedApiKey.length}`);

      if (cleanedApiKey !== sanitizedApiKey) {
        console.log("API key was cleaned of invalid characters");
      }

      // Use the cleaned key
      console.log("Creating axios instance with cleaned API key");

      // Create headers object with cleaned API key
      const headers = {
        Accept: "application/json",
        "Accept-Encoding": "gzip",
      };

      // Try using the API key as a URL parameter instead of in the header
      console.log("Using API key as URL parameter instead of Authorization header");
      return axios.create({
        baseURL: "https://rest.coincap.io",
        timeout: 15000,
        headers,
        params: {
          apiKey: cleanedApiKey
        }
      });
    }

    console.log("Creating axios instance for Coincap V3 API");

    // Create headers object with API key
    // V3 API uses Authorization Bearer token
    const headers = {
      Accept: "application/json",
      "Accept-Encoding": "gzip",
      Authorization: `Bearer ${sanitizedApiKey}`, // V3 API uses Bearer token
    };

    // Alternatively, we could use the apiKey as a URL parameter instead of a header

    // V3 API uses rest.coincap.io/ as the base URL (note: endpoints include /v3/)
    const instance = axios.create({
      baseURL: "https://rest.coincap.io", // Correct V3 API URL
      timeout: 15000, // Increased timeout
      headers,
    });

    // Add interceptor to add API key as URL parameter if needed
    instance.interceptors.request.use((config) => {
      // If the request fails with auth issues, we might need to use the URL parameter instead
      // This will be handled in the response interceptor
      return config;
    });

    // Add response interceptor for retry logic
    instance.interceptors.response.use(null, async (error) => {
      const { config, response } = error;

      // If the request was rate limited (429) and we haven't retried too many times
      if (
        response &&
        response.status === 429 &&
        (!config.retryCount || config.retryCount < 3)
      ) {
        config.retryCount = config.retryCount || 0;
        config.retryCount += 1;

        // Calculate backoff time: 2^retry * 1000ms + random jitter
        const backoff =
          Math.pow(2, config.retryCount) * 1000 + Math.random() * 1000;
        console.log(
          `Rate limited. Retrying after ${backoff}ms (attempt ${config.retryCount}/3)`
        );

        await sleep(backoff);
        return instance(config);
      }

      // If authentication failed (401 or 403), try with API key as URL parameter
      if (
        response &&
        (response.status === 401 ||
          response.status === 403 ||
          response.status === 404) &&
        !config.params?.apiKey &&
        !config.urlParamRetry
      ) {
        console.log(
          `Authentication error (${response.status}). Retrying with API key as URL parameter...`
        );

        // Clone the config and add API key as URL parameter
        const newConfig = { ...config };
        newConfig.params = newConfig.params || {};
        newConfig.params.apiKey = apiKey;
        newConfig.urlParamRetry = true; // Mark that we've tried with URL param

        // Remove Authorization header to avoid conflicts
        if (newConfig.headers && newConfig.headers.Authorization) {
          delete newConfig.headers.Authorization;
        }

        console.log("Retrying request with API key as URL parameter");
        return instance(newConfig);
      }

      // Log detailed error information
      if (response) {
        console.error(`API Error: ${response.status} - ${response.statusText}`);
        if (response.data) {
          console.error("Response data:", JSON.stringify(response.data));
        }
      }

      return Promise.reject(error);
    });

    return instance;
  } catch (error) {
    console.error("Error creating axios instance:", error.message);

    // Don't fall back to V2 API - we're fully migrating to V3
    console.error(
      "Failed to create V3 API instance. Cannot proceed without a valid API key."
    );
    console.error(
      "Please set the COINCAP_API_KEY environment variable or configure the coincap-api-key secret in Secret Manager."
    );
    throw new Error(
      "Failed to create V3 API instance. Cannot proceed without a valid API key. See logs for details."
    );
  }
}

// Helper function to fetch historical data from Coincap API with retries
async function fetchHistoricalData(api, startTimestamp, endTimestamp) {
  try {
    // Convert timestamps to milliseconds for Coincap API
    const start = startTimestamp * 1000;
    const end = endTimestamp * 1000;

    console.log(
      `Fetching historical data from ${new Date(
        start
      ).toISOString()} to ${new Date(end).toISOString()}`
    );

    // V3 API endpoint for historical data
    console.log(
      "Using V3 API endpoint for historical data: /v3/assets/bitcoin/history with m5 interval"
    );
    const response = await api.get("/v3/assets/bitcoin/history", {
      params: {
        interval: "m5", // 5-minute intervals
        start,
        end,
      },
    });

    if (
      !response.data ||
      !response.data.data ||
      !Array.isArray(response.data.data)
    ) {
      console.error(
        "Invalid response from Coincap V3 API for historical data:",
        response.data
      );
      return [];
    }

    const historicalData = response.data.data.map((item) => ({
      price_usd: item.priceUsd,
      timestamp: Math.floor(new Date(item.time).getTime() / 1000),
    }));

    if (historicalData.length === 0) {
      console.warn(
        `No historical data returned for period ${new Date(
          start
        ).toISOString()} to ${new Date(end).toISOString()} with m5 interval`
      );
      console.log("Attempting to fetch with hourly interval...");

      // Try with hourly interval if m5 returns no data
      try {
        console.log(
          "Using V3 API endpoint for historical data: /v3/assets/bitcoin/history with h1 interval"
        );
        const hourlyResponse = await api.get("/v3/assets/bitcoin/history", {
          params: {
            interval: "h1", // 1-hour intervals as fallback
            start,
            end,
          },
        });

        if (
          hourlyResponse.data &&
          hourlyResponse.data.data &&
          Array.isArray(hourlyResponse.data.data)
        ) {
          const hourlyData = hourlyResponse.data.data.map((item) => ({
            price_usd: item.priceUsd,
            timestamp: Math.floor(new Date(item.time).getTime() / 1000),
          }));

          if (hourlyData.length > 0) {
            console.log(
              `Retrieved ${hourlyData.length} data points using h1 interval`
            );
            return hourlyData;
          } else {
            console.warn("No historical data returned with h1 interval either");
            console.log("Attempting to fetch with daily interval...");

            // Try with daily interval as a last resort
            console.log(
              "Using V3 API endpoint for historical data: /v3/assets/bitcoin/history with d1 interval"
            );
            const dailyResponse = await api.get("/v3/assets/bitcoin/history", {
              params: {
                interval: "d1", // Daily intervals as last resort
                start,
                end,
              },
            });

            if (
              dailyResponse.data &&
              dailyResponse.data.data &&
              Array.isArray(dailyResponse.data.data)
            ) {
              const dailyData = dailyResponse.data.data.map((item) => ({
                price_usd: item.priceUsd,
                timestamp: Math.floor(new Date(item.time).getTime() / 1000),
              }));

              if (dailyData.length > 0) {
                console.log(
                  `Retrieved ${dailyData.length} data points using d1 interval`
                );
                return dailyData;
              }
            }
          }
        }
      } catch (fallbackError) {
        console.error(
          "Error fetching fallback historical data:",
          fallbackError.message
        );
        if (fallbackError.response) {
          console.error(
            "Fallback response status:",
            fallbackError.response.status
          );
          console.error("Fallback response data:", fallbackError.response.data);
        }
      }
    }

    return historicalData;
  } catch (error) {
    console.error("Error fetching historical data:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
    return [];
  }
}

// Helper function to check for dates with transactions that need price data
async function checkForMissingPriceDates(bigquery, datasetId) {
  try {
    // Query to find dates with transactions but no price data
    const query = `
      WITH transaction_dates AS (
        SELECT DISTINCT DATE(recorded_at) as date
        FROM \`${datasetId}.stg_journal_entries\`
        WHERE DATE(recorded_at) > DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
      ),
      price_dates AS (
        SELECT DISTINCT DATE(timestamp) as date
        FROM \`${datasetId}._functions_raw.coincap_price\`
        WHERE DATE(timestamp) > DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
      )
      SELECT transaction_dates.date
      FROM transaction_dates
      LEFT JOIN price_dates ON transaction_dates.date = price_dates.date
      WHERE price_dates.date IS NULL
      ORDER BY transaction_dates.date
    `;

    console.log(
      "Checking for dates with transactions but missing price data..."
    );
    const [rows] = await bigquery.query(query);

    if (rows && rows.length > 0) {
      console.log(
        `Found ${rows.length} dates with transactions but no price data:`
      );
      const dates = rows.map((row) => row.date.value);
      console.log(dates);
      return dates;
    } else {
      console.log("No dates with transactions are missing price data.");
    }

    return [];
  } catch (error) {
    console.error("Error checking for missing price dates:", error.message);
    return [];
  }
}

// Main function
functions.cloudEvent("pollCoincapPrice", async (_cloudEvent) => {
  const datasetId = process.env.DATASET_ID;
  const tableId = process.env.TABLE_ID;
  const environment = process.env.ENVIRONMENT || 'unknown';
  const functionName = process.env.FUNCTION_NAME || 'unknown';
  const functionRegion = process.env.FUNCTION_REGION || 'unknown';
  const bigquery = new BigQuery();

  console.log(`=== STARTING COINCAP PRICE FUNCTION ===`);
  console.log(`Environment: ${environment}`);
  console.log(`Function name: ${functionName}`);
  console.log(`Function region: ${functionRegion}`);
  console.log(`Dataset ID: ${datasetId}`);
  console.log(`Table ID: ${tableId}`);
  console.log(`Service account: <EMAIL>`);
  console.log(`=== CONFIGURATION COMPLETE ===`);

  // Create axios instance with API key and retry logic
  const api = await createAxiosInstance();

  // Ensure the table exists
  try {
    await bigquery.dataset(datasetId).table(tableId).get();
    console.log(`Table ${tableId} exists.`);
  } catch (e) {
    console.log(`Creating table ${tableId}.`);
    const schema = [
      { name: "timestamp", type: "TIMESTAMP" },
      { name: "price_usd", type: "STRING" },
    ];
    // Create the table and wait for it to be ready
    try {
      const table = bigquery.dataset(datasetId).table(tableId);
      await table.create({ schema, location: "US" });
      console.log(
        `Table ${tableId} created successfully, waiting for it to be ready...`
      );
      await sleep(10000);
    } catch (tableError) {
      console.error("Error creating table:", tableError.message);
      // Continue anyway, as the table might already exist
    }
  }

  try {
    // Get the current price
    console.log("Fetching current price from Coincap API");

    let currentPriceUsd, currentTimestamp;

    // We're only using V3 API now
    console.log("Using V3 API endpoint: /v3/assets/bitcoin");
    const currentPriceRes = await api.get("/v3/assets/bitcoin");

    if (!currentPriceRes.data || !currentPriceRes.data.data) {
      throw new Error("Invalid response from Coincap V3 API for current price");
    }

    // Get price from V3 API response
    currentPriceUsd = currentPriceRes.data.data.priceUsd;

    // V3 API might return timestamp differently
    currentTimestamp = Math.floor(Date.now() / 1000); // Use current time as fallback
    if (currentPriceRes.data.timestamp) {
      currentTimestamp = Math.floor(currentPriceRes.data.timestamp / 1000);
    }

    console.log(
      `Successfully fetched current price from V3 API: $${currentPriceUsd}`
    );

    // Get the most recent timestamp from the table
    const query = `
      SELECT MAX(UNIX_SECONDS(timestamp)) as last_timestamp
      FROM \`${datasetId}.${tableId}\`
    `;

    const [queryResult] = await bigquery.query(query);
    const lastTimestamp = queryResult[0]?.last_timestamp || 0;

    console.log(
      `Last timestamp in database: ${
        lastTimestamp ? new Date(lastTimestamp * 1000).toISOString() : "None"
      }`
    );
    console.log(
      `Current timestamp: ${new Date(currentTimestamp * 1000).toISOString()}`
    );

    // Calculate the gap between the last timestamp and now
    const now = Math.floor(Date.now() / 1000);
    const oneDayInSeconds = 24 * 60 * 60;
    const gapInSeconds = now - lastTimestamp;

    let rows = [
      {
        price_usd: currentPriceUsd,
        timestamp: currentTimestamp,
      },
    ];

    // Option to force a backfill for specific date ranges
    // Set to false to disable, or true to enable
    const forceBackfill = false; // Disabled for now, but can be re-enabled if needed

    // You can also enable it for specific environments by uncommenting the line below:
    // const forceBackfill = environment === 'bbw' || environment === 'staging';

    console.log(`Force backfill is ${forceBackfill ? 'enabled' : 'disabled'} for ${environment} environment`);
    if (forceBackfill) {
      console.log("FORCED BACKFILL: Initiating backfill for missing historical data");

      // Define the date range for April 8-15, 2025
      const startDate = new Date("2025-04-08T00:00:00Z").getTime() / 1000;
      const endDate = new Date("2025-04-15T23:59:59Z").getTime() / 1000;

      console.log(`FORCED BACKFILL: Targeting date range from ${new Date(startDate * 1000).toISOString()} to ${new Date(endDate * 1000).toISOString()}`);

      // Process in daily chunks
      let backfillStartTime = startDate;
      while (backfillStartTime < endDate) {
        const backfillEndTime = Math.min(backfillStartTime + oneDayInSeconds, endDate);
        console.log(`FORCED BACKFILL: Processing from ${new Date(backfillStartTime * 1000).toISOString()} to ${new Date(backfillEndTime * 1000).toISOString()}`);

        // Try to get data for this day
        const historicalData = await fetchHistoricalData(api, backfillStartTime, backfillEndTime);

        if (historicalData.length > 0) {
          console.log(`FORCED BACKFILL: Successfully fetched ${historicalData.length} data points for ${new Date(backfillStartTime * 1000).toISOString().split('T')[0]}`);
          rows = [...rows, ...historicalData];
        } else {
          console.log(`FORCED BACKFILL: No data available for ${new Date(backfillStartTime * 1000).toISOString().split('T')[0]} after trying all intervals`);
        }

        // Move to next day
        backfillStartTime = backfillEndTime;

        // Respect API rate limits
        await sleep(1000);
      }

      console.log("FORCED BACKFILL: Completed forced backfill process");
    }

    // If there's a gap of more than 1 hour, fetch historical data
    if (gapInSeconds > 3600) {
      console.log(
        `Gap detected: ${gapInSeconds} seconds. Fetching historical data...`
      );

      // Determine the appropriate start time for backfilling
      // For gaps less than 30 days, fetch the entire gap
      // For larger gaps, fetch at least the last 30 days plus any specific dates with transactions
      const maxBackfillDays = 30; // Increased from 7 to 30 days
      let startTime;

      if (gapInSeconds < maxBackfillDays * oneDayInSeconds) {
        // For smaller gaps, fetch the entire gap
        startTime = lastTimestamp;
        console.log(
          `Gap is less than ${maxBackfillDays} days, fetching the entire gap from ${new Date(
            startTime * 1000
          ).toISOString()}`
        );
      } else {
        // For larger gaps, start from 30 days ago
        startTime = now - maxBackfillDays * oneDayInSeconds;
        console.log(
          `Large gap detected (${gapInSeconds} seconds). Starting backfill from ${new Date(
            startTime * 1000
          ).toISOString()}`
        );

        // Check for specific dates with transactions that need price data
        try {
          const missingDates = await checkForMissingPriceDates(
            bigquery,
            datasetId
          );

          if (missingDates && missingDates.length > 0) {
            console.log(
              `Will ensure data is fetched for these specific dates with transactions: ${missingDates.join(
                ", "
              )}`
            );

            // For each missing date, we'll fetch data separately
            for (const dateStr of missingDates) {
              const date = new Date(dateStr);
              const dateStart = Math.floor(date.getTime() / 1000);
              const dateEnd = dateStart + oneDayInSeconds;

              // Only process dates that are before our current backfill start
              if (dateStart < startTime) {
                console.log(
                  `Fetching data for specific date with transactions: ${dateStr}`
                );
                const dateData = await fetchHistoricalData(
                  api,
                  dateStart,
                  dateEnd
                );

                if (dateData.length > 0) {
                  console.log(
                    `Fetched ${dateData.length} data points for ${dateStr}`
                  );
                  rows = [...rows, ...dateData];
                } else {
                  console.log(
                    `No data available for ${dateStr} after trying all intervals`
                  );
                }

                // Respect API rate limits
                await sleep(500);
              }
            }
          }
        } catch (error) {
          console.error("Error processing missing dates:", error.message);
        }
      }

      // Now fetch the main backfill data
      const endTime = now;
      let currentStartTime = startTime;

      // Fetch historical data in chunks of 1 day (Coincap API limitation)
      while (currentStartTime < endTime) {
        const chunkEndTime = Math.min(
          currentStartTime + oneDayInSeconds,
          endTime
        );
        const historicalData = await fetchHistoricalData(
          api,
          currentStartTime,
          chunkEndTime
        );

        if (historicalData.length > 0) {
          console.log(
            `Fetched ${
              historicalData.length
            } historical data points from ${new Date(
              currentStartTime * 1000
            ).toISOString()} to ${new Date(chunkEndTime * 1000).toISOString()}`
          );
          rows = [...rows, ...historicalData];
        } else {
          console.log(
            `No historical data available for the period ${new Date(
              currentStartTime * 1000
            ).toISOString()} to ${new Date(
              chunkEndTime * 1000
            ).toISOString()} after trying all intervals`
          );
        }

        // Move to the next chunk
        currentStartTime = chunkEndTime;

        // Respect Coincap API rate limits
        await sleep(500);
      }
    }

    // Remove duplicates based on timestamp
    const uniqueRows = Array.from(
      new Map(rows.map((row) => [row.timestamp, row])).values()
    );

    if (uniqueRows.length === 0) {
      console.log("No data to upload to BigQuery");
      return;
    }

    // Sort rows by timestamp for better logging and debugging
    uniqueRows.sort((a, b) => a.timestamp - b.timestamp);

    const oldestTimestamp = new Date(
      uniqueRows[0].timestamp * 1000
    ).toISOString();
    const newestTimestamp = new Date(
      uniqueRows[uniqueRows.length - 1].timestamp * 1000
    ).toISOString();

    console.log(
      `Uploading ${uniqueRows.length} rows to BigQuery (date range: ${oldestTimestamp} to ${newestTimestamp})`
    );

    // Log a summary of the data being uploaded
    const dateCounts = {};
    uniqueRows.forEach((row) => {
      const dateStr = new Date(row.timestamp * 1000)
        .toISOString()
        .split("T")[0];
      dateCounts[dateStr] = (dateCounts[dateStr] || 0) + 1;
    });

    console.log("Data points per date:");
    Object.entries(dateCounts)
      .sort()
      .forEach(([date, count]) => {
        console.log(`  ${date}: ${count} data points`);
      });

    // Insert data in batches to avoid exceeding BigQuery limits
    const batchSize = 500; // Smaller batch size to be safer
    let successfulInserts = 0;
    let failedInserts = 0;

    for (let i = 0; i < uniqueRows.length; i += batchSize) {
      const batch = uniqueRows.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(uniqueRows.length / batchSize);

      try {
        console.log(
          `Inserting batch ${batchNumber} of ${totalBatches} (${batch.length} rows)...`
        );

        // Get the date range for this batch for better logging
        const batchStartDate = new Date(batch[0].timestamp * 1000)
          .toISOString()
          .split("T")[0];
        const batchEndDate = new Date(batch[batch.length - 1].timestamp * 1000)
          .toISOString()
          .split("T")[0];
        console.log(`  Batch date range: ${batchStartDate} to ${batchEndDate}`);

        await bigquery.dataset(datasetId).table(tableId).insert(batch);

        console.log(
          `Successfully inserted batch ${batchNumber} of ${totalBatches}`
        );
        successfulInserts += batch.length;
      } catch (error) {
        if (error.name === "PartialFailureError") {
          console.warn(
            `Partial failure when inserting batch ${batchNumber}:`,
            error.errors
          );

          // Count successful rows in partial failure
          const failedRowIndices = new Set();
          error.errors.forEach((err) => {
            if (err.row) {
              failedRowIndices.add(err.row);
            }
          });

          const successfulRowsInBatch = batch.length - failedRowIndices.size;
          successfulInserts += successfulRowsInBatch;
          failedInserts += failedRowIndices.size;

          console.log(
            `  ${successfulRowsInBatch} rows succeeded, ${failedRowIndices.size} rows failed in this batch`
          );
        } else {
          console.error(
            `Failed to insert batch ${batchNumber}:`,
            error.message
          );
          failedInserts += batch.length;

          // Don't throw the error, try to continue with the next batch
          // This is more resilient than failing the entire function
        }
      }

      // Add a small delay between batches
      if (i + batchSize < uniqueRows.length) {
        await sleep(1000);
      }
    }

    // Log summary of insertion results
    console.log(
      `Insertion summary: ${successfulInserts} rows inserted successfully, ${failedInserts} rows failed`
    );

    if (successfulInserts === 0 && failedInserts > 0) {
      throw new Error("All insertions failed. Check logs for details.");
    }

    console.log("Data upload completed successfully");
  } catch (error) {
    console.error("Error in main function:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
    throw error; // Re-throw to mark the function as failed
  }
});
