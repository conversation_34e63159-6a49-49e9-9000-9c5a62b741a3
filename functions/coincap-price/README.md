# Command used to fix and test the coincap function

Check out the backfill function in the script to fill historical gaps.

```
# test coincap API
COINCAP_API_KEY=<key>
curl -s -H "X-API-Key: $COINCAP_API_KEY" https://api.coincap.io/v3/prices/bitcoin/latest | jq

# check table for last entry on staging
bq query --nouse_legacy_sql "SELECT MAX(timestamp) as latest_timestamp FROM \`galoy-reporting.galoy_staging_functions_raw.coincap_price\`"

# list functions
gcloud functions list

# check env vars for function
gcloud functions describe galoy-staging-pollCoincapPrice-ZmtkUX \
  --region=us-east1 \
  --format="value(environmentVariables)"

# set env var for function:
gcloud functions deploy galoy-staging-pollCoincapPrice-ZmtkUX \
  --region=us-east1 \
  --update-env-vars DATASET_ID=galoy_staging_functions_raw,TABLE_ID=coincap_price

# give secrets permission
gcloud projects add-iam-policy-binding galoy-reporting \  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

# logs staging
gcloud functions logs read galoy-staging-pollCoincapPrice-ZmtkUX --region=us-east1 --project=galoy-reporting --limit=10

# deploy staging
gcloud functions deploy galoy-staging-pollCoincapPrice-ZmtkUX \
  --region=us-east1 \
  --source=. \
  --entry-point=pollCoincapPrice \
  --trigger-topic=minutes \
  --runtime=nodejs20 \
  --memory=512MB \
  --env-vars-file=staging-env.yaml \
  --service-account=<EMAIL>

# deploy bbw
gcloud functions deploy galoy-bbw-pollCoincapPrice-RTNWQX \  --region=us-east1 \
  --source=. \
  --entry-point=pollCoincapPrice \
  --trigger-topic=minutes \
  --runtime=nodejs20 \
  --memory=512MB \
  --env-vars-file=bbw-env.yaml \
  --service-account=<EMAIL>

# logs bbw
gcloud functions logs read galoy-bbw-pollCoincapPrice-RTNWQX --region=us-east1 --project=galoy-reporting --limit=10
```