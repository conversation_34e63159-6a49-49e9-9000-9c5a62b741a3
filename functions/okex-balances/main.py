#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os
import pandas as pd
import numpy as np

from decimal import Decimal

import ccxt

def fetch_build_balances_df(exchange, since=0):
    #
    # get trading balance
    #
    trading_balances = exchange.fetch_balance(params={'ccy':'BTC'})

    # convert nested list of dictionaries to pandas DataFrame
    trading_df = pd.json_normalize(trading_balances["info"]["data"][0]["details"][0])
    trading_df = trading_df.loc[:, ['uTime', 'availBal', 'frozenBal', 'eq', 'notionalLever', 'upl']]
    trading_df.rename(columns={
        'uTime':'timestamp',
        'availBal': 'trading_btc_free_balance',
        'frozenBal': 'trading_btc_used_balance',
        'eq': 'trading_btc_total_balance',
        'upl': 'unrealized_profit_and_loss',
        'notionalLever': 'notional_lever'
        }, inplace=True)

    # replace timestamp with current time
    trading_df['timestamp'] = np.uint64(pd.Timestamp.now(tz='UTC').timestamp() * 1000)

    # convert object types to required types (uint64, float, decimal, string)
    for c in ['trading_btc_free_balance', 'trading_btc_used_balance', 'trading_btc_total_balance', 'unrealized_profit_and_loss', 'notional_lever']:
        trading_df[c] = trading_df[c].apply(lambda x: Decimal(x) if x else None)

    #
    # get funding balance
    #
    funding_balances = exchange.fetch_balance(params={'ccy':'BTC', 'instType':'funding'})

    # convert nested list of dictionaries to pandas DataFrame
    funding_df = pd.json_normalize(funding_balances["info"]["data"][0])
    funding_df = funding_df.loc[:, ['availBal', 'frozenBal', 'bal']]
    funding_df.rename(columns={
        'availBal': 'funding_btc_free_balance',
        'frozenBal': 'funding_btc_used_balance',
        'bal': 'funding_btc_total_balance',
        }, inplace=True)

    # convert object types to required types (uint64, float, decimal, string)
    for c in ['funding_btc_free_balance', 'funding_btc_used_balance', 'funding_btc_total_balance']:
        funding_df[c] = funding_df[c].apply(lambda x: Decimal(x) if x else None)

    # merge trading and funding balances
    balances_df = pd.concat([trading_df, funding_df], axis=1)

    balances_df.set_index('timestamp', inplace=True)

    return balances_df

@functions_framework.cloud_event
def pollOKXBalances(cloud_event):
    """https://www.okx.com/docs-v5/en/#rest-api-account-get-balance"""
    apikey = os.environ.get('OKEX_API_KEY')
    secretkey = os.environ.get('OKEX_SECRET_KEY')
    passphrase = os.environ.get('OKEX_PASSPHRASE')
    environment = os.environ.get('ENVIRONMENT')
    okx = ccxt.okex5({
        'apiKey':  apikey,
        'secret':  secretkey,
        'password':  passphrase,
    })

    if environment and 'staging' in environment.lower():
        okx.set_sandbox_mode(True)
    else:
        okx.set_sandbox_mode(False)

    client = bigquery.Client()
    table_id =  f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("trading_btc_free_balance", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("trading_btc_used_balance", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("trading_btc_total_balance", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("unrealized_profit_and_loss", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("notional_lever", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("funding_btc_free_balance", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("funding_btc_used_balance", "BIGNUMERIC", mode="NULLABLE"),
        bigquery.SchemaField("funding_btc_total_balance", "BIGNUMERIC", mode="NULLABLE"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")
    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

    df = fetch_build_balances_df(exchange=okx)

    if len(df) > 0:

        job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
        job_config.schema_update_options = [bigquery.SchemaUpdateOption.ALLOW_FIELD_RELAXATION, bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION]
        job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()

        table = client.get_table(table_id)  # Make an API request
        print(f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id}")

    print("job done")
