#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os


@functions_framework.cloud_event
def pollOkxTradingAccountHistory(cloud_event):
    client = bigquery.Client()
    table_id = f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("id", "INTEGER", mode="REQUIRED"),
        bigquery.SchemaField("order_id", "INTEGER", mode="REQUIRED"),
        bigquery.SchemaField("time", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("trade_type", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("instrument", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("action", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("amount", "INTEGER", mode="REQUIRED"),
        bigquery.SchemaField("trading_unit", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("fill_price", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("pnl", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("fee", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("position_change", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("position_balance", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("balance_change", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("balance", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("balance_unit", "STRING", mode="REQUIRED"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")

    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(
            f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

    print("job done")
