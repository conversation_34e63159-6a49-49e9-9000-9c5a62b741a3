import requests
from os import environ
import itertools
import time
import pandas as pd
import numpy as np
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
from functions_framework import cloud_event
from fuzzywuzzy import fuzz

from pympler import asizeof

class Applicants:
	def __init__(self):
		self.s = requests.Session()
		api_key = environ.get('ONFIDO_API_KEY')
		self.s.headers.update({'Authorization': f'Token token={api_key}'})

	def __enter__(self):
		self.s.__enter__()
		return self

	def __exit__(self, exc_type, exc_value, traceback):
		self.s.__exit__(exc_type, exc_value, traceback)

	def get_page(self, page):
		time.sleep(1)
		r = self.s.get(
			'https://api.eu.onfido.com/v3.6/applicants',
			params={'page': page, 'per_page': 500, 'include_deleted': False},
		)
		data = r.json()
		if r.status_code == requests.codes.ok and 'applicants' in data:
			print(f"Applicants.get_page({page}): Successfully read {len(data['applicants'])} onfido applicants")
			return data['applicants']
		else:
			print(f"Response status code: {r.status_code}")
			print(f"Applicants.get_page({page}): No 'applicants' key in data --> {data}")
			return []

	def get_iter(self):
		"Lazily get all applicants runs handling paging correctly."
		return itertools.chain.from_iterable(
			itertools.takewhile(
				lambda x: len(x) > 0,
				(self.get_page(i) for i in itertools.count(1))
			)
		)

class WatchlistProgress:
	def __init__(self):
		self.progress = {
			"watchlist_created_at": None,
			"watchlist_offset": 0
		}
		self.client = bigquery.Client()
		self.watchlist_progress_table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.watchlist_progress"
		self.progress_schema = [
			bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
			bigquery.SchemaField("watchlist_created_at", "TIMESTAMP", mode="REQUIRED"),
			bigquery.SchemaField("watchlist_offset", "INT64", mode="REQUIRED"),
		]

	def get_progress(self, asof_date):
		try:
			sql_query = f"SELECT * FROM `{self.watchlist_progress_table_id}` WHERE created_at >= '{asof_date.date()}' AND created_at = (SELECT MAX(created_at) FROM `{self.watchlist_progress_table_id}`) LIMIT 1"
			job = self.client.query(sql_query)
			results = job.result()
			offset_df = results.to_dataframe()
			print(f"Successfully read offset {offset_df.head()} rows from {self.watchlist_progress_table_id} as of date='{asof_date}'")
			if len(offset_df) > 0:
				self.progress["watchlist_created_at"] = offset_df["watchlist_created_at"][0]
				self.progress["watchlist_offset"] = offset_df["watchlist_offset"][0]
		except Exception as e:
			print(f"Could not get watchlist offset data from {self.watchlist_progress_table_id}, starting from 0")
			print(f"Exception: {e}")
			print(job.errors)

	def save_progress(self, created_at, offset):
		offset_df = pd.DataFrame([{
			'created_at': pd.Timestamp.now(tz='UTC'),
			'watchlist_created_at': created_at,
			'watchlist_offset': self.progress["watchlist_offset"] + offset,
		}])

		table = bigquery.Table(self.watchlist_progress_table_id, schema=self.progress_schema)
		table.clustering_fields = ["created_at"]
		table = self.client.create_table(table, exists_ok=True)
		job_config = bigquery.LoadJobConfig(schema=self.progress_schema, autodetect=False)
		job = self.client.load_table_from_dataframe(offset_df, table, job_config=job_config)
		try:
			job.result()
			print(f"Saved watchlist progress offset {offset} to {self.watchlist_progress_table_id}")
		except Exception as e:
			print(f"Could not save watchlist progress offset {offset} to {self.watchlist_progress_table_id}")
			print(f"Exception: {e}")
			print(job.errors)

class FalsePositive:
	def __init__(self):
		########### FALSE_POSITIVE ###########
		# Overview: public google sheet
		# Sheet Name: galoy google spreadsheet on FALSE_POSITIVE tab
		############################
		sheet_id = '1bpfiRvlD9F68JrGmPCNa3N_hl1oF3_my'
		sheet_name = 'FALSE_POSITIVE'
		self.fp_sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/gviz/tq?tqx=out:csv&sheet={sheet_name}"

	def get_false_positive_df(self):
		fp_sheet_df = pd.DataFrame()
		try:
			fp_sheet_df = pd.read_csv(self.fp_sheet_url)
			print(f"Successfully read {len(fp_sheet_df)} known false positive")
		except Exception as e:
			print(f"Error while fetching {self.fp_sheet_url} file: {e}")
			return fp_sheet_df

		fp_df = fp_sheet_df.loc[:, [
			'applicant_id',
			'watchlist_id',
			'watchlist',
		]]

		fp_df['watchlist_id'] = fp_df['watchlist_id'].map(np.int64)

		for col in ['applicant_id', 'watchlist']:
			fp_df[col] = fp_df[col].apply(lambda x: str(x) if x else '')

		return fp_df

class Watchlist:
	def __init__(self):
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.watchlist"
		self.schema = [
			bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
			bigquery.SchemaField("applicant_id", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("watchlist_id", "INT64", mode="REQUIRED"),
			bigquery.SchemaField("match_score_percent", "INT64", mode="REQUIRED"),
			bigquery.SchemaField("watchlist", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("watchlist_created_at", "TIMESTAMP", mode="REQUIRED"),
		]

	def get_watchlist_df(self, created_at, offset):
		try:
			sql_query = f'SELECT * FROM `{self.table_id}` WHERE created_at = (SELECT MAX(created_at) FROM `{self.table_id}`) ORDER BY watchlist, watchlist_id'
			if created_at:
				sql_query = f"SELECT * FROM `{self.table_id}` WHERE created_at = '{created_at}' ORDER BY watchlist, watchlist_id LIMIT 100000 OFFSET {offset}"
			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()
			df.rename(columns={'created_at': 'watchlist_created_at'}, inplace=True)
			if len(df) <= 0:
				if created_at:
					print(f"No watchlist data at offset {offset}")
				else:
					print(f"No watchlist data from query='{sql_query}'")
				return None
			df['complete_name'] = df[['first_name', 'second_name', 'third_name', 'fourth_name']].apply(lambda x: ' '.join(x), axis=1)
			df['complete_name'] = df['complete_name'].apply(lambda x: x.strip() if isinstance(x, str) else x)
			print(f"Successfully read {len(df)} rows from {self.table_id} as of date='{created_at}' and offset={offset} (size={asizeof.asizeof(df)})")
			return df
		except Exception as e:
			print(f"Could not get watchlist data from {self.watchlist_table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			return None


class WatchlistHits:
	def __init__(self, timestamp):
		self.timestamp = timestamp
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"
		self.schema = [
			bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
			bigquery.SchemaField("applicant_id", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("watchlist_id", "INT64", mode="REQUIRED"),
			bigquery.SchemaField("match_score_percent", "INT64", mode="REQUIRED"),
			bigquery.SchemaField("watchlist", "STRING", mode="REQUIRED"),
			bigquery.SchemaField("watchlist_created_at", "TIMESTAMP", mode="REQUIRED"),
		]

	def save_hits(self, watchlist_hits_df):
		# Record a stat entry when no match
		if len(watchlist_hits_df) <= 0:
			no_match_stats = {
				'applicant_id': [f'No matches'],
				'watchlist_id': [0],
				'match_score_percent': [0],
				'watchlist': [''],
				'watchlist_created_at': [self.timestamp]
			}
			watchlist_hits_df = pd.DataFrame(data=no_match_stats)
		# date creation with current execution time
		watchlist_hits_df['created_at'] = self.timestamp
		table = bigquery.Table(self.table_id, schema=self.schema)
		table.clustering_fields = ["created_at"]
		table = self.client.create_table(table, exists_ok=True)
		job_config = bigquery.LoadJobConfig(schema=self.schema, autodetect=False)
		job = self.client.load_table_from_dataframe(watchlist_hits_df, table, job_config=job_config)
		try:
			job.result()
			print(f"Loaded {watchlist_hits_df.shape[0]} {'rows' if watchlist_hits_df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {self.table_id}")
		except Exception as e:
			print(f"Could not load {watchlist_hits_df.shape[0]} {'rows' if watchlist_hits_df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)

class WorkflowRuns:
	def __init__(self):
		self.client = bigquery.Client()
		self.table_id = f"{self.client.project}.{environ.get('DATASET_ID')}.onfido_workflow_runs"

	def get_applicant_ids(self):
		try:
			sql_query = f"SELECT DISTINCT applicant_id FROM `{self.table_id}` WHERE status IN ('approved', 'declined')"
			job = self.client.query(sql_query)
			results = job.result()
			df = results.to_dataframe()
			print(f"Successfully read {len(df)} ('approved' & 'declined') rows from {self.table_id} (size={asizeof.asizeof(df)})")
			return df
		except Exception as e:
			print(f"Could not get ('approved' & 'declined') applicants from {self.table_id}")
			print(f"Exception: {e}")
			print(job.errors)
			print("job failed")
			return None

@cloud_event
def pollWatchlistCompare(cloud_event):
	################################
	# execution time
	################################
	timestamp = pd.Timestamp.now(tz='UTC')

	################################
	# get watchlist progress offset
	################################
	watchlist_progress = WatchlistProgress()
	watchlist_progress.get_progress(timestamp)
	watchlist_created_at = watchlist_progress.progress["watchlist_created_at"]
	watchlist_offset = watchlist_progress.progress["watchlist_offset"]

	################################
	# get combined watchlist
	################################
	watchlist = Watchlist()
	watchlist_df = watchlist.get_watchlist_df(watchlist_created_at, watchlist_offset)
	if (watchlist_df is None) or len(watchlist_df) <=0:
		print("job failed")
		return

	################################
	# get id for names to validate
	################################
	workflow_runs = WorkflowRuns()
	applicants_df = workflow_runs.get_applicant_ids()
	if (applicants_df is None) or len(applicants_df) <=0:
		print("job failed")
		return

	################################
	# get names to validate
	################################
	onfido_df = pd.DataFrame.from_dict(Applicants().get_iter())
	print(f"Successfully read {len(onfido_df)} applicants rows from OnFido (size={asizeof.asizeof(onfido_df)})")

	if len(onfido_df) <= 0:
		print("No OnFido applicants were retrieved")
		print("job failed")
		return

	onfido_df = onfido_df.loc[:, [
		'id',
		'first_name',
		'last_name',
	]]
	onfido_df.rename(columns={
		'id': 'applicant_id',
	}, inplace=True)

	for col in ['first_name', 'last_name']:
		onfido_df[col] = onfido_df[col].apply(lambda x: str(x) if x else '')

	onfido_df['complete_name'] = onfido_df[['first_name', 'last_name']].apply(lambda x: ' '.join(x), axis=1)
	onfido_df['complete_name'] = onfido_df['complete_name'].apply(lambda x: x.strip() if isinstance(x, str) else x)

	################################
	# filter onfido name list down to 'approved', 'declined' only
	################################
	onfido_df = applicants_df.merge(onfido_df, on='applicant_id')
	print(f"Trimmed down OnFido applicants to {len(onfido_df)} ('approved' & 'declined') rows")

	################################
	# get list of false positive
	################################
	false_positive = FalsePositive()
	fp_df = false_positive.get_false_positive_df()

	################################
	# init watchlist with execution time
	################################
	watchlist_hits = WatchlistHits(timestamp)

	################################
	# MATCHING ON FUZZ itertuples()
	################################
	# Measure time for itertuples()
	start_time = time.time()
	watchlist_hits_rows = []
	watchlist_iter = 0
	for watchlist_row in watchlist_df.itertuples():
		for onfido_row in onfido_df.itertuples():
			score = fuzz.token_sort_ratio(onfido_row.complete_name, watchlist_row.complete_name)
			if score >= 90:
				watchlist_hits_rows.append({
					'applicant_id': onfido_row.applicant_id,
					'watchlist_id': watchlist_row.watchlist_id,
					'match_score_percent': score,
					'watchlist': watchlist_row.watchlist,
					'watchlist_created_at': watchlist_row.watchlist_created_at
				})
		watchlist_iter += 1
		if watchlist_iter % 2000 == 0 and watchlist_iter != 0:
			print(f"Found {len(watchlist_hits_rows)} matches between {len(onfido_df)} applicants and {watchlist_iter}/{len(watchlist_df)} watchlist entries")
			watchlist_hits_df = pd.DataFrame(watchlist_hits_rows)
			# Remove known false positives
			if len(watchlist_hits_df) > 0:
				print(f"Removing {len(fp_df)} known false positives from {len(watchlist_hits_df)} matches")
				duplicates = pd.merge(watchlist_hits_df, fp_df, how="outer", on=["applicant_id", "watchlist_id", "watchlist"], indicator=True)
				watchlist_hits_df = duplicates.loc[duplicates["_merge"] == "left_only"].drop("_merge", axis=1)
				print(f"Uploading {len(watchlist_hits_df)} remaining matches")
			# save hits
			watchlist_hits.save_hits(watchlist_hits_df)
			# record progress
			watchlist_progress.save_progress(watchlist_row.watchlist_created_at, watchlist_iter)
			# reset hits accumulator
			watchlist_hits_rows = []
			print(f"Elapsed time: {time.time() - start_time:.6f} seconds")

	################################
	# process remaining matches
	################################
	watchlist_hits_df = pd.DataFrame(watchlist_hits_rows)
	print(f"Found {len(watchlist_hits_df)} matches between {len(onfido_df)} applicants and {len(watchlist_df)} watchlist entries")
	# Remove known false positives
	if len(watchlist_hits_df) > 0:
		print(f"Removing {len(fp_df)} known false positives from {len(watchlist_hits_df)} matches")
		duplicates = pd.merge(watchlist_hits_df, fp_df, how="outer", on=["applicant_id", "watchlist_id", "watchlist"], indicator=True)
		watchlist_hits_df = duplicates.loc[duplicates["_merge"] == "left_only"].drop("_merge", axis=1)
		print(f"Uploading {len(watchlist_hits_df)} remaining matches")
	# save hits
	watchlist_hits.save_hits(watchlist_hits_df)
	# record progress
	watchlist_progress.save_progress(watchlist_row.watchlist_created_at, len(watchlist_df))

	print("job done")
