resource "google_secret_manager_secret" "okex_api_key" {
  provider = google-beta
  project  = local.project

  secret_id = local.okex_api_key_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "okex_api_key" {
  secret      = google_secret_manager_secret.okex_api_key.id
  secret_data = var.okex_api_key
}

resource "google_secret_manager_secret" "okex_passphrase" {
  provider = google-beta
  project  = local.project

  secret_id = local.okex_passphrase_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "okex_passphrase" {
  secret      = google_secret_manager_secret.okex_passphrase.id
  secret_data = var.okex_passphrase
}

resource "google_secret_manager_secret" "okex_secret_key" {
  provider = google-beta
  project  = local.project

  secret_id = local.okex_secret_key_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "okex_secret_key" {
  secret      = google_secret_manager_secret.okex_secret_key.id
  secret_data = var.okex_secret_key
}

resource "google_secret_manager_secret" "onfido_api_key" {
  provider = google-beta
  project  = local.project

  secret_id = local.onfido_api_key_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "onfido_api_key" {
  secret      = google_secret_manager_secret.onfido_api_key.id
  secret_data = var.onfido_api_key
}

resource "google_secret_manager_secret" "twilio_account" {
  provider = google-beta
  project  = local.project

  secret_id = local.twilio_account_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "twilio_account" {
  secret      = google_secret_manager_secret.twilio_account.id
  secret_data = var.twilio_account
}

resource "google_secret_manager_secret" "twilio_account_sid" {
  provider = google-beta
  project  = local.project

  secret_id = local.twilio_account_sid_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "twilio_account_sid" {
  secret      = google_secret_manager_secret.twilio_account_sid.id
  secret_data = var.twilio_account_sid
}

resource "google_secret_manager_secret" "twilio_auth_token" {
  provider = google-beta
  project  = local.project

  secret_id = local.twilio_auth_token_id
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "twilio_auth_token" {
  secret      = google_secret_manager_secret.twilio_auth_token.id
  secret_data = var.twilio_auth_token
}
