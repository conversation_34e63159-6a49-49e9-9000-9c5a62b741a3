#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os
import pandas as pd

from decimal import Decimal
from datetime import datetime

import ccxt
import numpy as np


def todate(ts):
    return datetime.utcfromtimestamp(int(ts)/1000).isoformat()


def fetch_build_bills_df(exchange, since=0):
    # get last 3 months bills from exchange
    bills = exchange.private_get_account_bills_archive(params={'begin': since})

    # convert nested list of dictionaries to pandas DataFrame
    df = pd.json_normalize(bills["data"])
    df = df.loc[:, [
        'ts',
        'billId',
        'type',
        'subType',
        'bal',
        'balChg',
        'fee',
        'execType',
        'ordId',
        'pnl',
        'sz',
        'px',
        'mgnMode',
        'instType',
        'instId',
        'ccy',
    ]]
    df.rename(columns={
        'ts': 'timestamp',
        'billId': 'bill_id',
        'type': 'type_code',
        'subType': 'sub_type_code',
        'bal': 'balance',
        'balChg': 'balance_change',
        'fee': 'fee',
        'execType': 'execution_type',
        'ordId': 'order_id',
        'pnl': 'pnl',
        'sz': 'size',
        'px': 'price',
        'mgnMode': 'margin_mode',
        'instType': 'instrument_type',
        'instId': 'instrument_id',
        'ccy': 'currency',
    }, inplace=True)

    # convert object types to required types (uint64, float, decimal, string)
    df['timestamp'] = df.timestamp.astype(np.uint64)
    df['bill_id'] = df.bill_id.astype(np.uint64)
    df['type_code'] = df.type_code.astype(np.uint64)
    df['sub_type_code'] = df.sub_type_code.astype(np.uint64)

    for c in ['order_id']:
        df[c] = df[c].apply(lambda x: np.uint64(x) if x else None)

    for c in ['balance', 'balance_change', 'fee', 'pnl', 'size', 'price']:
        df[c] = df[c].apply(lambda x: Decimal(x) if x else None)

    df.set_index('timestamp', inplace=True)

    return df


@functions_framework.cloud_event
def pollOKXBillsArchive(cloud_event):
    """https://www.okx.com/docs-v5/en/#rest-api-account-get-bills-details-last-3-months"""
    apikey = os.environ.get('OKEX_API_KEY')
    secretkey = os.environ.get('OKEX_SECRET_KEY')
    passphrase = os.environ.get('OKEX_PASSPHRASE')
    okx = ccxt.okex5({
        'apiKey':  apikey,
        'secret':  secretkey,
        'password':  passphrase,
    })

    if 'staging' in os.environ.get('ENVIRONMENT').lower():
        okx.set_sandbox_mode(True)
    else:
        okx.set_sandbox_mode(False)

    client = bigquery.Client()
    table_id = f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("bill_id", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("type_code", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("sub_type_code", "INT64", mode="REQUIRED"),
        bigquery.SchemaField("balance", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("balance_change", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("fee", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("execution_type", "STRING", mode="NULLABLE"),
        bigquery.SchemaField("order_id", "INT64", mode="NULLABLE"),
        bigquery.SchemaField("pnl", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("size", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("price", "BIGNUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("margin_mode", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("instrument_type", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("instrument_id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("currency", "STRING", mode="REQUIRED"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")

        sql_query = f"SELECT bill_id, timestamp FROM `{table_id}` ORDER BY timestamp DESC LIMIT 1"
        query_job = client.query(sql_query)
        extable = query_job.result()

        if extable.total_rows == 0:
            remote_timestamp = 0
            remote_bill_id = 0
            print("Empty table? No entry found")
        else:
            for r in extable:
                remote_bill_id = np.uint64(r['bill_id'])
                remote_timestamp = r['timestamp']
            print(
                f"Most recent entry in cloud table: {todate(remote_timestamp)}")

        df = fetch_build_bills_df(exchange=okx, since=remote_timestamp).query(
            "bill_id>@remote_bill_id").drop_duplicates()

    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(
            f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

        df = fetch_build_bills_df(exchange=okx)

    if len(df) > 0:
        job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
        job = client.load_table_from_dataframe(
            df, table_id, job_config=job_config)
        job.result()

        table = client.get_table(table_id)  # Make an API request
        print(
            f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id}")

    print("job done")
