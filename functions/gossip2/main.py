import requests
from struct import unpack
from io import BytesIO
from base64 import b64encode
from google.cloud import bigquery
from os import environ
from functions_framework import cloud_event
from time import time

GOSSIP_PREFIX = b'GSP\x01'

def varint_decode(r):
	"""Decode an integer from reader `r`
	"""
	raw = r.read(1)
	if len(raw) != 1:
		return None

	i, = unpack("!B", raw)
	if i < 0xFD:
		return i
	elif i == 0xFD:
		return unpack("!H", r.read(2))[0]
	elif i == 0xFE:
		return unpack("!L", r.read(4))[0]
	else:
		return unpack("!Q", r.read(8))[0]

def parse_byte_stream(timestamp, b):
	prefix = b.read(4)
	if prefix != GOSSIP_PREFIX:
		raise ValueError("Invalid gossip prefix")

	messages = []
	while True:
		length = varint_decode(b)
		if not length:
			break
		msg = b.read(length)
		if len(msg) != length:
			raise ValueError("Invalid binary")
		(msgtype,) = unpack("!H", msg[:2])
		messages.append({
			"timestamp": timestamp,
			"type": msgtype,
			"message": b64encode(msg).decode(),
		})
	return messages

messages_schema = [
	bigquery.SchemaField("timestamp", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("type", "INT64", mode="REQUIRED"),
	bigquery.SchemaField("message", "BYTES", mode="REQUIRED"),
]

@cloud_event
def pollGossip2(cloud_event):
	new_timestamp = int(time())

	client = bigquery.Client()
	messages_table_id =  f"{client.project}.{environ.get('DATASET_ID')}.gossip_messages"

	messages_table = bigquery.Table(messages_table_id, schema=messages_schema)
	messages_table.clustering_fields = ["timestamp"]

	try:
		result = client.query(f"SELECT MAX(timestamp), ANY_VALUE(message HAVING MAX timestamp) FROM `{messages_table_id}` LIMIT 1").result()
		(timestamp, message) = next(result)
	except:
		print("Could not find last timestamp")
		timestamp = 0
		message = b"\x00"


	messages_table = client.create_table(messages_table, exists_ok=True)

	messages_job_config = bigquery.LoadJobConfig(schema=messages_schema, autodetect=False)

	r = requests.get(f"https://LNsync.blockstream.com/gossip/delta/{timestamp - 60*60}", stream=True)
	try:
		messages = parse_byte_stream(new_timestamp, BytesIO(r.content))
	except Exception as err:
		print("Error while requesting LNsync:", err)
		print(r.content)
		return

	if len(messages) == 0 or (len(messages) == 1 and messages[0]["message"] == b64encode(message).decode()):
		print("No new messages")
		return

	print(f"Adding {len(messages)} new gossip messages.")

	job = client.load_table_from_json(messages, messages_table, job_config=messages_job_config)
	try:
		job.result()
	except:
		print(job.errors)
		return
