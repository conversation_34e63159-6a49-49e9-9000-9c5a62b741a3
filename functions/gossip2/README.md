
# Persisting high-reslution gossip data in bigquery

LDK's rapidsync server prunes gossip data to remove all information
not related to routing.  This is nice for bandwidth, but leaves us without
some data that would be useful from the gossip messages:

-	node aliases
-	liquidity ads
-	metadata that would reveal lightning node implementation

Thankfully, blockstream runs their own version of a gossip synching
service that stores gossip messages in full
(https://github.com/lightningd/plugins/tree/master/historian)

