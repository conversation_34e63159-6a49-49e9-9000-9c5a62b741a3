from requests import Session
from datetime import timed<PERSON><PERSON>, datetime
from os import environ
from google.cloud import bigquery
from functions_framework import cloud_event
from time import sleep

def request_attempts(after, before):
	TWILIO_ACCOUNT = environ.get('TWILIO_ACCOUNT')
	TWILIO_ACCOUNT_SID = environ.get('TWILIO_ACCOUNT_SID')
	TWILIO_AUTH_TOKEN = environ.get('TWILIO_AUTH_TOKEN')
	s = Session()
	s.auth = (TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

	url = f"https://verify.twilio.com/v2/Attempts?DateCreatedAfter={after}&DateCreatedBefore={before}&PageSize=1000"
	backoff = 2
	while url and backoff < 256:
		try:
			response = s.get(url)
			response.raise_for_status()
		except:
			sleep(backoff)
			backoff *= 2
			continue
		backoff = 2
		json_response = response.json()
		url = json_response.get("meta").get("next_page_url")
		attempts = json_response.get("attempts")
		if not attempts:
			break
		for attempt in attempts:
			try:
				created_at = attempt["date_created"]
			except:
				created_at = None
			yield {"attempt": attempt, "created_at": created_at}

schema = [
	bigquery.SchemaField("attempt", "JSON", mode="REQUIRED"),
	bigquery.SchemaField("inserted_at", "TIMESTAMP", mode="REQUIRED"),
	bigquery.SchemaField("created_at", "TIMESTAMP", mode="NULLABLE"),
]

@cloud_event
def pollTwilioVerifications(cloud_event):
	client = bigquery.Client()
	table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"
	before = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ')
	after = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%dT%H:%M:%SZ')
	try:
		result = client.query(f"SELECT MAX(created_at) FROM `{table_id}` LIMIT 1").result()
		(after_,) = next(result)
		if after_:
			after = after_
	except:
		print("Could not find last date")

	inserted_at = {"inserted_at": datetime.now().isoformat()}

	attempts = [
		a | inserted_at
		for a in
		request_attempts(after, before)
	]

	print(f"Inserting {len(attempts)} attempts.")

	table = bigquery.Table(table_id, schema=schema)
	table.clustering_fields = ["created_at"]
	table = client.create_table(table, exists_ok=True)
	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
	job = client.load_table_from_json(attempts, table, job_config=job_config)
	try:
		job.result()
	except:
		print(job.errors)
