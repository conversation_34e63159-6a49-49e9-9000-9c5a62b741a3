const functions = require('@google-cloud/functions-framework');
const { BigQuery } = require('@google-cloud/bigquery');
const axios = require("axios");

functions.cloudEvent('pollOkexBidAsk', async (cloudEvent) => {
  const datasetId = process.env.DATASET_ID
  const tableId = process.env.TABLE_ID
  const bigquery = new BigQuery();

  try {
    await bigquery.dataset(datasetId).table(tableId).get();
    console.log(`Table ${tableId} exists.`);
  } catch (e) {
    console.log(`Creating table ${tableId}.`);
    schema = [
      { name: "ts", type: "TIMESTAMP", },
      { name: "last", type: "NUMERIC", },
      { name: "lastSz", type: "NUMERIC", },
      { name: "bidPx", type: "NUMERIC", },
      { name: "bidSz", type: "NUMERIC", },
      { name: "askPx", type: "NUMERIC", },
      { name: "askSz", type: "NUMERIC", },
      { name: "instType", type: "STRING" },
      { name: "instId", type: "STRING" }
    ];
    await bigquery.dataset(datasetId).createTable(tableId, { schema, location: 'US' });
    await new Promise(resolve => setTimeout(resolve, 10000));
  }

  const res = await axios.get('https://www.okx.com/api/v5/market/ticker?instId=BTC-USD-SWAP');

  const {
    instType,
    instId,
    ts,
    last,
    lastSz,
    bidPx,
    bidSz,
    askPx,
    askSz
  } = res.data.data[0];
  const rows = [
    {
      instType,
      instId,
      last: Number.parseFloat(last),
      lastSz: Number.parseFloat(lastSz),
      bidPx: Number.parseFloat(bidPx),
      bidSz: Number.parseFloat(bidSz),
      askPx: Number.parseFloat(askPx),
      askSz: Number.parseFloat(askSz),
      ts: Math.floor(Number.parseFloat(ts) / 1000)
    }
  ];
  console.log("Uploading rows:")
  console.log(JSON.stringify(rows));
  await bigquery
    .dataset(datasetId)
    .table(tableId)
    .insert(rows);
});
