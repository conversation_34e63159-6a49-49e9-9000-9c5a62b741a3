#!/usr/bin/env python3

from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import functions_framework
import os
import pandas as pd

from decimal import Decimal
from datetime import datetime

import ccxt
import numpy as np

#################################
# Data IN format
#################################
# {
#     "bal": "0.********",
#     "balChg": "-0.********",
#     "billId": "**********",
#     "ccy": "BTC",
#     "clientId": "9JUtUGgT5sl00A2SjDSsCuUBEf4xHmg2",
#     "ts": "*************",
#     "type": "131"
# }


#################################
# Data OUT format
#################################
# id,time,type,amount,before_balance,after_balance,fee,symbol
# **********,2024-01-09 02:49:53,Deposit,0.4669534,0.********,1.********,0,BTC
# **********,2024-01-09 02:44:33,To unified trading account,-0.********,1.********,0.********,0,BTC

def key_exists(dictionary, key):
    if hasattr(dictionary, '__getitem__') and not isinstance(dictionary, str):
        if isinstance(dictionary, list) and type(key) is not int:
            return False
        try:
            value = dictionary[key]
            return value is not None and value != ''
        except LookupError:
            return False
    return False

def to_type_string(key):
    types = {
        "1": "Deposit",
        "2": "Withdrawal",
        "13": "Canceled withdrawal",
        "20": "Transfer to sub account (for master account)",
        "21": "Transfer from sub account (for master account)",
        "22": "Transfer out from sub to master account (for sub-account)",
        "23": "Transfer in from master to sub account (for sub-account)",
        "28": "Manually claimed Airdrop",
        "47": "System reversal",
        "48": "Event Reward",
        "49": "Event Giveaway",
        "50": "Received from appointments",
        "51": "Deducted from appointments",
        "52": "Red packet sent",
        "53": "Red packet snatched",
        "54": "Red packet refunded",
        "61": "Convert",
        "68": "Fee rebate (by rebate card)",
        "69": "Distribute rebate card",
        "72": "Token received",
        "73": "Token given away",
        "74": "Token refunded",
        "75": "Subscription to savings",
        "76": "Redemption to savings",
        "77": "Distribute",
        "78": "Lock up",
        "79": "Node voting",
        "80": "DEFI/Staking purchase",
        "81": "Vote redemption",
        "82": "DEFI/Staking redemption",
        "83": "Staking yield",
        "84": "Violation fee",
        "85": "PoW mining yield",
        "86": "Cloud mining pay",
        "87": "Cloud mining yield",
        "88": "Subsidy",
        "89": "DEFI yield",
        "92": "Add collateral",
        "93": "Redeem collateral",
        "94": "Investment",
        "95": "Borrower borrows",
        "96": "Principal transferred in",
        "97": "Borrower transferred loan out",
        "98": "Borrower transferred interest out",
        "99": "Investor transferred interest in",
        "102": "Prepayment penalty transferred in",
        "103": "Prepayment penalty transferred out",
        "104": "Mortgage fee transferred in",
        "105": "Mortgage fee transferred out",
        "106": "Overdue fee transferred in",
        "107": "Overdue fee transferred out",
        "108": "Overdue interest transferred out",
        "109": "Overdue interest transferred in",
        "110": "Collateral for closed position transferred in",
        "111": "Collateral for closed position transferred out",
        "112": "Collateral for liquidation transferred in",
        "113": "Collateral for liquidation transferred out",
        "114": "Insurance fund transferred in",
        "115": "Insurance fund transferred out",
        "116": "Place an order",
        "117": "Fulfill an order",
        "118": "Cancel an order",
        "119": "Merchants unlock deposit",
        "120": "Merchants add deposit",
        "121": "FiatGateway Place an order",
        "122": "FiatGateway Cancel an order",
        "123": "FiatGateway Fulfill an order",
        "124": "Jumpstart unlocking",
        "125": "Manual deposit",
        "126": "Interest deposit",
        "127": "Investment fee transferred in",
        "128": "Investment fee transferred out",
        "129": "Rewards transferred in",
        "130": "Transferred from Trading account",
        "131": "Transferred to Trading account",
        "132": "Frozen by customer service",
        "133": "Unfrozen by customer service",
        "134": "Transferred by customer service",
        "135": "Cross chain exchange",
        "136": "ETH 2.0 staking system account increase ETH (for on-chain operation)",
        "137": "ETH 2.0 Subscription",
        "138": "ETH 2.0 Swapping",
        "139": "ETH 2.0 Earnings",
        "143": "System Reverse",
        "144": "Trading account reserve transferred out",
        "145": "Reward Expired",
        "146": "Customer feedback",
        "147": "vested sushi rewards",
        "150": "Affiliate commission",
        "151": "Referral reward",
        "152": "Broker reward",
        "153": "Joiner reward",
        "154": "Mystery box reward",
        "155": "Rewards withdraw",
        "160": "Dual Investment subscribe",
        "161": "Dual Investment collection",
        "162": "Dual Investment profit",
        "163": "Dual Investment refund",
        "169": "2022 new year rewards",
        "172": "Sub-affiliate commission",
        "173": "Fee rebate (by trading fee)",
        "174": "Pay",
        "175": "Locked collateral",
        "176": "Loan",
        "177": "Added collateral",
        "178": "Returned collateral",
        "179": "Repayment",
        "180": "Unlocked collateral",
        "181": "Airdrop Payment",
        "182": "Feedback bounty",
        "183": "Invite friends rewards",
        "184": "Divide the reward pool",
        "185": "Broker Convert Reward",
        "186": "FreeETH",
        "187": "Convert transfer",
        "188": "Slot Auction Conversion",
        "189": "Mystery box bonus",
        "193": "Card payment Buy",
        "195": "Untradable asset withdrawal",
        "196": "Untradable asset withdrawal revoked",
        "197": "Untradable asset deposit",
        "198": "Untradable asset collection reduce",
        "199": "Untradable asset collection increase",
        "200": "Buy",
        "202": "Price Lock Subscribe",
        "203": "Price Lock Collection",
        "204": "Price Lock Profit",
        "205": "Price Lock Refund",
        "207": "Dual Investment Lite Subscribe",
        "208": "Dual Investment Lite Collection",
        "209": "Dual Investment Lite Profit",
        "210": "Dual Investment Lite Refund",
        "211": "Win crypto with Satoshi",
        "212": "(Flexible loan) Multi-collateral loan collateral locked",
        "213": "(Flexible loan) Collateral transfer out from user's funding account",
        "214": "(Flexible loan) Collateral returned to users",
        "215": "(Flexible loan) Multi-collateral loan collateral released",
        "216": "(Flexible loan) Loan transfer into user's funding account",
        "217": "(Flexible loan) Multi-collateral loan borrowed",
        "218": "(Flexible loan) Multi-collateral loan repaid",
        "219": "(Flexible loan) Loan repayment transfer out from user's funding account",
        "220": "Delisted crypto",
        "221": "Blockchain's withdrawal fee",
        "222": "Withdrawal fee refund",
        "223": "SWAP lead trading profit share",
        "266": "SPOT lead trading profit share",
        "224": "Service fee",
        "225": "Shark Fin subscribe",
        "226": "Shark Fin collection",
        "227": "Shark Fin profit",
        "228": "Shark Fin refund",
        "229": "Airdrop",
        "230": "Token migration completed",
        "232": "Subsidized interest received",
        "233": "Broker rebate compensation",
        "249": "Seagull subscribe",
        "250": "Seagull collection",
        "251": "Seagull profit",
        "252": "Seagull refund",
        "263": "Strategy bots profit share",
        "270": "DCD broker transfer",
        "271": "DCD broker rebate",
        "284": "Transfer out trading sub-account",
        "285": "Transfer in trading sub-account",
        "286": "Transfer out custody funding account",
        "287": "Transfer in custody funding account",
        "288": "Custody fund delegation",
        "289": "Custody fund undelegation",
        "303": "Snowball market maker transfer",
        "311": "Crypto dust auto-transfer in",
    }
    return str(types[key]) if key_exists(types, key) else None

def fetch_funding_account_history_df(exchange, since=0):
    """https://docs.ccxt.com/#/exchanges/okx?id=fetchledger"""
    #
    # get funding account billing records from exchange
    #
    funding_records = exchange.private_get_asset_bills(params={'ccy': 'BTC', 'before': since})

    # convert nested list of dictionaries to pandas DataFrame
    df = pd.json_normalize(funding_records['data'])

    if len(df.index) == 0:
        return df

    df.rename(columns={
        'billId': 'id',
        'ts': 'time',
        'balChg': 'amount',
        'bal': 'after_balance',
        'ccy': 'symbol'
        }, inplace=True)

    # convert object types to required types (uint64, float, decimal, string)
    for c in ['amount', 'after_balance']:
        df[c] = df[c].apply(lambda x: Decimal(str(x)) if x is not None else x)
    df['id'] = df.id.astype(np.int64)
    df['time'] = pd.to_datetime(df.time, unit='ms')
    df['type'] = df['type'].apply(lambda x: to_type_string(x))

    # create new fields
    df['before_balance'] = df['after_balance'] - df['amount']
    df['fee'] = Decimal(0)

    # filter relevant fields
    df = df.loc[:, [
        'id',
        'time',
        'type',
        'amount',
        'before_balance',
        'after_balance',
        'fee',
        'symbol',
    ]]

    # Only None accepted by BigQuery for NULL
    df.replace('', None, inplace=True)

    return df

@functions_framework.cloud_event
def pollOkxFundingAccountHistory(cloud_event):
    """https://www.okx.com/docs-v5/en/#funding-account-rest-api-asset-bills-details"""
    apikey = os.environ.get('OKEX_API_KEY')
    secretkey = os.environ.get('OKEX_SECRET_KEY')
    passphrase = os.environ.get('OKEX_PASSPHRASE')
    environment = os.environ.get('ENVIRONMENT')
    okx = ccxt.okx({
        'apiKey':  apikey,
        'secret':  secretkey,
        'password':  passphrase,
    })

    if environment and 'staging' in environment.lower():
        okx.set_sandbox_mode(True)
    else:
        okx.set_sandbox_mode(False)

    client = bigquery.Client()
    table_id = f"{client.project}.{os.environ.get('DATASET_ID')}.{os.environ.get('TABLE_ID')}"

    schema = [
        bigquery.SchemaField("id", "INTEGER", mode="REQUIRED"),
        bigquery.SchemaField("time", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("type", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("amount", "NUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("before_balance", "NUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("after_balance", "NUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("fee", "NUMERIC", mode="REQUIRED"),
        bigquery.SchemaField("symbol", "STRING", mode="REQUIRED"),
    ]

    try:
        client.get_table(table_id)
        print(f"Table {table_id} already exists.")

        sql_query = f"SELECT id, time FROM {table_id} ORDER BY time DESC LIMIT 1"
        query_job = client.query(sql_query)
        extable = query_job.result()

        if extable.total_rows == 0:
            remote_time = 0
            remote_id = 0
            print("Empty table? No entry found")
        else:
            for r in extable:
                remote_id = np.uint64(r['id'])
                remote_time = np.uint64(pd.Timestamp(str(r['time'])).timestamp() * 1000)
            print(f"Most recent entry in cloud table: {r['time']}")

        df = fetch_funding_account_history_df(exchange=okx, since=remote_time)
        if len(df.index) != 0:
            df = df.query("id>@remote_id").drop_duplicates()

    except NotFound:
        table = bigquery.Table(table_id, schema=schema)
        table = client.create_table(table)
        print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

        df = fetch_funding_account_history_df(exchange=okx)

    if len(df) > 0:

        job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
        job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()

        table = client.get_table(table_id)  # Make an API request
        print(f"Loaded {df.shape[0]} {'rows' if df.shape[0]>1 else 'row'} with {len(table.schema)} columns to {table_id}")

    print("job done")
