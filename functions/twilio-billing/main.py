from requests import Session
from datetime import date, timedelta, datetime
from os import environ
from google.cloud import bigquery
from functions_framework import cloud_event
from time import sleep

def request_records(start_date, end_date):
	TWILIO_ACCOUNT = environ.get('TWILIO_ACCOUNT')
	TWILIO_ACCOUNT_SID = environ.get('TWILIO_ACCOUNT_SID')
	TWILIO_AUTH_TOKEN = environ.get('TWILIO_AUTH_TOKEN')
	s = Session()
	s.auth = (TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

	uri = f"/2010-04-01/Accounts/{TWILIO_ACCOUNT}/Usage/Records/Daily.json?StartDate={start_date}&EndDate={end_date}&PageSize=20"
	backoff = 2
	while uri and backoff < 43200:
		try:
			response = s.get("https://api.twilio.com" + uri)
			response.raise_for_status()
		except:
			sleep(backoff)
			backoff *= 2
			continue
		backoff = 2
		json_response = response.json()
		uri = json_response.get("next_page_uri")
		records = json_response.get("usage_records")
		if not records:
			break
		for record in records:
			try:
				date_of = record["end_date"]
			except:
				date_of = None
			yield {"record": record, "date_of": date_of}

schema = [
	bigquery.SchemaField("record", "JSON", mode="REQUIRED"),
	bigquery.SchemaField("inserted_at", "TIMESTAMP", mode="REQUIRED"),
	bigquery.SchemaField("date_of", "DATE", mode="NULLABLE"),
]

@cloud_event
def pollTwilioBilling(cloud_event):
	client = bigquery.Client()
	table_id =  f"{client.project}.{environ.get('DATASET_ID')}.{environ.get('TABLE_ID')}"
	last_date = date.today() - timedelta(days=3)
	try:
		result = client.query(f"SELECT MAX(date_of) FROM `{table_id}` LIMIT 1").result()
		(last_date,) = next(result)
	except:
		print("Could not find last date")

	inserted_at = {"inserted_at": datetime.now().isoformat()}

	records = [
		r | inserted_at
		for r in
		request_records(
			start_date = last_date + timedelta(days=1),
			end_date = date.today() - timedelta(days=1),
		)
	]

	print(f"Inserting {len(records)} records.")

	table = bigquery.Table(table_id, schema=schema)
	table.clustering_fields = ["date_of"]
	table = client.create_table(table, exists_ok=True)
	job_config = bigquery.LoadJobConfig(schema=schema, autodetect=False)
	job = client.load_table_from_json(records, table, job_config=job_config)
	try:
		job.result()
	except:
		print(job.errors)
