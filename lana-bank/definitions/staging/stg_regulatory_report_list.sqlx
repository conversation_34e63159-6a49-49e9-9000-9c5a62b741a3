config {
	type: "table",
}

WITH report_list AS (

-- NPB4-17 - Risk Asset System - 01. People (persona.xml)
    SELECT 1 AS report_id, "01.npb4_17_persona_xml" AS report_name, "NPB4-17" AS report_norm, "persona.xml" AS report_filename, "XML" AS report_format, False AS report_enabled, "raw_01_npb4_17_persona_xml" AS source_table_name, "stg_01_npb4_17_persona_xml" AS destination_table_name, 1 AS field_id, "nit_persona" AS field_name, "XsString" AS field_type, NULL AS field_format, 14 AS field_max_size, NULL AS field_max_decimal, "NIT de la persona" AS field_desc_original, "Tax Identification Number (NIT) of the person" AS field_desc_english, NULL AS field_assertion UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",2,"dui","XsString",NULL,9,N<PERSON><PERSON>,"DUI de la persona","person's (Unique Identification Document) DUI",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",3,"primer_apellido","XsString",NULL,25,NULL,"Primer apellido de la persona","First surname of the person",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",4,"segundo_apellido","XsString",NULL,25,NULL,"Segundo apellido de la persona","Second surname of the person",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",5,"apellido_casada","XsString",NULL,25,NULL,"Apellido de casada de la persona","Married surname of the person",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",6,"primer_nombre","XsString",NULL,25,NULL,"Primer nombre de la persona","Person's first name",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",7,"segundo_nombre","XsString",NULL,25,NULL,"Segundo nombre de la persona","Middle name of the person",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",8,"nombre_sociedad","XsString",NULL,100,NULL,"Nombre de la persona jurídica","Name of the legal entity",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",9,"tipo_persona","XsString",NULL,1,NULL,"Código del tipo de persona","Person type code",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",10,"tipo_relacion","XsString",NULL,1,NULL,"Código del tipo de relación del deudor con la entidad","Code of the type of relationship of the debtor with the entity",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",11,"tipo_identificador","XsString",NULL,1,NULL,"Código del tipo de identificador","Identifier type code",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",12,"nit_desactualizado","XsString",NULL,14,NULL,"NIT para actualización y cambios (21)","NIT for update and changes (21)",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",13,"residente","XsString",NULL,1,NULL,"Si la persona es residente o no en El Salvador (21)","Whether the person is a resident or not in El Salvador (21)",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",14,"giro_persona","XsString",NULL,6,NULL,"Código del giro o actividad económica de la persona","Code of the person's line of business or economic activity",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",15,"tamano_empresa","XsString",NULL,2,NULL,"Código del tamaño de la empresa","Company size code",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",16,"tipo_empresa","XsString",NULL,1,NULL,"Si la empresa es nueva o existente","If the company is new or existing",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",17,"reserva","XsDecimal",NULL,12,2,"Reservas de saneamiento","Sanitation reserves",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",18,"categoria_riesgo","XsString",NULL,2,NULL,"Código de la categoría de riesgo","Risk category code",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",19,"numero_cliente","XsString",NULL,17,NULL,"Número de Identificación Único (NIU) del cliente","Unique Identification Number (NIU) of the client",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",20,"id_alterno","XsString",NULL,20,NULL,"Número de identificación alterno","Alternate Identification Number",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",21,"tipo_id_alterno","XsString",NULL,2,NULL,"Tipo de identificación alterno","Alternate Identification Type",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",22,"fecha_nacimiento","XsDate",NULL,NULL,NULL,"Fecha de nacimiento o constitución","Date of birth or constitution",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",23,"pais_residencia","XsInt",NULL,NULL,NULL,"País de residencia de la persona (21)","Country of residence of the person (21)",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",24,"riesgo_consolidado","XsDecimal",NULL,12,2,"Sumatoria de saldos de referencias","Sum of reference balances",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",25,"sexo_persona","XsString",NULL,1,NULL,"Sexo de la persona","Sex of the person",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",26,"ocupación","XsString",NULL,3,NULL,"Ocupación de la persona","Person's occupation",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",27,"id_pais_origen","XsString",NULL,20,NULL,"Identificación tributaria del país de origen de la persona","Tax identification of the person's country of origin",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",28,"nacionalidad","XsInt",NULL,NULL,NULL,"Nacionalidad de la persona (21)","Nationality of the person (21)",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",29,"nit_anterior","XsString",NULL,14,NULL,"NIT anterior a reemplazo de DUI-NIT del Ministerio de Hacienda (21)","NIT prior to replacement of DUI-NIT of the Ministry of Finance (21)",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",30,"tipo_ident_anterior","XsString",NULL,1,NULL,"Código del tipo de identificador anterior (21)","Previous identifier type code (21)",NULL UNION ALL
    SELECT 1,"01.npb4_17_persona_xml","NPB4-17","persona.xml","XML",False,"raw_01_npb4_17_persona_xml","stg_01_npb4_17_persona_xml",31,"municipio_residencia","XsString",NULL,4,NULL,"Código del municipio de residencia de la persona (21)","Code of the municipality of residence of the person (21)",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 02. Risk asset references (referencia.xml)
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",1,"nit_deudor","XsString",NULL,14,NULL,"NIT del deudor","Debtor's NIT",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",2,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera","Wallet type code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",3,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo","Risk asset type code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",4,"num_referencia","XsString",NULL,20,NULL,"Número de la referencia","Reference number",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",5,"monto_referencia","XsDecimal",NULL,12,2,"Monto de la referencia","Reference amount",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",6,"saldo_referencia","XsDecimal",NULL,12,2,"Saldo de la referencia","Reference balance",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",7,"saldo_vigente_k","XsDecimal",NULL,12,2,"Saldo vigente de capital","Current capital balance",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",8,"saldo_vencido_k","XsDecimal",NULL,12,2,"Saldo vencido de capital","Overdue capital balance",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",9,"saldo_vigente_i","XsDecimal",NULL,12,2,"Saldo vigente de intereses","Current interest balance",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",10,"saldo_vencido_i","XsDecimal",NULL,12,2,"Saldo vencido de intereses","Overdue interest balance",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",11,"abono_deposito","XsDecimal",NULL,12,2,"Abonos sin aplicar al préstamo/ depósito sin aplicar a carta de crédito","Payments not applied to the loan/deposit not applied to a letter of credit",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",12,"fecha_otorgamiento","XsDate",NULL,NULL,NULL,"Fecha de otorgamiento","Award date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",13,"fecha_vencimiento","XsDate",NULL,NULL,NULL,"Fecha de vencimiento","Due date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",14,"fecha_castigo","XsDate",NULL,NULL,NULL,"Fecha de castigo","Punishment date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",15,"estado_credito","XsString",NULL,1,NULL,"Código del estado del crédito","Credit status code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",16,"saldo_mora_k","XsDecimal",NULL,12,2,"Saldo en mora de capital","Balance in capital arrears",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",17,"saldo_mora_i","XsDecimal",NULL,12,2,"Saldo en mora de intereses","Balance in arrears of interest",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",18,"dias_mora_k","XsInt",NULL,NULL,NULL,"Días de mora de capital","Days of capital arrears",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",19,"dias_mora_i","XsInt",NULL,NULL,NULL,"Días de mora de intereses","Days of interest arrears",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",20,"fecha_inicio_mora_k","XsDate",NULL,NULL,NULL,"Fecha de inicio de mora de capital","Capital arrears start date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",21,"fecha_inicio_mora_i","XsDate",NULL,NULL,NULL,"Fecha de inicio de mora de intereses","Start date of interest arrears",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",22,"pago_capital","XsString",NULL,1,NULL,"Código de forma de pago de capital","Capital payment method code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",23,"pago_interes","XsString",NULL,1,NULL,"Código de forma de pago de intereses","Interest payment method code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",24,"periodo_gracia_k","XsInt",NULL,NULL,NULL,"Periodo de gracia para el pago de capital","Grace period for capital payment",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",25,"periodo_gracia_i","XsInt",NULL,NULL,NULL,"Periodo de gracia para el pago de intereses","Grace period for interest payments",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",26,"garante","XsString",NULL,10,NULL,"Código de garantes de créditos pignorados","Code of guarantors of pledged credits",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",27,"emisión","XsString",NULL,15,NULL,"Denominación de la emisión","Issue name",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",28,"pais_destino_credito","XsInt",NULL,NULL,NULL,"Código de país de destino del crédito (21)","Credit destination country code (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",29,"destino","XsString",NULL,6,NULL,"Código de destino de los préstamos por sector económico","Loan destination code by economic sector",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",30,"codigo_moneda","XsString",NULL,1,NULL,"Código de moneda","currency code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",31,"tasa_interes","XsDecimal",NULL,5,2,"Tasa de interés actual","Current interest rate",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",32,"tasa_contractual","XsDecimal",NULL,5,2,"Tasa contractual o nominal","Contractual or nominal rate",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",33,"tasa_referencia","XsDecimal",NULL,5,2,"Tasa de referencia publicada","Published reference rate",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",34,"tasa_efectiva","XsDecimal",NULL,5,2,"Tasa efectiva","Effective rate",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",35,"tipo_tasa_interes","XsString",NULL,1,NULL,"Tipo de tasa nominal","Nominal rate type",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",36,"tipo_prestamo","XsString",NULL,2,NULL,"Código del tipo de prestamos","Loan type code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",37,"codigo_recurso","XsString",NULL,2,NULL,"Código de fuente de recursos","Resource source code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",38,"ultima_fecha_venc","XsDate",NULL,NULL,NULL,"Última fecha de vencimiento","Last expiration date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",39,"dias_prorroga","XsInt",NULL,NULL,NULL,"Días de prorroga","Extension days",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",40,"monto_desembolsado","XsDecimal",NULL,12,2,"Monto desembolsado en el mes","Amount disbursed in the month",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",41,"tipo_credito","XsString",NULL,2,NULL,"Tipo de crédito","Type of credit",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",42,"fecha_ultimo_pago_k","XsDate",NULL,NULL,NULL,"Fecha del último pago de capital cubierto","Date of last covered principal payment",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",43,"fecha_ultimo_pago_i","XsDate",NULL,NULL,NULL,"Fecha del último pago de intereses cubierto","Date of last covered interest payment",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",44,"dia_pago_k","XsInt",NULL,NULL,NULL,"Día del pago del capital","Capital payment day",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",45,"dia_pago_i","XsInt",NULL,NULL,NULL,"Día del pago de intereses","Interest payment day",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",46,"cuota_mora_k","XsInt",NULL,NULL,NULL,"Número de cuotas en mora de capital","Number of installments in capital arrears",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",47,"cuota_mora_i","XsInt",NULL,NULL,NULL,"Número de cuotas en mora de intereses","Number of installments in arrears with interest",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",48,"monto_cuota","XsDecimal",NULL,12,2,"Monto de la cuota de la referencia incluyendo capital más intereses","Reference installment amount including capital plus interest",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",49,"cuenta_contable_k","XsString",NULL,12,NULL,"Cuenta contable de capital","Capital accounting account",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",50,"cuenta_contable_i","XsString",NULL,12,NULL,"Cuenta contable de intereses","Interest accounting account",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",51,"fecha_cancelacion","XsDate",NULL,NULL,NULL,"Fecha de cancelación de la referencia","Reference cancellation date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",52,"adelanto_capital","XsDecimal",NULL,12,2,"Pagos adelantados de capital","Capital Advance Payments",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",53,"riesgo_neto","XsDecimal",NULL,12,2,"Riesgo neto","Net risk",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",54,"saldo_seguro","XsDecimal",NULL,12,2,"Saldo del seguro del crédito","Credit insurance balance",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",55,"saldo_costas_procesales","XsDecimal",NULL,12,2,"Saldo por costas procesales","Balance for procedural costs",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",56,"tipo_tarjeta_credito","XsString",NULL,1,NULL,"Tipo de tarjeta de crédito","Credit card type",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",57,"clase_tarjeta_credito","XsString",NULL,1,NULL,"Código de la clase de tarjeta de crédito","Credit card class code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",58,"producto_tarjeta_credito","XsString",NULL,20,NULL,"Producto de la tarjeta de crédito","credit card product",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",59,"valor_garantia_cons","XsDecimal",NULL,12,2,"Valor consolidado de la garantía","Consolidated value of the guarantee",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",60,"municipio_otorgamiento","XsString",NULL,4,NULL,"Código del municipio destino del crédito","Code of the municipality receiving the credit",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",61,"reserva_referencia","XsDecimal",NULL,12,2,"Reserva por referencia","Reservation by reference",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",62,"etapa_judicial","XsString",NULL,1,NULL,"Código de la etapa del crédito en vía judicial","Judicial credit stage code",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",63,"fecha_demanda","XsDate",NULL,NULL,NULL,"Fecha de la demanda","Demand date",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",64,"plazo_credito","XsInt",NULL,NULL,NULL,"Plazo del crédito","Credit term",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",65,"orden_descuento","XsString",NULL,2,NULL,"Con o sin orden de descuento","With or without discount order",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",66,"categoria_riesgo_ref","XsString",NULL,2,NULL,"Código de la categoría de riesgo de la referencia (20)","Reference risk category code (20)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",67,"reserva_constituir","XsDecimal",NULL,12,2,"Reserva acumulada a constituir créditos COVID (20)","Accumulated reserve to constitute COVID credits (20)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",68,"porcentaje_reserva","XsDecimal",NULL,6,2,"Porcentaje de reserva constituida de acuerdo con la gradualidad créditos COVID (20)","Percentage of reserve constituted in accordance with the gradual COVID credits (20)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",69,"pago_cuota","XsDecimal",NULL,12,2,"Monto pagado de la cuota de la referencia (20) (21)","Amount paid of the reference fee (20) (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",70,"fecha_pago","XsDate",NULL,NULL,NULL,"Fecha en que realizó el pago de la cuota (20) (21)","Date on which the fee payment was made (20) (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",71,"porcenta_reserva_descon","XsDecimal",NULL,6,2,"Porcentaje de reserva a descontar créditos agropecuarios (20)","Percentage of reserve to be deducted from agricultural credits (20)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",72,"porcenta_adiciona_descon","XsDecimal",NULL,6,2,"Porcentaje de reserva adicional a descontar créditos agropecuarios (20)","Percentage of additional reserve to be deducted from agricultural credits (20)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",73,"depto_destino_credito","XsString",NULL,2,NULL,"Código de departamento de destino del crédito (21)","Credit destination department code (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",74,"porc_reserva_referencia","XsDecimal",NULL,6,2,"Porcentaje de reserva por referencia (21)","Reservation percentage by reference (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",75,"calculo_brecha","XsDecimal",NULL,12,2,"Cálculo de brecha de reserva de saneamiento de la referencia (21)","Reference sanitation reserve gap calculation (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",76,"ajuste_brecha","XsDecimal",NULL,12,2,"Cálculo de ajuste mensual de brecha de la referencia (21)","Calculation of monthly reference gap adjustment (21)",NULL UNION ALL
    SELECT 2,"02.npb4_17_referencia.xml","NPB4-17","referencia.xml","XML",False,"raw_02_npb4_17_referencia_xml","stg_02_npb4_17_referencia_xml",77,"programa_asist_cafe","XsString",NULL,2,NULL,"Programa de asistencia suscrito créditos destino café (21)","Assistance program subscribed for coffee destination credits (21)",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 03. Association of references and guarantees (referencia_garantia.xml)
    SELECT 3,"03.npb4_17_referencia_garantia.xml","NPB4-17","referencia_garantia.xml","XML",False,"raw_03_npb4_17_referencia_garantia_xml","stg_03_npb4_17_referencia_garantia_xml",1,"num_referencia","XsString",NULL,20,NULL,"Número de la referencia","Reference number",NULL UNION ALL
    SELECT 3,"03.npb4_17_referencia_garantia.xml","NPB4-17","referencia_garantia.xml","XML",False,"raw_03_npb4_17_referencia_garantia_xml","stg_03_npb4_17_referencia_garantia_xml",2,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera","Wallet type code",NULL UNION ALL
    SELECT 3,"03.npb4_17_referencia_garantia.xml","NPB4-17","referencia_garantia.xml","XML",False,"raw_03_npb4_17_referencia_garantia_xml","stg_03_npb4_17_referencia_garantia_xml",3,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo","Risk asset type code",NULL UNION ALL
    SELECT 3,"03.npb4_17_referencia_garantia.xml","NPB4-17","referencia_garantia.xml","XML",False,"raw_03_npb4_17_referencia_garantia_xml","stg_03_npb4_17_referencia_garantia_xml",4,"identificacion_garantia","XsString",NULL,20,NULL,"Identificación única de la garantía","Unique warranty identification",NULL UNION ALL
    SELECT 3,"03.npb4_17_referencia_garantia.xml","NPB4-17","referencia_garantia.xml","XML",False,"raw_03_npb4_17_referencia_garantia_xml","stg_03_npb4_17_referencia_garantia_xml",5,"tipo_garantia","XsString",NULL,2,NULL,"Código del tipo de garantía","Warranty type code",NULL UNION ALL
    SELECT 3,"03.npb4_17_referencia_garantia.xml","NPB4-17","referencia_garantia.xml","XML",False,"raw_03_npb4_17_referencia_garantia_xml","stg_03_npb4_17_referencia_garantia_xml",6,"valor_garantia_proporcional","XsDecimal",NULL,12,2,"Valor proporcional de la garantía que cubre a la referencia","Proportional value of the guarantee that covers the reference",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 04. Mortgage guarantees (garantia_hipotecaria.xml)
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",1,"identificacion_garantia","XsString",NULL,20,NULL,"Identificación única de la garantía","Unique warranty identification","REGEXP_CONTAINS(`identificacion_garantia`, '[^A-ZÑÁÉÍÓÚÜ]+')" UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",2,"numero_registro","XsString",NULL,40,NULL,"Número de registro asignado por el CNR","Registration number assigned by the CNR",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",3,"nit_propietario","XsString",NULL,14,NULL,"NIT del propietario de la garantía","NIT of the warranty owner",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",4,"fecha_registro","XsDate",NULL,NULL,NULL,"Fecha de registro","Registration date",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",5,"estado","XsString",NULL,1,NULL,"Estado de la garantía","Warranty Status","`estado` IN ('P', 'E', 'I')" UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",6,"cod_ubicacion","XsString",NULL,4,NULL,"Código de ubicación de la garantía","Warranty Location Code","`cod_ubicacion` IN (SELECT `code` FROM ${ref('stg_dim_14_npb4_17_municipios')})" UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",7,"descripción","XsString",NULL,60,NULL,"Descripción del inmueble","Property description",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",8,"fecha_valuo","XsDate",NULL,NULL,NULL,"Fecha de valúo","Valuation date",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",9,"valor_pericial","XsDecimal",NULL,12,2,"Valor pericial","expert value",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",10,"valor_contractual","XsDecimal",NULL,12,2,"Valor contractual","Contract value",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",11,"valor_mercado","XsDecimal",NULL,12,2,"Valor de mercado","Market value",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",12,"grado_hipoteca","XsInt",NULL,NULL,NULL,"Número de hipoteca para una referencia en particular","Mortgage number for a particular reference",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",13,"direccion_gtia","XsString",NULL,60,NULL,"Dirección de la garantía","Warranty address",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",14,"cod_perito","XsString",NULL,10,NULL,"Código del perito valuador","Valuation expert code",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",15,"nombre_perito","XsString",NULL,70,NULL,"Nombre del perito valuador","Name of the appraiser",NULL UNION ALL
    SELECT 4,"04.npb4_17_garantia_hipotecaria.xml","NPB4-17","garantia_hipotecaria.xml","XML",True,"raw_04_npb4_17_garantia_hipotecaria_xml","stg_04_npb4_17_garantia_hipotecaria_xml",16,"tipo_perito","XsString",NULL,1,NULL,"Tipo del perito valuador","Type of appraiser",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 05. Fiduciary guarantees (garantia_fiduciaria.xml)
    SELECT 5,"05.npb4_17_garantia_fiduciaria.xml","NPB4-17","garantia_fiduciaria.xml","XML",False,"raw_05_npb4_17_garantia_fiduciaria_xml","stg_05_npb4_17_garantia_fiduciaria_xml",1,"num_referencia","XsString",NULL,20,NULL,"Número de la referencia","Reference number",NULL UNION ALL
    SELECT 5,"05.npb4_17_garantia_fiduciaria.xml","NPB4-17","garantia_fiduciaria.xml","XML",False,"raw_05_npb4_17_garantia_fiduciaria_xml","stg_05_npb4_17_garantia_fiduciaria_xml",2,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera","Wallet type code",NULL UNION ALL
    SELECT 5,"05.npb4_17_garantia_fiduciaria.xml","NPB4-17","garantia_fiduciaria.xml","XML",False,"raw_05_npb4_17_garantia_fiduciaria_xml","stg_05_npb4_17_garantia_fiduciaria_xml",3,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo","Risk asset type code",NULL UNION ALL
    SELECT 5,"05.npb4_17_garantia_fiduciaria.xml","NPB4-17","garantia_fiduciaria.xml","XML",False,"raw_05_npb4_17_garantia_fiduciaria_xml","stg_05_npb4_17_garantia_fiduciaria_xml",4,"nit_fiador_codeudor","XsString",NULL,14,NULL,"NIT del fiador o codeudor de la referencia","NIT of the guarantor or co-signer of the reference",NULL UNION ALL
    SELECT 5,"05.npb4_17_garantia_fiduciaria.xml","NPB4-17","garantia_fiduciaria.xml","XML",False,"raw_05_npb4_17_garantia_fiduciaria_xml","stg_05_npb4_17_garantia_fiduciaria_xml",5,"fiador_codeudor","XsString",NULL,1,NULL,"Si la persona es fiadora o codeudora de la referencia","If the person is a guarantor or co-signer of the reference",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 06. Guarantees of guarantees, bonds and stand-by letters (garantia_aval.xml)
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",1,"num_referencia","XsString",NULL,20,NULL,"Número de la referencia","Reference number",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",2,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera","Wallet type code",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",3,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo","Risk asset type code",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",4,"identificacion_garantia","XsString",NULL,20,NULL,"Identificación única de la garantía","Unique warranty identification",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",5,"cod_banco","XsString",NULL,4,NULL,"Código de banco emisor de la garantía","Guarantee issuing bank code",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",6,"monto_aval","XsDecimal",NULL,12,2,"Monto avalado","Guaranteed amount",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",7,"fecha_otorgamiento","XsDate",NULL,NULL,NULL,"Fecha de otorgamiento de la garantía","Guarantee granting date",NULL UNION ALL
    SELECT 6,"06.npb4_17_garantia_aval.xml","NPB4-17","garantia_aval.xml","XML",False,"raw_06_npb4_17_garantia_aval_xml","stg_06_npb4_17_garantia_aval_xml",8,"fecha_vencimiento","XsDate",NULL,NULL,NULL,"Fecha de vencimiento de la garantía","Warranty expiration date",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 07. Guarantees of pledged deposits (garantia_pignorada.xml)
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",1,"identificacion_garantia","XsString",NULL,20,NULL,"Número del certificado de depósito o número de cuenta","Certificate of deposit number or account number",NULL UNION ALL
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",2,"nit_depositante","XsString",NULL,14,NULL,"NIT de la persona propietaria del depósito","NIT of the person who owns the deposit",NULL UNION ALL
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",3,"fecha_deposito","XsDate",NULL,NULL,NULL,"Fecha de apertura del depósito","Deposit opening date",NULL UNION ALL
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",4,"fecha_vencimiento","XsDate",NULL,NULL,NULL,"Fecha de vencimiento del depósito","Deposit expiration date",NULL UNION ALL
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",5,"valor_deposito","XsDecimal",NULL,12,2,"Valor del depósito","Deposit value",NULL UNION ALL
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",6,"tipo_deposito","XsString",NULL,2,NULL,"Tipo de depósito","Deposit type",NULL UNION ALL
    SELECT 7,"07.npb4_17_garantia_pignorada.xml","NPB4-17","garantia_pignorada.xml","XML",False,"raw_07_npb4_17_garantia_pignorada_xml","stg_07_npb4_17_garantia_pignorada_xml",7,"cod_banco","XsString",NULL,4,NULL,"Código de banco emisor de la garantía","Guarantee issuing bank code",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 08. Guarantees of pledges of fixed income securities (garantia_prenda.xml)
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",1,"identificacion_garantia","XsString",NULL,20,NULL,"Número del título","Title number",NULL UNION ALL
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",2,"denominacion_titulo","XsString",NULL,20,NULL,"Denominación del título","Title name",NULL UNION ALL
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",3,"local_extranjera","XsString",NULL,1,NULL,"Si el título es local o extranjero","Whether the title is local or foreign",NULL UNION ALL
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",4,"monto_inversion","XsDecimal",NULL,14,2,"Monto de la inversión","Investment amount",NULL UNION ALL
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",5,"fecha_vencimiento","XsDate",NULL,NULL,NULL,"Fecha de vencimiento del título","Title expiration date",NULL UNION ALL
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",6,"clasificación","XsString",NULL,25,NULL,"Calificación de riesgo del título","Title Risk Rating",NULL UNION ALL
    SELECT 8,"08.npb4_17_garantia_prenda.xml","NPB4-17","garantia_prenda.xml","XML",False,"raw_08_npb4_17_garantia_prenda_xml","stg_08_npb4_17_garantia_prenda_xml",7,"nombre_clasificadora","XsString",NULL,50,NULL,"Nombre de la clasificadora de riesgo que asignó la calificación","Name of the risk rating agency that assigned the rating",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 09. Pledge bond guarantees (garantia_bono.xml)
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",1,"identificacion_garantia","XsString",NULL,20,NULL,"Número del bono de prenda","Pledge bond number",NULL UNION ALL
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",2,"tipo_prenda","XsString",NULL,2,NULL,"Código del tipo de prenda","Garment type code",NULL UNION ALL
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",3,"descripción","XsString",NULL,60,NULL,"Descripción de la prenda que respalda el bono","Description of the pledge that backs the bond",NULL UNION ALL
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",4,"fecha_certificado","XsDate",NULL,NULL,NULL,"Fecha del certificado","Certificate date",NULL UNION ALL
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",5,"valor_prenda","XsDecimal",NULL,12,2,"Valor del bono de prenda","Pledge bond value",NULL UNION ALL
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",6,"saldo_prenda","XsDecimal",NULL,12,2,"Saldo del valor del bono de prenda","Balance of the value of the pledge bond",NULL UNION ALL
    SELECT 9,"09.npb4_17_garantia_bono.xml","NPB4-17","garantia_bono.xml","XML",False,"raw_09_npb4_17_garantia_bono_xml","stg_09_npb4_17_garantia_bono_xml",7,"cod_almacenadora","XsString",NULL,10,NULL,"Código de la almacenadora","Store code",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 10. Policy guarantees (garantia_poliza.xml)
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",1,"identificacion_garantia","XsString",NULL,20,NULL,"Número de la póliza","Policy number",NULL UNION ALL
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",2,"monto_poliza","XsDecimal",NULL,12,2,"Monto de la póliza","Policy amount",NULL UNION ALL
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",3,"fecha_inicial","XsDate",NULL,NULL,NULL,"Fecha de inicio de la vigencia de la póliza","Policy start date",NULL UNION ALL
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",4,"fecha_final","XsDate",NULL,NULL,NULL,"Fecha de finalización de la vigencia de la póliza","Policy end date",NULL UNION ALL
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",5,"nombre_asegurado","XsString",NULL,100,NULL,"Nombre del asegurado","Name of Insure",NULL UNION ALL
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",6,"monto_reserva","XsDecimal",NULL,12,2,"Monto de la reserva matemática","Mathematical reserve amount",NULL UNION ALL
    SELECT 10,"10.npb4_17_garantia_poliza.xml","NPB4-17","garantia_poliza.xml","XML",False,"raw_10_npb4_17_garantia_poliza_xml","stg_10_npb4_17_garantia_poliza_xml",7,"valor_garantia","XsDecimal",NULL,12,2,"Monto de los valores garantizados","Amount of guaranteed values",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 11. Guarantee funds (garantia_fondo.xml)
    SELECT 11,"11.npb4_17_garantia_fondo.xml","NPB4-17","garantia_fondo.xml","XML",False,"raw_11_npb4_17_garantia_fondo_xml","stg_11_npb4_17_garantia_fondo_xml",1,"identificacion_garantia","XsString",NULL,20,NULL,"Número del aval","Guarantee number",NULL UNION ALL
    SELECT 11,"11.npb4_17_garantia_fondo.xml","NPB4-17","garantia_fondo.xml","XML",False,"raw_11_npb4_17_garantia_fondo_xml","stg_11_npb4_17_garantia_fondo_xml",2,"valor_garantia","XsDecimal",NULL,12,2,"Valor del aval","Guarantee value",NULL UNION ALL
    SELECT 11,"11.npb4_17_garantia_fondo.xml","NPB4-17","garantia_fondo.xml","XML",False,"raw_11_npb4_17_garantia_fondo_xml","stg_11_npb4_17_garantia_fondo_xml",3,"valor_porcentual","XsDecimal",NULL,6,2,"Valor proporcional de la garantía que cubre a la referencia","Proportional value of the guarantee that covers the reference",NULL UNION ALL
    SELECT 11,"11.npb4_17_garantia_fondo.xml","NPB4-17","garantia_fondo.xml","XML",False,"raw_11_npb4_17_garantia_fondo_xml","stg_11_npb4_17_garantia_fondo_xml",4,"tipo_fondo","XsString",NULL,3,NULL,"Código del tipo de fondo de garantía","Guarantee fund type code",NULL UNION ALL
    SELECT 11,"11.npb4_17_garantia_fondo.xml","NPB4-17","garantia_fondo.xml","XML",False,"raw_11_npb4_17_garantia_fondo_xml","stg_11_npb4_17_garantia_fondo_xml",5,"estado","XsString",NULL,1,NULL,"Estado de la garantía","Warranty Status",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 12. Expense types by reference (referencia_gasto.xml)
    SELECT 12,"12.npb4_17_referencia_gasto.xml","NPB4-17","referencia_gasto.xml","XML",False,"raw_12_npb4_17_referencia_gasto_xml","stg_12_npb4_17_referencia_gasto_xml",1,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera","Wallet type code",NULL UNION ALL
    SELECT 12,"12.npb4_17_referencia_gasto.xml","NPB4-17","referencia_gasto.xml","XML",False,"raw_12_npb4_17_referencia_gasto_xml","stg_12_npb4_17_referencia_gasto_xml",2,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo","Risk asset type code",NULL UNION ALL
    SELECT 12,"12.npb4_17_referencia_gasto.xml","NPB4-17","referencia_gasto.xml","XML",False,"raw_12_npb4_17_referencia_gasto_xml","stg_12_npb4_17_referencia_gasto_xml",3,"num_referencia","XsString",NULL,20,NULL,"Número de la referencia","Reference number",NULL UNION ALL
    SELECT 12,"12.npb4_17_referencia_gasto.xml","NPB4-17","referencia_gasto.xml","XML",False,"raw_12_npb4_17_referencia_gasto_xml","stg_12_npb4_17_referencia_gasto_xml",4,"codigo_gasto","XsString",NULL,3,NULL,"Código del uso del financiamiento o gastos","Financing or expense use code",NULL UNION ALL
    SELECT 12,"12.npb4_17_referencia_gasto.xml","NPB4-17","referencia_gasto.xml","XML",False,"raw_12_npb4_17_referencia_gasto_xml","stg_12_npb4_17_referencia_gasto_xml",5,"tipo_gasto","XsString",NULL,1,NULL,"Tipo de gasto","Expense type",NULL UNION ALL
    SELECT 12,"12.npb4_17_referencia_gasto.xml","NPB4-17","referencia_gasto.xml","XML",False,"raw_12_npb4_17_referencia_gasto_xml","stg_12_npb4_17_referencia_gasto_xml",6,"monto_gasto","XsDecimal",NULL,12,2,"Monto del gasto","Amount of expense",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 13. Units of measure funded by reference (referencia_unidad.xml)
    SELECT 13,"13.npb4_17_referencia_unidad.xml","NPB4-17","referencia_unidad.xml","XML",False,"raw_13_npb4_17_referencia_unidad_xml","stg_13_npb4_17_referencia_unidad_xml",1,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera","Wallet type code",NULL UNION ALL
    SELECT 13,"13.npb4_17_referencia_unidad.xml","NPB4-17","referencia_unidad.xml","XML",False,"raw_13_npb4_17_referencia_unidad_xml","stg_13_npb4_17_referencia_unidad_xml",2,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo","Risk asset type code",NULL UNION ALL
    SELECT 13,"13.npb4_17_referencia_unidad.xml","NPB4-17","referencia_unidad.xml","XML",False,"raw_13_npb4_17_referencia_unidad_xml","stg_13_npb4_17_referencia_unidad_xml",3,"num_referencia","XsString",NULL,20,NULL,"Número de la referencia","Reference number",NULL UNION ALL
    SELECT 13,"13.npb4_17_referencia_unidad.xml","NPB4-17","referencia_unidad.xml","XML",False,"raw_13_npb4_17_referencia_unidad_xml","stg_13_npb4_17_referencia_unidad_xml",4,"codigo_unidad","XsString",NULL,2,NULL,"Código de la unidad de medida","Measurement unit code",NULL UNION ALL
    SELECT 13,"13.npb4_17_referencia_unidad.xml","NPB4-17","referencia_unidad.xml","XML",False,"raw_13_npb4_17_referencia_unidad_xml","stg_13_npb4_17_referencia_unidad_xml",5,"cantidad_unidad","XsDecimal",NULL,10,2,"Cantidad de unidad de medida","Measurement unit quantity",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 14. References canceled due to refinancing (referencia_cancelada.xml)
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",1,"cod_cartera","XsString",NULL,2,NULL,"Código del tipo de cartera de la nueva referencia","Portfolio type code of the new reference",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",2,"cod_activo","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo de la nueva referencia","Code of the risk asset type of the new reference",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",3,"num_referencia","XsString",NULL,20,NULL,"Número de la nueva referencia","New reference number",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",4,"cod_cartera_canc","XsString",NULL,2,NULL,"Código del tipo de cartera de la referencia cancelada","Portfolio type code of the canceled reference",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",5,"cod_activo_canc","XsString",NULL,2,NULL,"Código del tipo de activo de riesgo de la referencia cancelada","Code of the risk asset type of the canceled reference",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",6,"num_referencia_canc","XsString",NULL,20,NULL,"Número de la referencia cancelada","Canceled reference number",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",7,"pago_capital","XsDecimal",NULL,12,2,"Ultimo pago a capital efectuado por el cliente al cancelar la deuda","Last capital payment made by the client when canceling the debt",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",8,"pago_interes","XsDecimal",NULL,12,2,"Ultimo pago a intereses efectuado por el cliente al cancelar la deuda","Last interest payment made by the client when canceling the debt",NULL UNION ALL
    SELECT 14,"14.npb4_17_referencia_cancelada.xml","NPB4-17","referencia_cancelada.xml","XML",False,"raw_14_npb4_17_referencia_cancelada_xml","stg_14_npb4_17_referencia_cancelada_xml",9,"saldo_total_interes","XsDecimal",NULL,12,2,"Saldo total de intereses cuando el cliente saldó la deuda","Total interest balance when the client paid off the debt",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 15. Partners of debtor companies (socios_sociedades.xml)
    SELECT 15,"15.npb4_17_socios_sociedades.xml","NPB4-17","socios_sociedades.xml","XML",False,"raw_15_npb4_17_socios_sociedades_xml","stg_15_npb4_17_socios_sociedades_xml",1,"nit_deudor","XsString",NULL,14,NULL,"NIT de la sociedad deudora","NIT of the debtor company",NULL UNION ALL
    SELECT 15,"15.npb4_17_socios_sociedades.xml","NPB4-17","socios_sociedades.xml","XML",False,"raw_15_npb4_17_socios_sociedades_xml","stg_15_npb4_17_socios_sociedades_xml",2,"nit_socio","XsString",NULL,14,NULL,"NIT del socio de la sociedad deudora","NIT of the partner of the debtor company",NULL UNION ALL
    SELECT 15,"15.npb4_17_socios_sociedades.xml","NPB4-17","socios_sociedades.xml","XML",False,"raw_15_npb4_17_socios_sociedades_xml","stg_15_npb4_17_socios_sociedades_xml",3,"porcentaje_participacion","XsDecimal",NULL,5,2,"Porcentaje de participación","Participation percentage",NULL UNION ALL

-- NPB4-17 - Risk Asset System - 16. Members of the board of directors of debtor companies (junta_directiva.xml)
    SELECT 16,"16.npb4_17_junta_directiva.xml","NPB4-17","junta_directiva.xml","XML",False,"raw_16_npb4_17_junta_directiva_xml","stg_16_npb4_17_junta_directiva_xml",1,"nit_deudor","XsString",NULL,14,NULL,"NIT de la sociedad deudora","NIT of the debtor company",NULL UNION ALL
    SELECT 16,"16.npb4_17_junta_directiva.xml","NPB4-17","junta_directiva.xml","XML",False,"raw_16_npb4_17_junta_directiva_xml","stg_16_npb4_17_junta_directiva_xml",2,"nit_miembro","XsString",NULL,14,NULL,"NIT del miembro de junta directiva de la sociedad deudora","NIT of the member of the board of directors of the debtor company",NULL UNION ALL
    SELECT 16,"16.npb4_17_junta_directiva.xml","NPB4-17","junta_directiva.xml","XML",False,"raw_16_npb4_17_junta_directiva_xml","stg_16_npb4_17_junta_directiva_xml",3,"cod_cargo","XsString",NULL,2,NULL,"Código de cargo del miembro de junta directiva de la sociedad deudora","Position code of the member of the board of directors of the debtor company",NULL UNION ALL
    SELECT 16,"16.npb4_17_junta_directiva.xml","NPB4-17","junta_directiva.xml","XML",False,"raw_16_npb4_17_junta_directiva_xml","stg_16_npb4_17_junta_directiva_xml",4,"fecha_inicial_jd","XsDate",NULL,NULL,NULL,"Fecha inicial de vigencia de la junta directiva","Initial effective date of the board of directors",NULL UNION ALL
    SELECT 16,"16.npb4_17_junta_directiva.xml","NPB4-17","junta_directiva.xml","XML",False,"raw_16_npb4_17_junta_directiva_xml","stg_16_npb4_17_junta_directiva_xml",5,"fecha_final_jd","XsDate",NULL,NULL,NULL,"Fecha final de vigencia de la junta directiva","Final effective date of the board of directors",NULL UNION ALL
    SELECT 16,"16.npb4_17_junta_directiva.xml","NPB4-17","junta_directiva.xml","XML",False,"raw_16_npb4_17_junta_directiva_xml","stg_16_npb4_17_junta_directiva_xml",6,"numero_credencial","XsString",NULL,17,NULL,"Número de inscripción de la credencial de junta directiva","Board of Directors Credential Registration Number",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 01. Catalog account balances (saldo_cuenta.xml)
    SELECT 1,"01.npb4_16_saldo_cuenta.xml","NPB4-16","saldo_cuenta.xml","XML",False,"raw_01_npb4_16_saldo_cuenta_xml","stg_01_npb4_16_saldo_cuenta_xml",1,"id_codigo_cuenta","XsString",NULL,10,0,"Cuenta Contable. Este valor no debe de modificarse","Accounting Account. This value should not be modified",NULL UNION ALL
    SELECT 1,"01.npb4_16_saldo_cuenta.xml","NPB4-16","saldo_cuenta.xml","XML",False,"raw_01_npb4_16_saldo_cuenta_xml","stg_01_npb4_16_saldo_cuenta_xml",2,"nom_cuenta","XsString",NULL,80,0,"Descripción de la cuenta contable. Este valor no debe de modificarse","Description of the accounting account. This value should not be modified",NULL UNION ALL
    SELECT 1,"01.npb4_16_saldo_cuenta.xml","NPB4-16","saldo_cuenta.xml","XML",False,"raw_01_npb4_16_saldo_cuenta_xml","stg_01_npb4_16_saldo_cuenta_xml",3,"valor","XsDecimal",NULL,17,2,"Saldo de la cuenta.","Account balance.",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 02. Deposits from 1st Foreign Banks. (deposito_extranjero.xml)
    SELECT 2,"02.npb4_16_deposito_extranjero.xml","NPB4-16","deposito_extranjero.xml","XML",False,"raw_02_npb4_16_deposito_extranjero_xml","stg_02_npb4_16_deposito_extranjero_xml",1,"id_codigo_banco","XsString",NULL,10,0,"Código del Banco de primera o no primera línea.","First or non-first line Bank code.",NULL UNION ALL
    SELECT 2,"02.npb4_16_deposito_extranjero.xml","NPB4-16","deposito_extranjero.xml","XML",False,"raw_02_npb4_16_deposito_extranjero_xml","stg_02_npb4_16_deposito_extranjero_xml",2,"nom_banco","XsString",NULL,80,0,"Nombre del banco de primera o no primera línea.","Name of the first or non-first line bank.",NULL UNION ALL
    SELECT 2,"02.npb4_16_deposito_extranjero.xml","NPB4-16","deposito_extranjero.xml","XML",False,"raw_02_npb4_16_deposito_extranjero_xml","stg_02_npb4_16_deposito_extranjero_xml",3,"pais","XsString",NULL,20,0,"País de origen del banco","Bank's country of origin",NULL UNION ALL
    SELECT 2,"02.npb4_16_deposito_extranjero.xml","NPB4-16","deposito_extranjero.xml","XML",False,"raw_02_npb4_16_deposito_extranjero_xml","stg_02_npb4_16_deposito_extranjero_xml",4,"categoria","XsString",NULL,2,0,"Categoría dentro de la que se clasifica al Banco. Para los Bancos de 1ª. Línea se aceptan los códigos ‘01’ y ‘02’. Para los de NO 1ª. Línea únicamente se admiten el código ‘00’.","Category within which the Bank is classified. For 1st Banks. Line codes '01' and '02' are accepted. For those of NO 1st. Line code '00' is only allowed.",NULL UNION ALL
    SELECT 2,"02.npb4_16_deposito_extranjero.xml","NPB4-16","deposito_extranjero.xml","XML",False,"raw_02_npb4_16_deposito_extranjero_xml","stg_02_npb4_16_deposito_extranjero_xml",5,"valor","XsDecimal",NULL,17,2,"Saldo del depósito en el banco de primera o no primera línea.","Deposit balance in the first or non-first line bank.",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 03. COES Extra-Accounting Information (dato_extracontable.xml)
    SELECT 3,"03.npb4_16_dato_extracontable.xml","NPB4-16","dato_extracontable.xml","XML",False,"raw_03_npb4_16_dato_extracontable_xml","stg_03_npb4_16_dato_extracontable_xml",1,"id_codigo_extracontable","XsString",NULL,10,0,"Cuenta de variable extracontable. Este valor no debe de modificarse","Extra-accounting variable account. This value should not be modified",NULL UNION ALL
    SELECT 3,"03.npb4_16_dato_extracontable.xml","NPB4-16","dato_extracontable.xml","XML",False,"raw_03_npb4_16_dato_extracontable_xml","stg_03_npb4_16_dato_extracontable_xml",2,"desc_extra_contable","XsString",NULL,80,0,"Descripción de la variable extracontable. Este valor no debe de modificarse","Description of the extra-accounting variable. This value should not be modified",NULL UNION ALL
    SELECT 3,"03.npb4_16_dato_extracontable.xml","NPB4-16","dato_extracontable.xml","XML",False,"raw_03_npb4_16_dato_extracontable_xml","stg_03_npb4_16_dato_extracontable_xml",3,"valor","XsDecimal",NULL,17,2,"Saldo extracontable","Off-accounting balance",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 04. Foreign Securities (titulo_valor_extranjero.xml)
    SELECT 4,"04.npb4_16_titulo_valor_extranjero.xml","NPB4-16","titulo_valor_extranjero.xml","XML",False,"raw_04_npb4_16_titulo_valor_extranjero_xml","stg_04_npb4_16_titulo_valor_extranjero_xml",1,"id_codigo_titulo_extranjero","XsString",NULL,10,0,"Código del Título Valor Extranjero. Este valor no debe de modificarse","Code of the Foreign Security Title. This value should not be modified",NULL UNION ALL
    SELECT 4,"04.npb4_16_titulo_valor_extranjero.xml","NPB4-16","titulo_valor_extranjero.xml","XML",False,"raw_04_npb4_16_titulo_valor_extranjero_xml","stg_04_npb4_16_titulo_valor_extranjero_xml",2,"desc_tv_extranj","XsString",NULL,254,0,"Descripción del Título Valor Extranjero. Este valor no debe de modificarse","Description of the Foreign Value Title. This value should not be modified",NULL UNION ALL
    SELECT 4,"04.npb4_16_titulo_valor_extranjero.xml","NPB4-16","titulo_valor_extranjero.xml","XML",False,"raw_04_npb4_16_titulo_valor_extranjero_xml","stg_04_npb4_16_titulo_valor_extranjero_xml",3,"valor_tv_extranj","XsDecimal",NULL,17,2,"Valor del título valor extranjero","Value of the foreign security",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 05. Loans Guaranteed by Deposits in Banks Foreign (prestamo_garantizado.xml)
    SELECT 5,"05.npb4_16_prestamo_garantizado.xml","NPB4-16","prestamo_garantizado.xml","XML",False,"raw_05_npb4_16_prestamo_garantizado_xml","stg_05_npb4_16_prestamo_garantizado_xml",1,"id_codigo_banco","XsString",NULL,10,0,"Código del Banco de primera línea.","First line bank code.",NULL UNION ALL
    SELECT 5,"05.npb4_16_prestamo_garantizado.xml","NPB4-16","prestamo_garantizado.xml","XML",False,"raw_05_npb4_16_prestamo_garantizado_xml","stg_05_npb4_16_prestamo_garantizado_xml",2,"nom_banco","XsString",NULL,80,0,"Nombre del banco de primera línea.","Name of the first line bank.",NULL UNION ALL
    SELECT 5,"05.npb4_16_prestamo_garantizado.xml","NPB4-16","prestamo_garantizado.xml","XML",False,"raw_05_npb4_16_prestamo_garantizado_xml","stg_05_npb4_16_prestamo_garantizado_xml",3,"pais","XsString",NULL,20,0,"País de origen del banco","Bank's country of origin",NULL UNION ALL
    SELECT 5,"05.npb4_16_prestamo_garantizado.xml","NPB4-16","prestamo_garantizado.xml","XML",False,"raw_05_npb4_16_prestamo_garantizado_xml","stg_05_npb4_16_prestamo_garantizado_xml",4,"categoria","XsString",NULL,2,0,"Categoría dentro de la que se clasifica al Banco. Para los Bancos de 1ª. Línea se aceptan los códigos ‘01’ y ‘02’.","Category within which the Bank is classified. For 1st Banks. Line codes '01' and '02' are accepted.",NULL UNION ALL
    SELECT 5,"05.npb4_16_prestamo_garantizado.xml","NPB4-16","prestamo_garantizado.xml","XML",False,"raw_05_npb4_16_prestamo_garantizado_xml","stg_05_npb4_16_prestamo_garantizado_xml",5,"valor","XsDecimal",NULL,17,2,"Valor del préstamo garantizado por el banco de primera línea.","Value of the loan guaranteed by the first-line bank.",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 06. Guarantees Guaranteed by Deposits in Banks Foreign (aval_garantizado.xml)
    SELECT 6,"06.npb4_16_aval_garantizado.xml","NPB4-16","aval_garantizado.xml","XML",False,"raw_06_npb4_16_aval_garantizado_xml","stg_06_npb4_16_aval_garantizado_xml",1,"id_codigo_banco","XsString",NULL,10,0,"Código del Banco de primera línea.","First line bank code.",NULL UNION ALL
    SELECT 6,"06.npb4_16_aval_garantizado.xml","NPB4-16","aval_garantizado.xml","XML",False,"raw_06_npb4_16_aval_garantizado_xml","stg_06_npb4_16_aval_garantizado_xml",2,"nom_banco","XsString",NULL,80,0,"Nombre del banco de primera línea.","Name of the first line bank.",NULL UNION ALL
    SELECT 6,"06.npb4_16_aval_garantizado.xml","NPB4-16","aval_garantizado.xml","XML",False,"raw_06_npb4_16_aval_garantizado_xml","stg_06_npb4_16_aval_garantizado_xml",3,"pais","XsString",NULL,20,0,"País de origen del banco","Bank's country of origin",NULL UNION ALL
    SELECT 6,"06.npb4_16_aval_garantizado.xml","NPB4-16","aval_garantizado.xml","XML",False,"raw_06_npb4_16_aval_garantizado_xml","stg_06_npb4_16_aval_garantizado_xml",4,"categoria","XsString",NULL,2,0,"Categoría dentro de la que se clasifica al Banco. Para los Bancos de 1ª. Línea se aceptan los códigos ‘01’ y ‘02’.","Category within which the Bank is classified. For 1st Banks. Line codes '01' and '02' are accepted.",NULL UNION ALL
    SELECT 6,"06.npb4_16_aval_garantizado.xml","NPB4-16","aval_garantizado.xml","XML",False,"raw_06_npb4_16_aval_garantizado_xml","stg_06_npb4_16_aval_garantizado_xml",5,"valor_aval_fianza","XsDecimal",NULL,17,2,"Valor del aval garantizado por el Banco de 1ª. Línea.","Value of the guarantee guaranteed by the 1st Bank. Line.",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 07. Subordinated debt (deuda_subordinada.xml)
    SELECT 7,"07.npb4_16_deuda_subordinada.xml","NPB4-16","deuda_subordinada.xml","XML",False,"raw_07_npb4_16_deuda_subordinada_xml","stg_07_npb4_16_deuda_subordinada_xml",1,"id_codigo_deuda","XsString",NULL,10,0,"Código asignado a la deuda subordinada según su periodo de vencimiento. Este valor no debe de modificarse","Code assigned to the subordinated debt according to its maturity period. This value should not be modified",NULL UNION ALL
    SELECT 7,"07.npb4_16_deuda_subordinada.xml","NPB4-16","deuda_subordinada.xml","XML",False,"raw_07_npb4_16_deuda_subordinada_xml","stg_07_npb4_16_deuda_subordinada_xml",2,"desc_deuda","XsString",NULL,80,0,"Descripción del código de la deuda subordinada. Este valor no debe de modificarse","Description of the subordinated debt code. This value should not be modified",NULL UNION ALL
    SELECT 7,"07.npb4_16_deuda_subordinada.xml","NPB4-16","deuda_subordinada.xml","XML",False,"raw_07_npb4_16_deuda_subordinada_xml","stg_07_npb4_16_deuda_subordinada_xml",3,"valor_deuda","XsDecimal",NULL,17,2,"Valor de la deuda subordinada","Value of subordinated debt",NULL UNION ALL

-- NPB4-16 - Information from the Accounting Statistician - 08. Projected Balance (balance_proyectado.xml)
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",1,"id_codigo_cuentaproy","XsString",NULL,10,0,"Cuenta Contable. Este valor no debe de modificarse","Accounting Account. This value should not be modified",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",2,"nom_cuentaproy","Caracter",NULL,80,0,"Descripción de la cuenta contable. Este valor no debe de modificarse","Description of the accounting account. This value should not be modified",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",3,"enero","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para enero","Projected account balance for January",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",4,"febrero","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para febrero","Projected account balance for February",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",5,"marzo","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para marzo","Projected account balance for March",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",6,"abril","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para abril","Projected account balance for April",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",7,"mayo","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para mayo","Projected account balance for May",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",8,"junio","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para junio","Projected account balance for June",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",9,"julio","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para julio","Projected account balance for July",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",10,"agosto","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para agosto","Projected account balance for August",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",11,"septiembre","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para septiembre","Projected account balance for September",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",12,"octubre","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para octubre","Projected account balance for October",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",13,"noviembre","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para noviembre","Projected account balance for November",NULL UNION ALL
    SELECT 8,"08.npb4_16_balance_proyectado.xml","NPB4-16","balance_proyectado.xml","XML",False,"raw_08_npb4_16_balance_proyectado_xml","stg_08_npb4_16_balance_proyectado_xml",14,"diciembre","XsDecimal",NULL,17,2,"Saldo de la cuenta proyectado para diciembre","Projected account balance for December",NULL UNION ALL

-- NPB4-12 - Shareholder System - 01. Details of shareholders, individuals and entities (persona.xml)
-- NPB4-12 - Shareholder System - 02. Details of shareholder certificates (certificado.xml)
-- NPB4-12 - Shareholder System - 03. Details of spouses, etc. of shareholders (pariente.xml)
-- NPB4-12 - Shareholder System - 04. Monthly share transfers (traspaso.xml)
-- NPB4-12 - Shareholder System - 05. Details of entities shareholders (socios_sociedad.xml)

-- NPB4-35 - Information on monetary deposits and securities - 01. Bank clients (CLIENTE.txt)
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",1,"cod_inst","char(4)",NULL,4,NULL,"Código asignado por la Superintendencia del Sistema Financiero al banco. Debiendo iniciar con las letras BC, para los bancos y FC para las financieras.","Code assigned by the Superintendency of the Financial System to the bank. Must start with the letters BC, for banks and FC for financial companies.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",2,"niu_banco","char(20)",NULL,20,NULL,"Número Identificación Único asignado por la institución a un determinado Cliente","Unique Identification Number assigned by the institution to a specific Client",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",3,"pri_nomb","char(60)",NULL,60,NULL,"Primer nombre de la persona natural, cliente de la Institución. Al menos deberá existir el primer nombre de la persona. Deberá venir completo, no abreviar y no sustituirse por la inicial y un punto.","First name of the natural person, client of the Institution. At least the person's first name must exist. It must be complete, not abbreviated and not replaced by the initial and a period.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",4,"seg_nomb","char(30)",NULL,30,NULL,"Segundo nombre de la persona natural cliente del banco","Second name of the natural person who is a client of the bank",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",5,"ter_nomb","char(30)",NULL,30,NULL,"Tercer nombre de la persona natural del cliente, en caso de existir","Third name of the client's natural person, if applicable",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",6,"pri_apel","char(30)",NULL,30,NULL,"Primer Apellido de la persona natural, cliente del banco. Al igual que el primer nombre deberá existir primer apellido. Deberá venir completo, no abreviar y no sustituirse por la inicial y un punto.","First surname of the natural person, a bank customer. Just like the first name, there must be a first surname. It must be complete, not abbreviated and not replaced by the initial and a period.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",7,"seg_apel","char(30)",NULL,30,NULL,"Segundo apellido de la persona natural, cliente de la Institución. Al igual que el primer apellido deberá existir segundo apellido. Deberá venir completo, no abreviar y no sustituirse por la inicial y un punto.","Second surname of the natural person, client of the Institution. Like the first surname, there must be a second surname. It must be complete, not abbreviated and not replaced by the initial and a period.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",8,"ape_cas","char(30)",NULL,30,NULL,"Apellido de casada de la persona natural, si en caso lo hubiera. Deberá venir completo, no abreviar y no sustituirse por la inicial y un punto.","Married surname of the natural person, if applicable. It must be complete, not abbreviated and not replaced by the initial and a period.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",9,"razon_so","char(80)",NULL,80,NULL,"Razón Social. Este campo tendrá datos si el tipo de persona es persona jurídica o el nombre de una entidad sin personería jurídica, de lo contrario deberá estar vacío.","Business name. This field will have data if the type of person is a legal entity or the name of an entity without legal personality, otherwise it must be empty.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",10,"tipo_pers","char(1)",NULL,1,NULL,"Tipo de Persona","Kind of person",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",11,"nacional","char(4)",NULL,4,NULL,"Nacionalidad","Nationality",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",12,"cod_acti","char(6)",NULL,6,NULL,"Giro o Actividad Económica de la empresa o persona natural, se deberá indicar la actividad principal de la empresa o la actividad que le genera el mayor flujo de caja, debiendo coincidir dicho código con el asignado por el Ministerio de Hacienda, para efectos de la declaración del Impuesto a la Transferencia de Bienes Muebles y a la Prestación de Servicios.","Business or Economic Activity of the company or natural person, the main activity of the company or the activity that generates the greatest cash flow must be indicated, and said code must coincide with the one assigned by the Ministry of Finance, for the purposes of the declaration. of the Tax on the Transfer of Movable Property and the Provision of Services.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",13,"sector","char(6)",NULL,6,NULL,"Código de Sector Geográfico","Geographic Sector Code",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",14,"cod_dpto","char(2)",NULL,2,NULL,"Código del Departamento","Department Code",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",15,"cod_muni","char(2)",NULL,2,NULL,"Código del Municipio","Municipal Code",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",16,"Direcc","char(100)",NULL,100,NULL,"Dirección de residencia o domicilio","Residence or domicile address",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",17,"no_telef","char(10)",NULL,10,NULL,"No de teléfono","phone number",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",18,"tel_ofic","char(10)",NULL,10,NULL,"Número de teléfono de la oficina","Office phone number",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",19,"sec_inst","char(6)",NULL,6,NULL,"Sector Institucional","Institutional Sector",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",20,"es_resid","boolean",NULL,NULL,NULL,"Valor lógico o bandera utilizada para indicar si la persona es residente o no en el país, se considerarán no residentes o no domiciliados a las personas que residen en el extranjero y que tienen su centro de interés económico fuera del país.","Logical value or flag used to indicate whether the person is a resident or not in the country, people who reside abroad and who have their center of economic interest outside the country will be considered non-residents or non-domiciled.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",21,"tipo_sec","char(1)",NULL,1,NULL,"Tipo de Sector público o privado","Type of public or private sector",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",22,"fec_nac","date",NULL,NULL,NULL,"Fecha Nacimiento","Birth date",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",23,"genero","char(1)",NULL,1,NULL,"","",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",24,"est_civil","char(1)",NULL,1,NULL,"Estado Civil","Civil status",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",25,"clas_r","char(2)",NULL,2,NULL,"Código de la Clasificación de Riesgo crediticio asignado al cliente. Debe digitarse en mayúscula, de acuerdo a la codificación establecida por la SSF.","Credit Risk Classification code assigned to the client. It must be typed in capital letters, according to the coding established by the SSF.",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",26,"tip_rela","char(1)",NULL,1,NULL,"Tipo de relación del cliente con el Banco","Type of customer relationship with the Bank",NULL UNION ALL
    SELECT 1,"01.npb4_35_CLIENTE.txt","NPB4-35","CLIENTE.txt","TXT",False,"raw_01_npb4_35_CLIENTE_txt","stg_01_npb4_35_CLIENTE_txt",27,"agencia","char(7)",NULL,7,NULL,"Agencia donde atienden al cliente","Agency where they serve the client",NULL UNION ALL

-- NPB4-35 - Information on monetary deposits and securities - 02. Bank deposits and other captures (DEPÓSITOS Y OTRAS CAPTACIONES.txt)
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",1,"cod_inst","char(4)",NULL,4,NULL,"Código del banco","Bank code",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",2,"niu_banc","char(20)",NULL,20,NULL,"Número Identificación Único de Cliente","Unique Customer Identification Number",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",3,"cod_prod","char(4)",NULL,4,NULL,"Código del producto","Product code",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",4,"num_cta","char(20)",NULL,20,NULL,"No. de Cuenta o Certificado de Depósito, éste deberá justificarse a la izquierda y sin guiones.","Account No. or Certificate of Deposit, this must be justified on the left and without hyphens.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",5,"agencia","char(7)",NULL,7,NULL,"Código de la agencia o sucursal del banco, éste deberá justificarse a la izquierda y sin guiones.","Code of the bank's agency or branch, this must be justified on the left and without hyphens.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",6,"tip_peri","char(1)",NULL,1,NULL,"Periodicidad de pago de intereses","Frequency of interest payments",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",7,"tas_vigen","numeric(10,6)",NULL,10,6,"Tasa de interés vigente al cierre.","Interest rate in effect at closing.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",8,"tas_inic","numeric(10,6)",NULL,10,6,"Porcentaje de tasa inicial.","Initial rate percentage.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",9,"fe_in_ts","Date",NULL,NULL,NULL,"Fecha inicio tasa vigente.","Current rate start date.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",10,"fec_fin_ts","Date",NULL,NULL,NULL,"Fecha final de tasa vigente.","Final date of current rate.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",11,"tip_tasa","char(2)",NULL,2,NULL,"Tipo de tasa de interés.","Type of interest rate.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",12,"for_pg_I","char(2)",NULL,2,NULL,"Forma de pago de Interés estandarizado (cheque, abono en cuenta, etc)","Standardized interest payment method (check, account credit, etc.)",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",13,"tas_ref","numeric(10,6)",NULL,10,6,"Porcentaje de tasa de referencia","Reference rate percentage",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",14,"sobretas","numeric(10,6)",NULL,10,6,"Porcentaje de sobretasa (para depósitos a plazo y/o certificados de inversión)","Surcharge percentage (for time deposits and/or investment certificates)",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",15,"dia_cort","date",NULL,NULL,NULL,"Día de corte","Cutting day",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",16,"porc_com","numeric(10,6)",NULL,10,6,"Porcentaje de comisión (por inactividad, etc)","Commission percentage (for inactivity, etc.)",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",17,"tip_titul","char(1)",NULL,1,NULL,"Tipo de titularidad","Ownership type",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",18,"num_tit","smallint",NULL,NULL,NULL,"Número de Titulares registrados en el depósito. Debiendo estar todos los titulares registrados en la tabla correspondiente a CLIENTES","Number of Holders registered in the deposit. All owners must be registered in the table corresponding to CUSTOMERS",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",19,"plaz_cta","char(8)",NULL,8,NULL,"Plazo de la Cuenta","Account Term",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",20,"form_emi","char(4)",NULL,4,NULL,"Forma de Emisión","Form of Issuance",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",21,"serie","varchar(12)",NULL,12,NULL,"Serie","Series",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",22,"cond_esp","Char(1)",NULL,1,NULL,"Condiciones especiales","Special conditions",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",23,"fec_aper","Date",NULL,NULL,NULL,"Fecha de apertura de la cuenta o depósito","Account or deposit opening date",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",24,"fec_venc","Date",NULL,NULL,NULL,"Fecha de vencimiento","Due date",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",25,"mont_min","numeric(15,2)",NULL,15,2,"Monto mínimo de apertura de la cuenta o depósito.","Minimum account opening or deposit amount.",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",26,"cta_cont","char(20)",NULL,20,NULL,"Código de la cuenta contable del producto","Product accounting account code",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",27,"fdos_com","numeric(15,2)",NULL,15,2,"Fondos en compensación","Compensation funds",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",28,"fdos_res","numeric(15,2)",NULL,15,2,"Fondos restringidos","Restricted funds",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",29,"tip_rest","char(2)",NULL,2,NULL,"Tipo de restricción","Restriction type",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",30,"tran_pen","numeric(15,2)",NULL,15,2,"Transacciones pendientes","Pending transactions",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",31,"emis_neg","char(1)",NULL,1,NULL,"Negociabilidad de la emisión","Negotiability of the issue",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",32,"fec_ul_tr","Date",NULL,NULL,NULL,"Fecha de la última transacción efectuada","Date of the last transaction carried out",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",33,"sald_cap","numeric(15,2)",NULL,15,2,"Saldo de capital, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\" .","Capital balance must be unmasked only with the decimal point, as follows: \"************.99\".",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",34,"sald_int","numeric(15,2)",NULL,15,2,"Saldo de intereses, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\" .","Interest balance must be unmasked only with the decimal point, as follows: \"************.99\".",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",35,"mora_sgi","numeric(12,2)",NULL,12,2,"Saldo de la mora por sobregiro, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"999999999.99\" .","Balance of the arrears due to overdraft, must be unmasked only with the decimal point, as follows \"999999999.99\".",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",36,"saldo_tot","numeric(15,2)",NULL,15,2,"Saldo total del depósito, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\" .","Total deposit balance must be unmasked only with the decimal point, as follows: \"************.99\".",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",37,"tip_o_elec.","char(1)",NULL,1,NULL,"Tipo de operaciones electrónicas que permite la institución","Type of electronic operations allowed by the institution",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",38,"med_o_el","char(3)",NULL,3,NULL,"Medio para realizar operaciones electrónicas","Means to carry out electronic operations",NULL UNION ALL
    SELECT 2,"02.npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","NPB4-35","DEPÓSITOS_Y_OTRAS_CAPTACIONES.txt","TXT",False,"raw_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt","stg_02_npb4_35_DEPÓSITOS_Y_OTRAS_CAPTACIONES_txt",39,"estado","char(1)",NULL,1,NULL,"Estado del depósito, representa el estado en que se encuentra el depósito. Los estados a los que deberá homologarse son los que aparecen en la columna Requerido de esta tabla.","Deposit status, represents the state in which the deposit is. The states that must be approved are those that appear in the Required column of this table.",NULL UNION ALL

-- NPB4-35 - Information on monetary deposits and securities - 03. Identification documents of the clients (DOCUMENTOS CLIENTE.txt)
    SELECT 3,"03.npb4_35_DOCUMENTOS_CLIENTE.txt","NPB4-35","DOCUMENTOS_CLIENTE.txt","TXT",False,"raw_03_npb4_35_DOCUMENTOS_CLIENTE_txt","stg_03_npb4_35_DOCUMENTOS_CLIENTE_txt",1,"cod_inst","char(4)",NULL,4,NULL,"Código del banco","Bank code",NULL UNION ALL
    SELECT 3,"03.npb4_35_DOCUMENTOS_CLIENTE.txt","NPB4-35","DOCUMENTOS_CLIENTE.txt","TXT",False,"raw_03_npb4_35_DOCUMENTOS_CLIENTE_txt","stg_03_npb4_35_DOCUMENTOS_CLIENTE_txt",2,"niu_banc","char(20)",NULL,20,NULL,"Número Identificación Único de Cliente","Unique Customer Identification Number",NULL UNION ALL
    SELECT 3,"03.npb4_35_DOCUMENTOS_CLIENTE.txt","NPB4-35","DOCUMENTOS_CLIENTE.txt","TXT",False,"raw_03_npb4_35_DOCUMENTOS_CLIENTE_txt","stg_03_npb4_35_DOCUMENTOS_CLIENTE_txt",3,"cod_doc","char(5)",NULL,5,NULL,"Código del Documento","Document Code",NULL UNION ALL
    SELECT 3,"03.npb4_35_DOCUMENTOS_CLIENTE.txt","NPB4-35","DOCUMENTOS_CLIENTE.txt","TXT",False,"raw_03_npb4_35_DOCUMENTOS_CLIENTE_txt","stg_03_npb4_35_DOCUMENTOS_CLIENTE_txt",4,"num_doc","char(25)",NULL,25,NULL,"Número de Documento, éste deberá justificarse a la izquierda y sin guiones.","Document Number, this must be justified to the left and without hyphens.",NULL UNION ALL

-- NPB4-35 - Information on monetary deposits and securities - 04. Data on the bank's officials and employees (FUNCIONARIOS Y EMPLEADOS.txt)
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",1,"cod_inst","char(4)",NULL,4,NULL,"Código del banco para la cual trabaja.","Bank code for which you work.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",2,"nit","char(25)",NULL,25,NULL,"Es el Número de Identificación Tributaria que posee el empleado.","It is the Tax Identification Number that the employee has.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",3,"pri_nomb","char(20)",NULL,20,NULL,"Primer nombre de la persona que labora en la Institución financiera. Al menos deberá existir el primer nombre de la persona.","First name of the person who works at the financial institution. At least the person's first name must exist.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",4,"seg_nomb","char(20)",NULL,20,NULL,"Segundo nombre de la persona que labora en la Institución financiera. Al igual que el primer nombre deberá existir primer apellido.","Second name of the person who works at the financial institution. Just like the first name, there must be a first surname.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",5,"pri_apel","char(20)",NULL,20,NULL,"Primer apellido de la persona que trabaja en la institución. Deberá existir el primer apellido de la persona.","First surname of the person who works at the institution. The person's first surname must exist.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",6,"seg_apel","char(20)",NULL,20,NULL,"Segundo apellido","Second surname",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",7,"ape_casada","char(20)",NULL,20,NULL,"Apellido de casada, en caso se tenga.","Married surname, if available.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",8,"fec_ingre","Date",NULL,NULL,NULL,"Fecha de Ingreso al banco","Bank Entry Date",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",9,"cargo","char(50)",NULL,50,NULL,"Ultimo cargo desempeñado en el banco.","Last position held at the bank.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",10,"niu","char(25)",NULL,25,NULL,"Código único del empleado como cliente de la IMI","Unique code of the employee as an IMI client",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",11,"dui","char(25)",NULL,25,NULL,"Documento único de identidad del empleado.","Unique identity document of the employee.",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",12,"telef","char(10)",NULL,10,NULL,"Número telefónico registrado por el empleado en la Institución","Telephone number registered by the employee in the Institution",NULL UNION ALL
    SELECT 4,"04.npb4_35_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-35","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_04_npb4_35_FUNCIONARIOS_Y_EMPLEADOS_txt",13,"depto","char(25)",NULL,25,NULL,"Departamento al que pertenece el empleado","Department to which the employee belongs",NULL UNION ALL

-- NPB4-35 - Information on monetary deposits and securities - 05. Data on the bank's shareholders as required by NPB4-12 norms (ACCIONISTAS.txt)
-- NPB4-35 - Information on monetary deposits and securities - 06. Information on risk assets as required by NPB4-17 norms (ACTIVOS DE RIESGO.txt)
-- NPB4-35 - Information on monetary deposits and securities - 07. NPB4-16 reports

-- NPB3-11 - Liquid assets - 01. REQUERIMIENTO DE ACTIVOS LIQUIDOS Y REPORTE DE INVERSIONES (pdf?)
-- NPB3-11 - Liquid assets - 02. DETALLE DE DEPÓSITOS AL CIERRE DEL MES (pdf, excel?)
-- NPB3-11 - Liquid assets - 03. DETALLE DE OBLIGACIONES (pdf, excel, paper?)

-- NPB4-44 - Guaranteed deposits - 01. Information about the bank's clients (CLIENTES.txt)
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",1,"NIU","Alphanumeric","25(A)",25,NULL,"Número de Identificación Único asignado por la entidad a un determinado Cliente","Unique Identification Number assigned by the entity to a specific Client",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",2,"Primer Nombre","Alphanumeric","30(A)",30,NULL,"Primer nombre de la persona natural, cliente de la entidad. Al menos deberá existir el primer nombre de la persona","First name of the natural person, client of the entity. At least the first name of the person must exist",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",3,"Segundo Nombre","Alphanumeric","30(A)",30,NULL,"Segundo nombre de la persona natural, cliente de la entidad en caso posea","Second name of the natural person, client of the entity in case it has",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",4,"Tercer Nombre","Alphanumeric","30(A)",30,NULL,"Tercer nombre de la persona natural, cliente de la entidad, en caso posea","Third name of the natural person, client of the entity, if they have",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",5,"Primer Apellido","Alphanumeric","30(A)",30,NULL,"Primer Apellido de la persona natural, cliente de la entidad. Al igual que el primer nombre deberá existir primer apellido. Deberá venir completo, no abreviar y no sustituirse por la inicial y un punto","First Surname of the natural person, client of the entity. Just like the first name, there must be a first surname. It must be complete, not abbreviated and not replaced by the initial and a period",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",6,"Segundo Apellido","Alphanumeric","30(A)",30,NULL,"Segundo apellido de la persona natural, cliente de la Entidad. Al igual que el primer apellido deberá existir segundo apellido. En caso de existir deberá venir completo, no abreviar y no sustituirse por la inicial y un punto","Second surname of the natural person, client of the Entity. Just like the first surname, there must be a second surname. If it exists, it must be complete, not abbreviated and not replaced by the initial and a period.",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",7,"Apellido de casada","Alphanumeric","30(A)",30,NULL,"Apellido de casada de la persona natural, si en caso lo hubiera. Deberá venir completo, no abreviar y no sustituirse por la inicial y un punto","Married surname of the natural person, if applicable. It must be complete, not abbreviated and not replaced by the initial and a period",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",8,"Razón social","Alphanumeric","80(A)",80,NULL,"Razón Social. Este campo tendrá datos si el tipo de persona es persona jurídica para las personas naturales deberá estar vacío","Business name. This field will have data if the type of person is a legal entity, for natural persons it must be empty",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",9,"Tipo de persona","Alphanumeric","A",1,NULL,"Tipo de Persona 1 = Natural 2 = Jurídica 3 = Patrimonio 4 = Otros","Type of Person 1 = Natural 2 = Legal 3 = Heritage 4 = Others",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",10,"Nacionalidad","Alphanumeric","4(A)",4,NULL,"Nacionalidad De acuerdo a NPB4-","Nationality According to NPB4-",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",11,"Actividad Económica","Alphanumeric","6(A)",6,NULL,"Giro o Actividad Económica de la empresa o persona natural, se deberá indicar la actividad principal de la empresa o la actividad que le genera el mayor flujo de caja, debiendo coincidir dicho código con el asignado por el Ministerio de Hacienda, para efectos de la declaración del Impuesto a la Transferencia de Bienes Muebles y a la Prestación de Servicios","Business or Economic Activity of the company or natural person, the main activity of the company or the activity that generates the greatest cash flow must be indicated, and said code must coincide with the one assigned by the Ministry of Finance, for the purposes of the declaration. of the Tax on the Transfer of Movable Property and the Provision of Services",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",12,"Sector Geográfico","Alphanumeric","6(A)",6,NULL,"Código de Sector Geográfico, de acuerdo al lugar de residencia. De acuerdo a NPB4-","Geographic Sector Code, according to the place of residence. According to NPB4-",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",13,"Departamento","Alphanumeric","2(A)",2,NULL,"Código del Departamento. De acuerdo a NPB4-","Department Code. According to NPB4-",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",14,"Municipio","Alphanumeric","2(A)",2,NULL,"Código del Municipio De acuerdo a NPB4-","Municipal Code According to NPB4-",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",15,"Dirección","Alphanumeric","100(A)",100,NULL,"Deberá tener la dirección de residencia o domicilio, sea persona natural o jurídica","You must have the address of residence or domicile, whether a natural or legal person.",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",16,"Número de teléfono fijo","Alphanumeric","30(A)",30,NULL,"Número de teléfono del domicilio del particular, y en su caso, de la oficina","Telephone number of the individual's home, and, if applicable, the office",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",17,"Número de celular","Alphanumeric","30(A)",30,NULL,"Celular del titular","Owner's cell phone",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",18,"Correo electrónico","Alphanumeric","50(A)",50,NULL,"Dirección de correo electrónico proporcionado por el Titular que tenga registrada en la institución","Email address provided by the Owner that is registered with the institution",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",19,"Sector Institucional","Alphanumeric","6(A)",6,NULL,"Sector Institucional De acuerdo a NPB4-32. Anexo D","Institutional Sector According to NPB4-32. Annex D",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",20,"Es residente","Alphanumeric","A",1,NULL,"Valor lógico o bandera utilizada para indicar si la persona es residente o no en el país, se considerarán no residentes o no domiciliados a las personas que residen en el extranjero y que tienen su centro de interés económico fuera del país. 0= No Residente 1 = Residente (default)","Logical value or flag used to indicate whether the person is a resident or not in the country, people who reside abroad and who have their center of economic interest outside the country will be considered non-residents or non-domiciled. 0= Non-Resident 1 = Resident (default)",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",21,"Tipo de sector","Alphanumeric","A",1,NULL,"Tipo de Sector público o privado, entendiéndose como público las instituciones gubernamentales. 1=Privado 2 = Público","Type of public or private sector, with public being understood as government institutions. 1=Private 2 = Public",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",22,"Fecha de Nacimiento","Date","AAAAMMDD",8,NULL,"Fecha Nacimiento para las personas naturales y fecha de constitución para las personas jurídicas","Date of Birth for natural persons and date of incorporation for legal entities",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",23,"Género","Alphanumeric","A",1,NULL,"Para las personas jurídicas deberá personas naturales según sea el caso M = Masculino F = Femenino N = No Aplica","For legal entities, it must be natural persons as the case may be M = Male F = Female N = Not Applicable",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",24,"Estado civil","Alphanumeric","A",1,NULL,"Estado Civil para las personas jurídicas para las personas naturales según sea el caso. 1 = Solter(@) 2 = Casad(@) 3 = Acompañad(@) 4 = Viud@ 5 = Divorciad@ 6 = No aplica","Civil Status for legal entities for natural persons as the case may be. 1 = Single(@) 2 = Married(@) 3 = Accompany(@) 4 = Widowed 5 = Divorced 6 = Not applicable",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",25,"Clasificación de Riesgo","Alphanumeric","2(A)",2,NULL,"Código de la Clasificación de Riesgo crediticio asignado al cliente. Debe digitarse en mayúscula, de acuerdo a la codificación establecida por la SSF. Los clientes que no poseen operaciones activas con la entidad deberán asignársele NA Tabla 3 de la NPB4-17","Credit Risk Classification code assigned to the client. It must be typed in capital letters, according to the coding established by the SSF. Clients who do not have active operations with the entity must be assigned NA Table 3 of the NPB4-17",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",26,"Tipo de relación","Alphanumeric","A",1,NULL,"Tipo de relación del cliente con la entidad. 0 = No Relacionado 1 = Relacionado por Administración 2 = Relacionado por Propiedad 3 = Conglomerado Financiero 4 = Grupo Empresarial 5= Otros","Type of relationship between the client and the entity. 0 = Not Related 1 = Related by Administration 2 = Related by Ownership 3 = Financial Conglomerate 4 = Business Group 5 = Others",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",27,"Agencia","Alphanumeric","7(A)",7,NULL,"Código de la agencia o sucursal de la entidad donde atienden al cliente, éste deberá justificarse a la izquierda y sin guiones. Estos deberán estar detallados en tabla de agencias contenidas en estas Norma","Code of the agency or branch of the entity where they serve the client, this must be justified on the left and without hyphens. These must be detailed in the table of agencies contained in these Standards.",NULL UNION ALL
    SELECT 1,"01.npb4_44_CLIENTES.txt","NPB4-44","CLIENTES.txt","TXT",False,"raw_01_npb4_44_CLIENTES_txt","stg_01_npb4_44_CLIENTES_txt",28,"Saldo garantizado","Numeric","15(X).XX",18,NULL,"Saldo hasta el límite garantizado por el IGD (Sería la garantía bruta). Sumatoria de depósitos hasta por el valor del límite de la garantía por persona de conformidad a lo establecido en el Artículo 167 de la ley de Bancos y al artículo 6 de la Técnicas para Informar los Depósitos","Balance up to the limit guaranteed by the IGD (It would be the gross guarantee). Sum of deposits up to the value of the guarantee limit per person in accordance with the provisions of Article 167 of the Banking Law and Article 6 of the Techniques for Reporting Deposits",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 02. Details of the deposits in the institution (DEPÓSITOS.txt)
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",1,"NIU","Alphanumeric","25(A)",25,NULL,"Número de Identificación Único asignado por la entidad a un determinado cliente.","Unique Identification Number assigned by the entity to a specific client.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",2,"Código del Producto","Alphanumeric","4(A)",4,NULL,"Código del Producto o tipo de depósitos establecidos por la entidad los cuales deberán estar definidos dentro del catálogo de productos.","Product code or type of deposits established by the entity which must be defined within the product catalog.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",21,"Fecha de vencimiento","Date","AAAAMMDD",8,NULL,"Fecha de vencimiento del depósito, para ahorro y corriente se pondrá la fecha de fin de mes que se está reportando.","Deposit expiration date, for savings and current the date of the end of the month being reported will be set.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",3,"Número de cuenta","Alphanumeric","20(A)",20,NULL,"No. De cuenta o Certificado de Depósito, éste deberá justificarse a la izquierda y sin guiones. Este campo debe ser único en la tabla (No se permite dos números de cuenta iguales).","No. Account or Certificate of Deposit, this must be justified on the left and without hyphens. This field must be unique in the table (No two identical account numbers are allowed).",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",4,"Agencia","Alphanumeric","7(A)",7,NULL,"Código de la agencia o sucursal de la entidad, éste deberá justificarse a la izquierda y sin guiones.","Code of the agency or branch of the entity, this must be justified on the left and without hyphens.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",5,"Tipo de Periodicidad","Alphanumeric","A",1,NULL,"Periodicidad del pago de intereses, el cual dependerá del producto contratado. A= anual E=semestral T=trimestral B=bimestral M=mensual Q=quincenal S=semanal D=diario V=al vencimiento P=pactada O=otras","Frequency of interest payment, which will depend on the contracted product. A= annual E=semi-annual T=quarterly B=bi-monthly M=monthly Q=biweekly S=weekly D=daily V=due P=agreed O=other",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",6,"Tasa vigente","Numeric","10(X).XX",13,NULL,"Tasa de Interés vigente al cierre de mes que se está procesando. Debe ser mayor a cero a excepción de las cuentas corrientes.","Interest rate in effect at the end of the month being processed. It must be greater than zero except for checking accounts.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",7,"Tasa inicial","Numeric","10(X).XX",13,NULL,"Tasa con la que fue abierta la cuenta o depósito. Debe ser mayor a cero a excepción de las cuentas corrientes.","Rate with which the account or deposit was opened. It must be greater than zero except for checking accounts.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",8,"Fecha inicial de tasa","Date","AAAAMMDD",8,NULL,"Fecha en la cual inició la tasa aplicada actualmente. Para el caso de cuentas corrientes que no devengan intereses se colocará la fecha de apertura del depósito.","Date on which the currently applied rate began. In the case of current accounts that do not earn interest, the deposit opening date will be entered.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",9,"Fecha fin de tasa","Date","AAAAMMDD",8,NULL,"Fecha futura en la que vencerá la tasa del depósito. Para ahorro y corriente será la fecha de cierre.","Future date on which the deposit rate will expire. For savings and current it will be the closing date.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",10,"Tipo de tasa","Alphanumeric","2(A)",2,NULL,"Si la tasa es fija, variable o escalonada, deberá identificar como variable. FI = fija VA = variable","If the rate is fixed, variable or graduated, it must be identified as variable. FI = fixed VA = variable",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",11,"Forma de pago de interés","Alphanumeric","2(A)",2,NULL,"Forma de pago de Interés estandarizado (cheque, abono en cuenta, efectivo u otros). CH = cheque EF =efectivo AB = abono a cuenta OT = otros","Standardized Interest payment method (check, account credit, cash or others). CH = check EF = cash AB = credit to account OT = others",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",12,"Tasa de referencia","Numeric","10(X).XX",13,NULL,"Tasa de referencia al cierre de mes que se procesa, se refiere a la tasa de referencia propia de la entidad.","Reference rate at the end of the month being processed, refers to the entity's own reference rate.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",13,"Porcentaje a pagar por intereses","Numeric","10(X).XX",13,NULL,"Porcentaje adicional a pagar por intereses, con respecto de la tasa de referencia.","Additional percentage to pay for interest, with respect to the reference rate.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",14,"Dia de corte","Date","AAAAMMDD",8,NULL,"Día de corte, para todos los registros es la fecha correspondiente al fin de mes reportado.","Cut-off day, for all records, is the date corresponding to the end of the reported month.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",15,"Porcentaje de comisión","Numeric","10(X).XX",13,NULL,"Valor de comisión cobrado por la entidad, en caso aplique.","Commission value charged by the entity, if applicable.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",16,"Tipo de titularidad","Alphanumeric","A",1,NULL,"Tipo de titularidad del depósito. 1=Único (default) 2 = Co Propiedad 3 = Propiedad Alternativa.","Deposit ownership type. 1=Single (default) 2 = Co Property 3 = Alternative Property.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",17,"Número de titulares","Numeric","X",1,NULL,"Número de Titulares registrados en el depósito. Debiendo estar todos los titulares registrados en la tabla correspondiente a CLIENTES. Debe ser un entero mayor a cero, y debe estar de acuerdo al tipo de titularidad.","Number of Holders registered in the deposit. All owners must be registered in the table corresponding to CUSTOMERS. It must be an integer greater than zero, and must be according to the type of ownership.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",18,"Plazo de la Cuenta","Alphanumeric","8(A)",8,NULL,"Plazo de la Cuenta. Para las cuentas de ahorro y corriente.","Account Term. For savings and checking accounts.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",19,"Condiciones especiales","Alphanumeric","A",1,NULL,"Condiciones especiales 1 = Ninguna 2 = Embargada 3 = Restringida por orden judicial o fiscal 4 = Dado en garantía 5 = Otros.","Special conditions 1 = None 2 = Embargoed 3 = Restricted by court or fiscal order 4 = Given as guarantee 5 = Others.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",20,"Fecha de apertura","Date","AAAAMMDD",8,NULL,"Fecha de apertura de la cuenta o depósito.","Account or deposit opening date.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",22,"Monto mínimo","Numeric","15(X).XX",18,NULL,"Monto mínimo de apertura de la cuenta o depósito.","Minimum account opening or deposit amount.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",23,"Código de la cuenta contable","Alphanumeric","20(A)",20,NULL,"Código de la cuenta contable del producto De acuerdo a Catálogo de cuenta del Manual de Contabilidad aprobado por el Banco Central . Mínimo de 10 dígitos.","Product accounting account code According to the Account Catalog of the Accounting Manual approved by the Central Bank. Minimum of 10 digits.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",24,"Fondos en compensación","Numeric","15(X).XX",18,NULL,"Fondos en compensación.","Compensation funds.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",25,"Fondos restringidos","Numeric","15(X).XX",18,NULL,"Fondos restringidos.","Restricted funds.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",26,"Transacciones pendientes","Numeric","15(X).XX",18,NULL,"Transacciones pendientes de registrar en caso aplique.","Transactions pending registration, if applicable.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",27,"Negociabilidad del depósito","Alphanumeric","A",1,NULL,"Negociabilidad del depósito Los valores que pueden tomar son: 0 No Negociable 1 Negociable (Certificado de depósito negociable).","Negotiability of the deposit The values ​​that can be taken are: 0 Non-Negotiable 1 Negotiable (Negotiable certificate of deposit).",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",28,"Moneda","Alphanumeric","3(A)",3,NULL,"Tipo de moneda en la que fue aperturado el depósito.","Type of currency in which the deposit was opened.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",29,"Saldo del depósito en la moneda original de apertura","Numeric","15(X).XX",18,NULL,"Saldo del depósito en la moneda original de apertura.","Deposit balance in the original opening currency.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",30,"Fecha de la última transacción","Date","AAAAMMDD",8,NULL,"Fecha de la última transacción efectuada, para los depósitos a plazo será el vencimiento del depósito.","Date of the last transaction carried out, for term deposits it will be the maturity of the deposit.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",31,"Saldo de capital","Numeric","15(X).XX",18,NULL,"Saldo de capital, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\" deberá estar en dólares.","Capital balance must be unmasked only with the decimal point, as follows \"************.99\" must be in dollars.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",32,"Saldo de intereses","Numeric","15(X).XX",18,NULL,"Saldo de intereses, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\". Deberá estar en dólares.","Interest balance must be unmasked only with the decimal point, as follows: \"************.99\". It must be in dollars.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",33,"Saldo total","Numeric","15(X).XX",18,NULL,"Saldo total del depósito, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\". Deberá contener la suma de saldo capital más intereses y deberá estar en dólares.","Total deposit balance must be unmasked only with the decimal point, as follows: \"************.99\". It must contain the sum of the principal balance plus interest and must be in dollars.",NULL UNION ALL
    SELECT 2,"02.npb4_44_DEPÓSITOS.txt","NPB4-44","DEPÓSITOS.txt","TXT",False,"raw_02_npb4_44_DEPÓSITOS_txt","stg_02_npb4_44_DEPÓSITOS_txt",34,"Estado","Alphanumeric","A",1,NULL,"Estado del depósito, representa el estado en que se encuentra el depósito. Los estados a los que deberá homologarse son los siguientes: 1=Activa(default) 2=Inactiva 3=Cancelada 4=Cerrada 5=Embargo 6 = Otros.","Deposit status, represents the state in which the deposit is. The states to which it must be approved are the following: 1=Active (default) 2=Inactive 3=Cancelled 4=Closed 5=Embargo 6 = Others.",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 03. Identification documents of the clients (DOCUMENTOS CLIENTES.txt)
    SELECT 3,"03.npb4_44_DOCUMENTOS_CLIENTES.txt","NPB4-44","DOCUMENTOS_CLIENTES.txt","TXT",False,"raw_03_npb4_44_DOCUMENTOS_CLIENTES_txt","stg_03_npb4_44_DOCUMENTOS_CLIENTES_txt",1,"NIU","Alphanumeric","25(A)",25,NULL,"Número de Identificación Único asignado por la entidad a un determinado Cliente","Unique Identification Number assigned by the entity to a specific Client",NULL UNION ALL
    SELECT 3,"03.npb4_44_DOCUMENTOS_CLIENTES.txt","NPB4-44","DOCUMENTOS_CLIENTES.txt","TXT",False,"raw_03_npb4_44_DOCUMENTOS_CLIENTES_txt","stg_03_npb4_44_DOCUMENTOS_CLIENTES_txt",2,"Código del Documento","Alphanumeric","5(A)",5,NULL,"Código del Documento NIT=Número de Identificación Tributaria (default) DUI=Documento Único de Identidad PASAP=Pasaporte LICEC=Licencia de Conducir PTNAC=Partida de nacimiento CRESI=Carné de Residente CMINO=Carné de Minoridad","Document Code NIT=Tax Identification Number (default) DUI=Unique Identity Document PASAP=Passport LICEC=Driver's License PTNAC=Birth Certificate CRESI=Resident Card CMINO=Minority Card",NULL UNION ALL
    SELECT 3,"03.npb4_44_DOCUMENTOS_CLIENTES.txt","NPB4-44","DOCUMENTOS_CLIENTES.txt","TXT",False,"raw_03_npb4_44_DOCUMENTOS_CLIENTES_txt","stg_03_npb4_44_DOCUMENTOS_CLIENTES_txt",3,"Número de documento","Alphanumeric","25(A)",25,NULL,"Número de documento","Document number",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 04. Details of deposit holders, including joint or alternate ownership (TITULARES.txt)
    SELECT 4,"04.npb4_44_TITULARES.txt","NPB4-44","TITULARES.txt","TXT",False,"raw_04_npb4_44_TITULARES_txt","stg_04_npb4_44_TITULARES_txt",1,"NIU","Alphanumeric","25(A)",25,NULL,"Número de Identificación Único asignado por la entidad a un determinado cliente","Unique Identification Number assigned by the entity to a specific client",NULL UNION ALL
    SELECT 4,"04.npb4_44_TITULARES.txt","NPB4-44","TITULARES.txt","TXT",False,"raw_04_npb4_44_TITULARES_txt","stg_04_npb4_44_TITULARES_txt",2,"Número de la cuenta","Alphanumeric","20(A)",20,NULL,"Número de cuenta del depósito.","Deposit account number.",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 05. Names and locations of the institution's branches (AGENCIAS.txt)
    SELECT 5,"05.npb4_44_AGENCIAS.txt","NPB4-44","AGENCIAS.txt","TXT",False,"raw_05_npb4_44_AGENCIAS_txt","stg_05_npb4_44_AGENCIAS_txt",1,"Código de la Agencia","Alphanumeric","7(A)",7,NULL,"Código asignado por la entidad a cada una de las agencias o sucursales utilizadas por éste. Este es código único.","Code assigned by the entity to each of the agencies or branches used by it. This is unique code.",NULL UNION ALL
    SELECT 5,"05.npb4_44_AGENCIAS.txt","NPB4-44","AGENCIAS.txt","TXT",False,"raw_05_npb4_44_AGENCIAS_txt","stg_05_npb4_44_AGENCIAS_txt",2,"Nombre de la Agencia","Alphanumeric","30(A)",30,NULL,"Nombre asignado por la entidad para identificar una agencia.","Name assigned by the entity to identify an agency.",NULL UNION ALL
    SELECT 5,"05.npb4_44_AGENCIAS.txt","NPB4-44","AGENCIAS.txt","TXT",False,"raw_05_npb4_44_AGENCIAS_txt","stg_05_npb4_44_AGENCIAS_txt",3,"Ubicación de la Agencia","Alphanumeric","100(A)",100,NULL,"Ubicación física exacta de la agencia.","Exact physical location of the agency.",NULL UNION ALL
    SELECT 5,"05.npb4_44_AGENCIAS.txt","NPB4-44","AGENCIAS.txt","TXT",False,"raw_05_npb4_44_AGENCIAS_txt","stg_05_npb4_44_AGENCIAS_txt",4,"Código del Departamento","Alphanumeric","2(A)",2,NULL,"Código de departamento, de acuerdo a ubicación de la agencia. De acuerdo a NPB4-32, Anexo C Sector Geográfico.","Department code, according to agency location. According to NPB4-32, Annex C Geographic Sector.",NULL UNION ALL
    SELECT 5,"05.npb4_44_AGENCIAS.txt","NPB4-44","AGENCIAS.txt","TXT",False,"raw_05_npb4_44_AGENCIAS_txt","stg_05_npb4_44_AGENCIAS_txt",5,"Código del Municipio","Alphanumeric","2(A)",2,NULL,"Código del municipio, de acuerdo a la ubicación de la agencia De acuerdo a NPB4-32, Anexo C Sector Geográfico.","Municipality code, according to the location of the agency According to NPB4-32, Annex C Geographic Sector.",NULL UNION ALL
    SELECT 5,"05.npb4_44_AGENCIAS.txt","NPB4-44","AGENCIAS.txt","TXT",False,"raw_05_npb4_44_AGENCIAS_txt","stg_05_npb4_44_AGENCIAS_txt",6,"Estado de la Agencia","Alphanumeric","A",1,NULL,"Estado actual de la agencia, si ésta se encuentra o no habilitada al público. 1= Habilitado; 2=Cerrada.","Current status of the agency, whether or not it is open to the public. 1= Enabled; 2=Closed.",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 06. Types of deposit products approved by the institution (PRODUCTOS.txt)
    SELECT 6,"06.npb4_44_PRODUCTOS.txt","NPB4-44","PRODUCTOS.txt","TXT",False,"raw_06_npb4_44_PRODUCTOS_txt","stg_06_npb4_44_PRODUCTOS_txt",1,"Código del producto","Alphanumeric","4(A)",4,NULL,"Código del producto o tipo de depósito establecido por la entidad definidos dentro del catálogo de productos. Este es un código único.","Product code or deposit type established by the entity defined within the product catalog. This is a unique code.",NULL UNION ALL
    SELECT 6,"06.npb4_44_PRODUCTOS.txt","NPB4-44","PRODUCTOS.txt","TXT",False,"raw_06_npb4_44_PRODUCTOS_txt","stg_06_npb4_44_PRODUCTOS_txt",2,"Nombre del producto","Alphanumeric","30(A)",30,NULL,"Nombre asignado por la entidad para identificar un producto o tipo de depósito.","Name assigned by the entity to identify a product or type of deposit.",NULL UNION ALL
    SELECT 6,"06.npb4_44_PRODUCTOS.txt","NPB4-44","PRODUCTOS.txt","TXT",False,"raw_06_npb4_44_PRODUCTOS_txt","stg_06_npb4_44_PRODUCTOS_txt",3,"Estado del producto","Alphanumeric","A",1,NULL,"Estado actual del producto, si se encuentra o no disponible al público. 1=disponible 2=cerrado","Current status of the product, whether or not it is available to the public. 1=available 2=closed",NULL UNION ALL
    SELECT 6,"06.npb4_44_PRODUCTOS.txt","NPB4-44","PRODUCTOS.txt","TXT",False,"raw_06_npb4_44_PRODUCTOS_txt","stg_06_npb4_44_PRODUCTOS_txt",4,"Código genérico del producto","Alphanumeric","2(A)",2,NULL,"Código genérico utilizado 01=ahorro 02=corriente 03=Depósito a plazo 04=Otros.","Generic code used 01=savings 02=current 03=Time deposit 04=Others.",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 07. Data on the institution's officials and employees (FUNCIONARIOS Y EMPLEADOS.txt)
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",1,"Primer Nombre","Alphanumeric","30(A)",30,NULL,"Primer nombre de la persona que labora en la entidad financiera.","First name of the person who works at the financial institution.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",2,"Segundo Nombre","Alphanumeric","30(A)",30,NULL,"Segundo nombre de la persona que labora en la entidad financiera.","Second name of the person who works at the financial institution.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",3,"Primer Apellido","Alphanumeric","30(A)",30,NULL,"Primer apellido de la persona que trabaja en la entidad. Deberá existir el primer apellido de la persona.","First surname of the person who works at the entity. The person's first surname must exist.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",4,"Segundo Apellido","Alphanumeric","30(A)",30,NULL,"Segundo apellido, en caso aplique y de acuerdo a documento de identidad.","Second surname, if applicable and according to identification document.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",5,"Apellido de casada","Alphanumeric","30(A)",30,NULL,"Apellido de casada, en caso se tenga.","Married surname, if available.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",6,"Fecha de ingreso","Date","8(AAAAMMDD)",8,NULL,"Fecha de ingreso a la entidad.","Date of entry into the entity.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",7,"Cargo","Alphanumeric","50(A)",50,NULL,"Cargo actualmente desempeñado en la entidad.","Position currently held in the entity.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",8,"NIU","Alphanumeric","25(A)",25,NULL,"Código Único del empleado como cliente de la entidad.","Unique Code of the employee as a client of the entity.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",9,"Código del documento","Alphanumeric","5(A)",5,NULL,"Código del documento de identidad, perteneciente al empleado o funcionario, el cual deberá estar vigente. DUI= Documento Único de Identidad PASAP=Pasaporte CRESI= Carné de Residente.","Code of the identity document, belonging to the employee or official, which must be current. DUI= Unique Identity Document PASAP=Passport CRESI= Resident Card.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",10,"Número de documento","Alphanumeric","25(A)",25,NULL,"Número de documento de identidad del empleado o funcionario.","Identification document number of the employee or official.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",11,"Número Telefónico","Alphanumeric","10(A)",10,NULL,"Número telefónico registrado por el empleado en la entidad.","Telephone number registered by the employee in the entity.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",12,"Departamento","Alphanumeric","25(A)",25,NULL,"Departamento al que pertenece el empleado.","Department to which the employee belongs.",NULL UNION ALL
    SELECT 7,"07.npb4_44_FUNCIONARIOS_Y_EMPLEADOS.txt","NPB4-44","FUNCIONARIOS_Y_EMPLEADOS.txt","TXT",False,"raw_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt","stg_07_npb4_44_FUNCIONARIOS_Y_EMPLEADOS_txt",13,"Relacionado por administración","Alphanumeric","A",1,NULL,"Indicar si el funcionario se encuentra relaciona por administración de acuerdo con el artículo 204 de la ley de Bancos 0  No relacionado 1  Relacionado Para las entidades cooperativas, todos los empleados deben tener la relación por administración = 1.","Indicate if the official is related by administration in accordance with article 204 of the Banking Law 0 Not related 1 Related For cooperative entities, all employees must have the relationship by administration = 1.",NULL UNION ALL

-- NPB4-44 - Guaranteed deposits - 08. Information on guarantee payment (RESUMEN DE DEPÓSITOS GARANTIZADOS.txt)
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",1,"Correlativo","Numeric","9(A)",9,NULL,"Correlativo de depositantes empezando en 1.","Correlative of depositors starting at 1.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",2,"NIU","Alphanumeric","25(A)",25,NULL,"Número de identificación único asignado por la entidad a un determinado Cliente.","Unique identification number assigned by the entity to a specific Client.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",3,"Primer Nombre","Alphanumeric","30(A)",30,NULL,"Primer nombre de la persona natural, cliente de la entidad. Al menos deberá existir el primer nombre de la persona.","First name of the natural person, client of the entity. At least the person's first name must exist.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",4,"Segundo Nombre","Alphanumeric","30(A)",30,NULL,"Segundo nombre de la persona natural cliente de la entidad en caso posea.","Second name of the natural person who is a client of the entity, if applicable.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",5,"Tercer Nombre","Alphanumeric","30(A)",30,NULL,"Tercer nombre de la persona natural del cliente, en caso de existir.","Third name of the client's natural person, if applicable.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",6,"Primer Apellido","Alphanumeric","30(A)",30,NULL,"Primer Apellido de la persona natural, cliente de la entidad. Al igual que el primer nombre deberá existir primer apellido.","First Surname of the natural person, client of the entity. Just like the first name, there must be a first surname.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",7,"Segundo Apellido","Alphanumeric","30(A)",30,NULL,"Segundo apellido de la persona natural, cliente de la Entidad. Al igual que el primer apellido deberá existir segundo apellido.","Second surname of the natural person, client of the Entity. Just like the first surname, there must be a second surname.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",8,"Apellido de casada","Alphanumeric","30(A)",30,NULL,"Apellido de casada de la persona natural, si en caso lo hubiera.","Married surname of the natural person, if applicable.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",9,"Razón social","Alphanumeric","80(A)",80,NULL,"Razón Social. Este campo tendrá datos si el tipo de persona es persona jurídica para las personas naturales deberá estar vacío.","Business name. This field will have data if the type of person is a legal entity, for natural persons it must be empty.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",10,"Código del Documento","Alphanumeric","5(A)",5,NULL,"Código del Documento NIT=Número de Identificación Tributaria (default) DUI=Documento Único de Identidad PASAP=Pasaporte LICEC=Licencia de Conducir PTNAC=Partida de nacimiento CRESI=Carné de Residente CMINO=Carné de Minoridad.","Document Code NIT=Tax Identification Number (default) DUI=Unique Identity Document PASAP=Passport LICEC=Driver's License PTNAC=Birth Certificate CRESI=Resident Card CMINO=Minority Card.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",11,"Número de documento","Alphanumeric","25(A)",25,NULL,"Número de documento.","Document number.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",12,"Total de cuentas","Numeric","5(A)",5,NULL,"Cantidad total de cuentas que tiene el depositante.","Total number of accounts that the depositor has.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",13,"Saldo de capital","Numeric","15(X).XX",18,NULL,"Saldo de capital, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\" Deberá estar en dólares.","Capital balance, must be unmasked only with the decimal point, as follows \"************.99\" It must be in dollars.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",14,"Saldo de intereses","Numeric","15(X).XX",18,NULL,"Saldo de intereses, deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\". Deberá estar en dólares.","Interest balance must be unmasked only with the decimal point, as follows: \"************.99\". It must be in dollars.",NULL UNION ALL
    SELECT 8,"08.npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","NPB4-44","RESUMEN_DE_DEPÓSITOS_GARANTIZADOS.txt","TXT",False,"raw_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt","stg_08_npb4_44_RESUMEN_DE_DEPÓSITOS_GARANTIZADOS_txt",15,"Saldo garantizado","Numeric","15(X).XX",18,NULL,"Saldo hasta el límite garantizado por el IGD. Suma de depósitos hasta por el valor del límite de la garantía por persona estipulado de acuerdo con el artículo 167 de la ley de Bancos y de acuerdo con lo mencionado en el artículo 6 de la NRSF-01. Deberá estar sin máscara únicamente con el punto decimal, de la siguiente forma \"************.99\". Deberá estar en dólares.","Balance up to the limit guaranteed by the IGD. Sum of deposits up to the value of the guarantee limit per person stipulated in accordance with article 167 of the Banking Law and in accordance with what is mentioned in article 6 of the NRSF-01. It should be unmasked only with the decimal point, as follows: \"************.99\". It must be in dollars.",NULL

-- NPB4-44 - Guaranteed deposits - 09. NPB4-12 reports (ACCIONISTAS.txt)
-- NPB4-44 - Guaranteed deposits - 10. NPB4-17 reports (ACTIVOS DE RIESGO.txt)
-- NPB4-44 - Guaranteed deposits - 11. NPB4-16 reports (CONTABLE ESTADÍSTICA.txt)

-- NPB3-05 - Patrimonial Fund - 1. CÁLCULO DEL FONDO PATRIMONIAL CONSOLIDADO DE LA CONTROLADORA
-- NPB3-05 - Patrimonial Fund - 1.a. Con base a estados financieros no consolidados
-- NPB3-05 - Patrimonial Fund - 1.b. Con base a estados financieros consolidados
-- NPB3-05 - Patrimonial Fund - 2. CALCULATION OF THE EQUITY SITUATION OF THE FINANCIAL CONGLOMERATE

-- NPB3-09 - Credits to related parties - No official reporting requirements

)


SELECT
      report_id
    , report_name
    , report_norm
    , report_filename
    , report_format
    , report_enabled

    , source_table_name
    , destination_table_name

    , field_id
    , field_name
    , field_type
    , field_format
    , field_max_size
    , field_max_decimal
    , field_desc_original
    , field_desc_english

    , field_assertion
FROM report_list
