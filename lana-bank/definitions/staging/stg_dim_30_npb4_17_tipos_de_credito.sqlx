config {
	type: "table",
}

WITH tipos_de_credito AS (

    SELECT 'RF' AS code, 'Para refinanciamiento' AS description UNION ALL
    SELECT 'RR', 'Para refinanciamiento refinanciado' UNION ALL
    SELECT 'RE', 'Para reestructuración' UNION ALL
    SELECT 'CO', 'Para consolidación' UNION ALL
    SELECT 'RT', 'Para refinanciamiento total' UNION ALL
    SELECT 'RP', 'Para refinanciamiento parcial' UNION ALL
    SELECT 'SG', 'Para créditos garantizados con Fondos de Garantías de las Sociedades de Garantía Recíprocas (SGR)' UNION ALL
    SELECT 'FC', 'Para Fianzas FICAFE' UNION ALL
    SELECT 'CF', 'Para refinanciamiento con medidas temporales *' UNION ALL
    SELECT 'CR', 'Para refinanciamiento refinanciado con medidas temporales*' UNION ALL
    SELECT 'CE', 'Para reestructuración con medidas temporales*' UNION ALL
    SELECT 'CC', 'Para consolidación con medidas temporales*' UNION ALL
    SELECT 'CT', 'Para refinanciamiento total con medidas temporales*' UNION ALL
    SELECT 'CP', 'Para refinanciamiento parcial con medidas temporales*' UNION ALL
    SELECT 'CG', 'Para créditos garantizados con Fondos de Garantías de las Sociedades de Garantía Recíprocas (SGR) con medidas temporales*' UNION ALL
    SELECT 'CV', 'Créditos sin modificación, con medidas temporales*' UNION ALL
    SELECT 'PF', 'Para refinanciamiento con programa de asistencia sector Café' UNION ALL
    SELECT 'PR', 'Para refinanciamiento refinanciado con programa de asistencia sector Café' UNION ALL
    SELECT 'PE', 'Para reestructuración con programa de asistencia sector Café' UNION ALL
    SELECT 'PT', 'Para refinanciamiento total con programa de asistencia sector Café' UNION ALL
    SELECT 'PP', 'Para refinanciamiento parcial con programa de asistencia sector Café'

)


SELECT
    code,
    description
FROM tipos_de_credito
