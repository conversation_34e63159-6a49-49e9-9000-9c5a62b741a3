config {
	type: "table",
}

WITH tipos_de_categorias_de_riesgo AS (

    SELECT 'A1' AS code, 'Deudores normales' AS description UNION ALL
    SELECT 'A2', 'Deudores normales declinantes' UNION ALL
    SELECT 'B', 'Deudores subnormales' UNION ALL
    SELECT 'C1', 'Deudores deficientes' UNION ALL
    SELECT 'C2', 'Deudores deficientes declinantes' UNION ALL
    SELECT 'D1', 'Deudores de difícil recuperación' UNION ALL
    SELECT 'D2', 'Deudores de difícil recuperación declinantes' UNION ALL
    SELECT 'E', 'Deudores irrecuperables' UNION ALL
    SELECT 'E1', 'Deudores irrecuperables por Cuenta propia o autoempleo; Micro y Pequeña empresa de la Banca de Desarrollo (21)' UNION ALL
    SELECT 'E2', 'Deudores irrecuperables por Cuenta propia o autoempleo; Micro y Pequeña empresa de la Banca de Desarrollo (21)' UNION ALL
    SELECT 'E3', 'Deudores irrecuperables por Cuenta propia o autoempleo; Micro y Pequeña empresa de la Banca de Desarrollo (21)'

)


SELECT
    code,
    description
FROM tipos_de_categorias_de_riesgo
