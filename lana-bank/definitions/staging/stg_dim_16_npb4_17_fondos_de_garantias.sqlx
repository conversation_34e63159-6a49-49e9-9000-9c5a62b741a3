config {
	type: "table",
}

WITH fondos_de_garantias AS (

    SELECT 'FFA' AS code, 'Fondo de Garantía para Pequeños Empresarios (FOGAPE)' AS description UNION ALL
    SELECT 'FFG', 'Fondo de Garantía Agropecuario (FOGARA)' UNION ALL
    SELECT 'FFC', 'Fondo de Garantía para las Zonas Francas (FOGACRE)' UNION ALL
    SELECT 'PRO', 'Programa de Garantía Agropecuario (PROGARA)' UNION ALL
    SELECT 'PGP', 'Programa de Garantía para Pequeños Empresarios (PROGRAPE)' UNION ALL
    SELECT 'SGR', 'Sociedades de Garantía Recíproca' UNION ALL
    SELECT 'FDH', 'Fideicomiso de Respaldo para Créditos de Desarrollo Habitacional (FORDEH)' UNION ALL
    SELECT 'BSA', 'Fideicomiso Banco de Desarrollo de El Salvador (BANDESAL)' UNION ALL
    SELECT 'PGA', 'Programa Especial de Garantía para la Agricultura Intensa y su Agroindustria (PROGAIN) (13)' UNION ALL
    SELECT 'PGN', 'Fondo de Garantía de Crédito Agropecuario para la Zona Norte de El Salvador (PROGARA NORTE) (13)' UNION ALL
    SELECT 'FSG', 'Fondo Salvadoreño de Garantías (BANDESAL)' UNION ALL
    SELECT 'FDG', 'Fondo Solidario de Garantías' UNION ALL
    SELECT 'PMC', 'Proyecto Mercados Centroamericanos para la Biodiversidad' UNION ALL
    SELECT 'FGE', 'Fondo de Garantía para Estudiantes (FONEDUCA)' UNION ALL
    SELECT 'FIA', 'Fondo de Inversión para el Sector Agropecuario (FINSAGRO)' UNION ALL
    SELECT 'DFC', 'Fondo de Garantías DFC-USAID (21)' UNION ALL
    SELECT 'OTR', 'Otro (21)'

)


SELECT
    code,
    description
FROM fondos_de_garantias
