config {
	type: "table",
}

WITH uso_del_financiamiento AS (

    SELECT '001' AS code, 'Gastos corrientes o capital de trabajo' AS description UNION ALL
    SELECT '011', 'Compra de maquinaria y equipo' UNION ALL
    SELECT '012', 'Construcción y mejoras' UNION ALL
    SELECT '013', 'Instalaciones' UNION ALL
    SELECT '014', 'Estudios técnicos y de factibilidad' UNION ALL
    SELECT '015', 'Asistencia técnica y capacitación' UNION ALL
    SELECT '016', 'Adquisición de lotes de terreno' UNION ALL
    SELECT '017', 'Compra de locales comerciales' UNION ALL
    SELECT '018', 'Compra de mobiliario y equipo de oficina' UNION ALL
    SELECT '019', 'Estantería para locales comerciales' UNION ALL
    SELECT '020', 'Otras actividades' UNION ALL
    SELECT '090', 'Otros no especificados' UNION ALL
    SELECT '095', 'Refinanciamientos'

)


SELECT
    code,
    description
FROM uso_del_financiamiento
