config {
	type: "table",
}

WITH formas_de_pago AS (

    SELECT 'A' AS code, 'Anual' AS description UNION ALL
    SELECT 'E', 'Semestral' UNION ALL
    SELECT 'T', 'Trimestral' UNION ALL
    SELECT 'B', 'Bimensual' UNION ALL
    SELECT 'M', 'Mensual' UNION ALL
    SELECT 'Q', 'Quincenal' UNION ALL
    SELECT 'S', 'Semanal' UNION ALL
    SELECT 'D', 'Diario' UNION ALL
    SELECT 'V', 'Al Vencimiento' UNION ALL
    SELECT 'P', 'Pactada' UNION ALL
    SELECT 'O', 'Otras'

)


SELECT
    code,
    description
FROM formas_de_pago
