config {
	type: "table",
}

WITH municipios AS (

    -- <PERSON><PERSON><PERSON><PERSON><PERSON>
    SELECT "0101" AS code,"<PERSON><PERSON><PERSON><PERSON><PERSON>" AS description UNION ALL
    SELECT "0102","Apaneca" UNION ALL
    SELECT "0103","Atiquizaya" UNION ALL
    SELECT "0104","Concepción de Ataco" UNION ALL
    SELECT "0105","El Refugio" UNION ALL
    SELECT "0106","Guaymango" UNION ALL
    SELECT "0107","Jujutla" UNION ALL
    SELECT "0108","San Francisco Menéndez" UNION ALL
    SELECT "0109","San Lorenzo" UNION ALL
    SELECT "0110","San Pedro Puxtla" UNION ALL
    SELECT "0111","Tacuba" UNION ALL
    SELECT "0112","<PERSON><PERSON><PERSON>" UNION ALL

    -- Santa Ana
    SELECT "0201","Candelaria de la Frontera" UNION ALL
    SELECT "0202","Coatepeque" UNION ALL
    SELECT "0203","Chalchu<PERSON>" UNION ALL
    SELECT "0204","El Congo" UNION ALL
    SELECT "0205","El Porvenir" UNION ALL
    SELECT "0206","Masahuat" UNION ALL
    SELECT "0207","Metapán" UNION ALL
    SELECT "0208","San Antonio Pajonal" UNION ALL
    SELECT "0209","San Sebastián Salitrillo" UNION ALL
    SELECT "0210","Santa Ana" UNION ALL
    SELECT "0211","Santa Rosa Guachipilín" UNION ALL
    SELECT "0212","Santiago de la Frontera" UNION ALL
    SELECT "0213","Texistepeque" UNION ALL

    -- Sonsonate
    SELECT "0301","Acajutla" UNION ALL
    SELECT "0302","Armenia" UNION ALL
    SELECT "0303","Caluco" UNION ALL
    SELECT "0304","Cuisnahuat" UNION ALL
    SELECT "0305","Santa Isabel Ishuatán" UNION ALL
    SELECT "0306","Izalco" UNION ALL
    SELECT "0307","Juayúa" UNION ALL
    SELECT "0308","Nahuizalco" UNION ALL
    SELECT "0309","Nahuilingo" UNION ALL
    SELECT "0310","Salcoatitán" UNION ALL
    SELECT "0311","San Antonio del Monte" UNION ALL
    SELECT "0312","San Julián" UNION ALL
    SELECT "0313","Santa Catarina Masahuat" UNION ALL
    SELECT "0314","Santo Domingo de Guzmán" UNION ALL
    SELECT "0315","Sonsonate" UNION ALL
    SELECT "0316","Sonzacate" UNION ALL

    -- Chalatenango
    SELECT "0401","Aguacaliente" UNION ALL
    SELECT "0402","Arcatao" UNION ALL
    SELECT "0403","Azacualpa" UNION ALL
    SELECT "0404","Citalá" UNION ALL
    SELECT "0405","Comalapa" UNION ALL
    SELECT "0406","Concepción Quezaltepeque" UNION ALL
    SELECT "0407","Chalatenango" UNION ALL
    SELECT "0408","Dulce Nombre de María" UNION ALL
    SELECT "0409","El Carrizal" UNION ALL
    SELECT "0410","El Paraíso" UNION ALL
    SELECT "0411","La Laguna" UNION ALL
    SELECT "0412","La Palma" UNION ALL
    SELECT "0413","La Reina" UNION ALL
    SELECT "0414","Las Vueltas" UNION ALL
    SELECT "0415","Nombre de Jesús" UNION ALL
    SELECT "0416","Nueva Concepción" UNION ALL
    SELECT "0417","Nueva Trinidad" UNION ALL
    SELECT "0418","Ojos de Agua" UNION ALL
    SELECT "0419","Potonico" UNION ALL
    SELECT "0420","San Antonio de la Cruz" UNION ALL
    SELECT "0421","San Antonio Los Ranchos" UNION ALL
    SELECT "0422","San Fernando" UNION ALL
    SELECT "0423","San Francisco Lempa" UNION ALL
    SELECT "0424","San Francisco Morazán" UNION ALL
    SELECT "0425","San Ignacio" UNION ALL
    SELECT "0426","San Isidro Labrador" UNION ALL
    SELECT "0427","San José Cancasque" UNION ALL
    SELECT "0428","San José las Flores" UNION ALL
    SELECT "0429","San Luis del Carmen" UNION ALL
    SELECT "0430","San Miguel de Mercedes" UNION ALL
    SELECT "0431","San Rafael" UNION ALL
    SELECT "0432","Santa Rita" UNION ALL
    SELECT "0433","Tejutla" UNION ALL

    -- La Libertad
    SELECT "0501","Antiguo Cuscatlán" UNION ALL
    SELECT "0502","Ciudad Arce" UNION ALL
    SELECT "0503","Colón" UNION ALL
    SELECT "0504","Comasagua" UNION ALL
    SELECT "0505","Chiltiupán" UNION ALL
    SELECT "0506","Huizúcar" UNION ALL
    SELECT "0507","Jayaque" UNION ALL
    SELECT "0508","Jicalapa" UNION ALL
    SELECT "0509","La Libertad" UNION ALL
    SELECT "0510","Nuevo Cuscatlán" UNION ALL
    SELECT "0511","Santa Tecla" UNION ALL
    SELECT "0512","Quezaltepeque" UNION ALL
    SELECT "0513","Sacacoyo" UNION ALL
    SELECT "0514","San José Villanueva" UNION ALL
    SELECT "0515","San Juan Opico" UNION ALL
    SELECT "0516","San Matías" UNION ALL
    SELECT "0517","San PabloTacachico" UNION ALL
    SELECT "0518","Tamanique" UNION ALL
    SELECT "0519","Talnique" UNION ALL
    SELECT "0520","Teotepeque" UNION ALL
    SELECT "0521","Tepecoyo" UNION ALL
    SELECT "0522","Zaragoza" UNION ALL

    -- San Salvador
    SELECT "0601","Aguilares" UNION ALL
    SELECT "0602","Apopa" UNION ALL
    SELECT "0603","Ayutuxtepeque" UNION ALL
    SELECT "0604","Cuscatancingo" UNION ALL
    SELECT "0605","El Paisnal" UNION ALL
    SELECT "0606","Guazapa" UNION ALL
    SELECT "0607","Ilopango" UNION ALL
    SELECT "0608","Mejicanos" UNION ALL
    SELECT "0609","Nejapa" UNION ALL
    SELECT "0610","Panchimalco" UNION ALL
    SELECT "0611","Rosario De Mora" UNION ALL
    SELECT "0612","San Marcos" UNION ALL
    SELECT "0613","San Martín" UNION ALL
    SELECT "0614","San Salvador" UNION ALL
    SELECT "0615","Santiago Texacuangos" UNION ALL
    SELECT "0616","Santo Tomás" UNION ALL
    SELECT "0617","Soyapango" UNION ALL
    SELECT "0618","Tonacatepeque" UNION ALL
    SELECT "0619","Ciudad Delgado" UNION ALL

    -- Cuscatlán
    SELECT "0701","Candelaria" UNION ALL
    SELECT "0702","Cojutepeque" UNION ALL
    SELECT "0703","El Carmen" UNION ALL
    SELECT "0704","El Rosario" UNION ALL
    SELECT "0705","Monte San Juan" UNION ALL
    SELECT "0706","Oratorio De Concepción" UNION ALL
    SELECT "0707","San Bartolomé Perulapía" UNION ALL
    SELECT "0708","San Cristóbal" UNION ALL
    SELECT "0709","San José Guayabal" UNION ALL
    SELECT "0710","San Pedro Perulapán" UNION ALL
    SELECT "0711","San Rafael Cedros" UNION ALL
    SELECT "0712","San Ramón" UNION ALL
    SELECT "0713","Santa Cruz Analquito" UNION ALL
    SELECT "0714","Santa Cruz Michapa" UNION ALL
    SELECT "0715","Suchitoto" UNION ALL
    SELECT "0716","Tenancingo" UNION ALL

    -- La Paz
    SELECT "0801","Cuyultitán" UNION ALL
    SELECT "0802","El Rosario" UNION ALL
    SELECT "0803","Jerusalén" UNION ALL
    SELECT "0804","Mercedes La Ceiba" UNION ALL
    SELECT "0805","Olocuilta" UNION ALL
    SELECT "0806","Paraíso de Osorio" UNION ALL
    SELECT "0807","San Antonio Masahuat" UNION ALL
    SELECT "0808","San Emigdio" UNION ALL
    SELECT "0809","San Francisco Chinameca" UNION ALL
    SELECT "0810","San Juan Nonualco" UNION ALL
    SELECT "0811","San Juan Talpa" UNION ALL
    SELECT "0812","San Juan Tepezontes" UNION ALL
    SELECT "0813","San Luis Talpa" UNION ALL
    SELECT "0814","San Miguel Tepezontes" UNION ALL
    SELECT "0815","San Pedro Masahuat" UNION ALL
    SELECT "0816","San Pedro Nonualco" UNION ALL
    SELECT "0817","San Rafael Obrajuelo" UNION ALL
    SELECT "0818","Santa María Ostuma" UNION ALL
    SELECT "0819","Santiago Nonualco" UNION ALL
    SELECT "0820","Tapalhuaca" UNION ALL
    SELECT "0821","Zacatecoluca" UNION ALL
    SELECT "0822","San Luis La Herradura" UNION ALL

    -- Cabañas
    SELECT "0901","Cinquera" UNION ALL
    SELECT "0902","Guacotecti" UNION ALL
    SELECT "0903","Ilobasco" UNION ALL
    SELECT "0904","Jutiapa" UNION ALL
    SELECT "0905","San Isidro" UNION ALL
    SELECT "0906","Sensuntepeque" UNION ALL
    SELECT "0907","Tejutepeque" UNION ALL
    SELECT "0908","Victoria" UNION ALL
    SELECT "0909","Villa Dolores" UNION ALL

    -- San Vicente
    SELECT "1001","Apastepeque" UNION ALL
    SELECT "1002","Guadalupe" UNION ALL
    SELECT "1003","San Cayetano Istepeque" UNION ALL
    SELECT "1004","Santa Clara" UNION ALL
    SELECT "1005","Santo Domingo" UNION ALL
    SELECT "1006","San Esteban Catarina" UNION ALL
    SELECT "1007","San Ildefonso" UNION ALL
    SELECT "1008","San Lorenzo" UNION ALL
    SELECT "1009","San Sebastián" UNION ALL
    SELECT "1010","San Vicente" UNION ALL
    SELECT "1011","Tecoluca" UNION ALL
    SELECT "1012","Tepetitán" UNION ALL
    SELECT "1013","Verapaz" UNION ALL

    -- Usulután
    SELECT "1101","Alegría" UNION ALL
    SELECT "1102","Berlín" UNION ALL
    SELECT "1103","California" UNION ALL
    SELECT "1104","Concepción Batres" UNION ALL
    SELECT "1105","El Triunfo" UNION ALL
    SELECT "1106","Ereguayquín" UNION ALL
    SELECT "1107","Estanzuelas" UNION ALL
    SELECT "1108","Jiquilisco" UNION ALL
    SELECT "1109","Jucuapa" UNION ALL
    SELECT "1110","Jucuarán" UNION ALL
    SELECT "1111","Mercedes Umaña" UNION ALL
    SELECT "1112","Nueva Granada" UNION ALL
    SELECT "1113","Ozatlán" UNION ALL
    SELECT "1114","Puerto El Triunfo" UNION ALL
    SELECT "1115","San Agustín" UNION ALL
    SELECT "1116","San Buenaventura" UNION ALL
    SELECT "1117","San Dionisio" UNION ALL
    SELECT "1118","Santa Elena" UNION ALL
    SELECT "1119","San Francisco Javier" UNION ALL
    SELECT "1120","Santa María" UNION ALL
    SELECT "1121","Santiago De María" UNION ALL
    SELECT "1122","Tecapán" UNION ALL
    SELECT "1123","Usulután" UNION ALL

    -- San Miguel
    SELECT "1201","Carolina" UNION ALL
    SELECT "1202","Ciudad Barrios" UNION ALL
    SELECT "1203","Comacarán" UNION ALL
    SELECT "1204","Chapeltique" UNION ALL
    SELECT "1205","Chinameca" UNION ALL
    SELECT "1206","Chirilagua" UNION ALL
    SELECT "1207","El Tránsito" UNION ALL
    SELECT "1208","Lolotique" UNION ALL
    SELECT "1209","Moncagua" UNION ALL
    SELECT "1210","Nueva Guadalupe" UNION ALL
    SELECT "1211","Nuevo Edén de San Juan" UNION ALL
    SELECT "1212","Quelepa" UNION ALL
    SELECT "1213","San Antonio Del Mosco" UNION ALL
    SELECT "1214","San Gerardo" UNION ALL
    SELECT "1215","San Jorge" UNION ALL
    SELECT "1216","San Luis de la Reina" UNION ALL
    SELECT "1217","San Miguel" UNION ALL
    SELECT "1218","San Rafael Oriente" UNION ALL
    SELECT "1219","Sesori" UNION ALL
    SELECT "1220","Uluazapa" UNION ALL

    -- Morazán
    SELECT "1301","Arambala" UNION ALL
    SELECT "1302","Cacaopera" UNION ALL
    SELECT "1303","Corinto" UNION ALL
    SELECT "1304","Chilanga" UNION ALL
    SELECT "1305","Delicias De Concepción" UNION ALL
    SELECT "1306","El Divisadero" UNION ALL
    SELECT "1307","El Rosario" UNION ALL
    SELECT "1308","Gualococti" UNION ALL
    SELECT "1309","Guatajiagua" UNION ALL
    SELECT "1310","Joateca" UNION ALL
    SELECT "1311","Jocoaitique" UNION ALL
    SELECT "1312","Jocoro" UNION ALL
    SELECT "1313","Lolotiquillo" UNION ALL
    SELECT "1314","Meanguera" UNION ALL
    SELECT "1315","Osicala" UNION ALL
    SELECT "1316","Perquín" UNION ALL
    SELECT "1317","San Carlos" UNION ALL
    SELECT "1318","San Fernando" UNION ALL
    SELECT "1319","San Francisco Gotera" UNION ALL
    SELECT "1320","San Isidro" UNION ALL
    SELECT "1321","San Simón" UNION ALL
    SELECT "1322","Sensembra" UNION ALL
    SELECT "1323","Sociedad" UNION ALL
    SELECT "1324","Torola" UNION ALL
    SELECT "1325","Yamabal" UNION ALL
    SELECT "1326","Yoloaiquín" UNION ALL

    -- La Unión
    SELECT "1401","Anamorós" UNION ALL
    SELECT "1402","Bolívar" UNION ALL
    SELECT "1403","Concepción De Oriente" UNION ALL
    SELECT "1404","Conchagua" UNION ALL
    SELECT "1405","El Carmen" UNION ALL
    SELECT "1406","El Sauce" UNION ALL
    SELECT "1407","Intipucá" UNION ALL
    SELECT "1408","La Unión" UNION ALL
    SELECT "1409","Lislique" UNION ALL
    SELECT "1410","Meanguera Del Golfo" UNION ALL
    SELECT "1411","Nueva Esparta" UNION ALL
    SELECT "1412","Pasaquina" UNION ALL
    SELECT "1413","Polorós" UNION ALL
    SELECT "1414","San Alejo" UNION ALL
    SELECT "1415","San José" UNION ALL
    SELECT "1416","Santa Rosa de Lima" UNION ALL
    SELECT "1417","Yayantique" UNION ALL
    SELECT "1418","Yucuaiquín" UNION ALL
    SELECT "1500","Derogado (21)" UNION ALL
    SELECT "1600","Derogado (21)" UNION ALL
    SELECT "1700","Derogado (21)" UNION ALL
    SELECT "1800","Derogado (21)" UNION ALL
    SELECT "1900","Derogado (21)" UNION ALL
    SELECT "2000","Derogado (21)"

)


SELECT
    code,
    description
FROM municipios
