config {
	type: "table",
}

WITH tipos_de_garantias AS (

    SELECT 'HA' AS code, 'Hipoteca abierta' AS description UNION ALL
    SELECT 'HC', 'Hipoteca cerrada' UNION ALL
    SELECT 'FI', 'Fiduciaria' UNION ALL
    SELECT 'PR', 'Prendaria' UNION ALL
    SELECT 'PI', 'Pignorada - Depósito de dinero' UNION ALL
    SELECT 'FG', 'Fondos de garantías' UNION ALL
    SELECT 'FB', 'Fianzas de bancos locales o bancos extranjeros de primera línea' UNION ALL
    SELECT 'CC', 'Cartas de crédito stand by' UNION ALL
    SELECT 'AV', 'Avales' UNION ALL
    SELECT 'BP', 'Bonos de prenda' UNION ALL
    SELECT 'PD', 'Prenda de documentos' UNION ALL
    SELECT 'VR', 'Valores de rescate de seguros de vida' UNION ALL
    SELECT 'PO', 'Póliza de seguro' UNION ALL
    SELECT 'PV', 'Prenda sobre valores de renta fija'

)


SELECT
    code,
    description
FROM tipos_de_garantias
