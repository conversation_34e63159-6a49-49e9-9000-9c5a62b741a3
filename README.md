
# galoy-reporting

## Overview of data pipeline

### Sources

Raw data from production databases or ingested using cloud functions.

-	mongodb:

		bq ls galoy_staging_galoy_raw

-	stablesats:
		
		bq ls galoy_staging_stablesats_raw

-	cloud functions:

		bq ls galoy_staging_functions_raw

	Example function implementation:
		[./functions/coincap-price/index.js](./functions/coincap-price/index.js)

### Transformations

We use dataform to transform source tables to create useful models.
A transformation is a SQL query on source tables
and/or the outputs of previous transormations:

	source | output, [source | output], ... -> output

This implies a directed acyclic graph of transformations.

Example simple transformation:
	[./definitions/staging/stg_lightning_payments.sqlx](./definitions/staging/stg_lightning_payments.sqlx)

Example complex transformation:
	[./definitions/holistics-models/fct_user_wallet_lightning_payments.sqlx](./definitions/holistics-models/fct_user_wallet_lightning_payments.sqlx)

A view of the dependency graph:
	./dependencies.svg
	(generated by running `make dep-graph`)

### Dasboards

We import tables in [./definitions/holistics-models/](./definitions/holistics-models/)
into holistics and define how they can be joined together.

See: https://us.holistics.io/home

Tables here should be ready to use in holistics' report templates.

### Exploratory data analysis

Often holistics' reporting interface is not flexible enough for custom plots.
In those case you can simply use the `bq` command to download query results
(be mindful of `bq`'s limits on download sizes).

See https://github.com/blinkbitcoin/data-exploration/tree/main/bigquery
for examples.

## Dev

### `current-dev` tag and testing on production data

Add `tags: ["current-dev"],` to the top of your sqlx config block
and run `make run-dev` to run just the subset of the sqlx files that you're working on.
(Remove the tag before committing.)

This can also be used to test sql statements on production data by
replacing the relative references (`${ref("stg_some_table")}`) with direct references
to the production database (`dataform_galoy_bbw.stg_some_table`).
This can even be done for interdependent series of transformations by
tagging all of them with "current-dev" and keeping only the relative
references that are being tested.

### Pre-commit hooks

The script [`./scripts/pre-commit`](./scripts/pre-commit)
can be linked to `.git/hooks/pre-commit`
to automatically check for common mistakes like:

- `current-dev` tag in sqlx files
- direct references to production tables
- `make run` errors

### `bq` command line interface

List available tables:

    bq ls dataform_galoy_bbw
    
Run a query on a table:

    bq query << 'EOF'
    #standardSQL
    SELECT *
    FROM dataform_galoy_bbw.fct_user_wallet_transactions
    WHERE DATE(recorded_at) = CURRENT_DATE()
    LIMIT 3
    EOF

## Data security

Since anyone with access to holistics can inspect them,
the tables in [./definitions/models](./definitions/models)
(except the "recently active" uncensored accounting view)
should be constructed so as to keep the identities of users as private as possible.
Currently, these tables use a one way mapping from id strings to integers
to make sure that no internal ids are exposed to holistics.
(For an example see here:
https://github.com/blinkbitcoin/blink-reporting/blob/051eeb052ee42e9732bacca886955956d907b63a/definitions/staging/stg_user_accounts.sqlx#L12C1-L13
)
We also censor both onchain transaction ids and lightning payment hashes in these tables.

## Performance

It's easy to construct queries that can be very expensive when run,
to make matters worse, some queries are run many many times per day by tools like holistics.
For these reasons, it's important to keep track of the costs associated with queries.
The following sections have some example queries that can be run on bigquery's
`INFORMATION_SCHEMA.JOBS` table to get a sense of the costs of commonly used queries.

### Jobs by bytes billed

This is an unaggreageted view of the most expensive jobs run in the last day:

	select job_id, user_email, job_type, statement_type, priority, query, total_bytes_billed/1e9 as total_gbytes_billed
	from `region-us.INFORMATION_SCHEMA.JOBS`
	where date(creation_time) = date_sub(current_date(), interval 1 day)
	order by total_bytes_billed desc
	limit 1000

### Most expensive by query

This groups jobs by the query source code to rank queries that run often higher:

	SELECT 
	  any_value(destination_table.dataset_id) as destination_dataset,
	  any_value(destination_table.table_id) as destination_table,
	  any_value(statement_type) as statement_type,
	  query as query,
	  sum(total_bytes_billed)/1e9 as total_gbytes_billed,
	  count(1) as n
	FROM `region-us.INFORMATION_SCHEMA.JOBS`
	WHERE date(creation_time) = date_sub(current_date(), interval 1 day)
	group by query
	ORDER BY total_gbytes_billed DESC
	LIMIT 1000

### Most referenced tables

This groups jobs by the tables they reference to show which tables might
benefit from careful clustering design:

	SELECT 
	  ref.dataset_id as referenced_dataset,
	  ref.table_id as referenced_table,
	  any_value(statement_type) as statement_type,
	  any_value(query) as query,
	  sum(total_bytes_billed)/1e9 as total_gbytes_billed,
	  count(1) as n
	FROM `region-us.INFORMATION_SCHEMA.JOBS`, unnest(referenced_tables) as ref
	WHERE date(creation_time) = date_sub(current_date(), interval 1 day)
	group by referenced_dataset, referenced_table
	ORDER BY total_gbytes_billed DESC
	LIMIT 1000



