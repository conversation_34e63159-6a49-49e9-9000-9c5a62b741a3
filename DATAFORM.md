# Dataform definitions

The folder [./definitions](definitions) contains dataform
transformations of data from the production databases.
We want to take the raw data and make it more amenable to generating reports
like the weekly BBW report, the USD PnL report, and the payment destinations report.
We also want the transformations to model the data so that it's
easy for non-data team users to query the transformed tables and
answer business questions.

Throughout, we use the following naming conventions for tables:
- `stg_*` for "intermediary tables that are only used internally"
  [[dataform docs]](https://docs.dataform.co/best-practices/start-your-dataform-project)
- `fct_*` for facts, "which hold the measurable, quantitative data about the bank"
  [[wikipedia]](https://en.wikipedia.org/wiki/Star_schema)
- `dim_*` for dimensions, "which are descriptive attributes related to fact data" [[wikipedia]](https://en.wikipedia.org/wiki/Star_schema)
- `report_*` for reports that use facts and dimensions in the reporting directory, [./definitions/rpt](definitions/rpt)
- `assert_*` for assertions

In addition to that, keep in mind the following recommendations on persistence:
> Use `views` by default (they’re cheap and quick)
> 
> Use `tables` for datasets you expect to have downstream users for performance reasons. For example datasets queries by BI tools or datasets that have multiple descendents.
> 
> If the input data is very large, use `incremental` tables. They transform only new data (but are more complex to manage)
> 
> [[dataform docs]](https://docs.dataform.co/best-practices/best-practices)


## Ledger transactions

There are two possible levels of resolution, or "grains",
at which we can model the ledger transactions.
The first corresponds to the individual journal entries in `medici_transactions`.
This level of resolution is necessary to answer questions about particular users
like user PnL, weekly active users, and loop imbalance by user.
We can call this level of resolution `fct_journal_entries`.
It will have references to a dimension for user accounts, `dim_accounts`.
`dim_accounts`, in turn, will be drawn from the production
`users` and `wallets` databases
(combining them rather than creating two mutually dependent transformed tables).

The second level of resolution that is useful corresponds to
the journals themselves in `medici_journals`.
This level of resolution is better for answering questions about
general trends across users like
monthly volume, number of transactions by settlement layer, or gross fee revenue.
This table, `fct_journals`, will incorporate aggregate statistics from `tbl_journal_entries`
and references to `dim_lnpayments`.

## Lightning payments

Lightning payments are a good fit for the star schema,
where a fact table with a row describing each payment
can reference dimensions for individual channels and individual nodes
that make up the route and the destination of the payment.
The payment details are currently in the `lnpayments` table in the production database
and the nodes and channels database lives in lnd and I will investigate
good ways to persist it into our data warehouse.

## Stablesats exchange transactions

It is reasonable to model the different kinds of transactions
on the exchange separately.
We can have a table for trades that increase or decrease our hedge position this will include trading fees,
a table for the regular funding fees that are applied to open positions,
a table for bitcoin transfers between funding and trade accounts on the exchange, and
a table for bitcoin transfers between the Stablesats wallet in the bank and the funding account on the exchange.
