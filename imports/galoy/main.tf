variable "environment" {}
variable "kratos_connection" {}
variable "dataset_id" {}
variable "timestamp" {}

locals {
  project    = "galoy-reporting"
  env        = var.environment
  timestamp  = var.timestamp
  dataset_id = var.dataset_id

  tables = {
    identities = {
      table_id = "identities"
      query = templatefile("${path.module}/queries/identities.sql.tmpl",
        {
          connection_id : var.kratos_connection
        }
      )
    },
    sessions = {
      table_id = "sessions"
      query = templatefile("${path.module}/queries/sessions.sql.tmpl",
        {
          connection_id : var.kratos_connection
        }
      )
    },
  }
}

resource "google_bigquery_job" "errors" {
  project = local.project
  job_id  = "${local.env}-${local.timestamp}-import-errors"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = templatefile("${path.module}/queries/import-errors.sql.tmpl", {
      project : local.project
      dataset : local.dataset_id
      timestamp : local.timestamp
    })

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = local.dataset_id
      table_id   = "last_import_errors"
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

resource "google_bigquery_job" "job" {
  for_each = local.tables

  project = local.project
  job_id  = "import-${local.env}-${each.key}-${local.timestamp}"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = each.value.query

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = local.dataset_id
      table_id   = each.value.table_id
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/galoy"
  }
}
