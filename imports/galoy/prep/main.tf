variable "environment" {}

locals {
  project   = "galoy-reporting"
  env       = var.environment
  timestamp = replace(timestamp(), "/[-| |T|Z|:]/", "")
}

module "dataset" {
  source = "../../raw_dataset"

  component   = "galoy"
  environment = var.environment
}

output "timestamp" {
  value = local.timestamp
}

output "dataset_id" {
  value = module.dataset.dataset_id
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/galoy/prep"
  }
}
