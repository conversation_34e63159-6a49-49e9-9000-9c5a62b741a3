variable "environment" {}
variable "bria_connection" {}

locals {
  project         = "galoy-reporting"
  env             = var.environment
  bria_connection = var.bria_connection
  timestamp       = replace(timestamp(), "/[-| |T|Z|:]/", "")

  tables = {
    bria-accounts = {
      table_id = "bria_accounts"
      query = templatefile("${path.module}/queries/bria_accounts.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-addresses = {
      table_id = "bria_addresses"
      query = templatefile("${path.module}/queries/bria_addresses.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-batch-wallet-summaries = {
      table_id = "bria_batch_wallet_summaries"
      query = templatefile("${path.module}/queries/bria_batch_wallet_summaries.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-batches = {
      table_id = "bria_batches"
      query = templatefile("${path.module}/queries/bria_batches.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-descriptors = {
      table_id = "bria_descriptors"
      query = templatefile("${path.module}/queries/bria_descriptors.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-payout-queues = {
      table_id = "bria_payout_queues"
      query = templatefile("${path.module}/queries/bria_payout_queues.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-payouts = {
      table_id = "bria_payouts"
      query = templatefile("${path.module}/queries/bria_payouts.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-profiles = {
      table_id = "bria_profiles"
      query = templatefile("${path.module}/queries/bria_profiles.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-utxos = {
      table_id = "bria_utxos"
      query = templatefile("${path.module}/queries/bria_utxos.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-wallets = {
      table_id = "bria_wallets"
      query = templatefile("${path.module}/queries/bria_wallets.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    bria-xpubs = {
      table_id = "bria_xpubs"
      query = templatefile("${path.module}/queries/bria_xpubs.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-accounts = {
      table_id = "sqlx_ledger_accounts"
      query = templatefile("${path.module}/queries/sqlx_ledger_accounts.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-balances = {
      table_id = "sqlx_ledger_balances"
      query = templatefile("${path.module}/queries/sqlx_ledger_balances.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-current-balances = {
      table_id = "sqlx_ledger_current_balances"
      query = templatefile("${path.module}/queries/sqlx_ledger_current_balances.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-entries = {
      table_id = "sqlx_ledger_entries"
      query = templatefile("${path.module}/queries/sqlx_ledger_entries.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-events = {
      table_id = "sqlx_ledger_events"
      query = templatefile("${path.module}/queries/sqlx_ledger_events.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-journals = {
      table_id = "sqlx_ledger_journals"
      query = templatefile("${path.module}/queries/sqlx_ledger_journals.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-transactions = {
      table_id = "sqlx_ledger_transactions"
      query = templatefile("${path.module}/queries/sqlx_ledger_transactions.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
    sqlx-ledger-tx-templates = {
      table_id = "sqlx_ledger_tx_templates"
      query = templatefile("${path.module}/queries/sqlx_ledger_tx_templates.sql.tmpl",
        {
          connection_id : var.bria_connection
        }
      )
    },
  }
}

module "dataset" {
  source = "../raw_dataset"

  component   = "bria"
  environment = var.environment
}

resource "google_bigquery_job" "job" {
  for_each = local.tables

  project = local.project
  job_id  = "import-${local.env}-bria-${each.key}-${local.timestamp}"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = each.value.query

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = module.dataset.dataset_id
      table_id   = each.value.table_id
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/bria"
  }
}
