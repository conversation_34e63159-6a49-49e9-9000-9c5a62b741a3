SELECT * FROM EXTERNAL_QUERY("${connection_id}", "SELECT account_id::VARCHA<PERSON>(40), wallet_id::VARCHAR(40), keychain_id::VARCHAR(40), tx_id, vout, sats_per_vbyte_when_created, self_pay, kind::VARCHAR(10), address_idx, value, address, script_hex, bdk_spent, spending_batch_id::VARCHAR(40), spending_payout_queue_id::VARCHAR(40), spending_sats_per_vbyte, block_height, income_detected_ledger_tx_id::VARCHAR(40), income_settled_ledger_tx_id::VARCHAR(40), spend_detected_ledger_tx_id::VARCHAR(40), spend_settled_ledger_tx_id::VA<PERSON><PERSON><PERSON>(40), created_at, modified_at FROM bria_utxos;");
