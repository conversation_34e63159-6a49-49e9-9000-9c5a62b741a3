variable "environment" {}
variable "stablesats_connection" {}

locals {
  project               = "galoy-reporting"
  env                   = var.environment
  stablesats_connection = var.stablesats_connection
  timestamp             = replace(timestamp(), "/[-| |T|Z|:]/", "")

  tables = {
    user-trades = {
      table_id = "user_trades"
      query = templatefile("${path.module}/queries/user_trades.sql.tmpl",
        {
          connection_id : var.stablesats_connection
        }
      )
    },
    galoy-transactions = {
      table_id = "galoy_transactions"
      query = templatefile("${path.module}/queries/galoy_transactions.sql.tmpl",
        {
          connection_id : var.stablesats_connection
        }
      )
    },
    okex-orders = {
      table_id = "okex_orders"
      query = templatefile("${path.module}/queries/okex_orders.sql.tmpl",
        {
          connection_id : var.stablesats_connection
        }
      )
    }
    okex-transfers = {
      table_id = "okex_transfers"
      query = templatefile("${path.module}/queries/okex_transfers.sql.tmpl",
        {
          connection_id : var.stablesats_connection
        }
      )
    }
    ledger-balances = {
      table_id = "sqlx_ledger_balances"
      query = templatefile("${path.module}/queries/sqlx_ledger_balances.sql.tmpl",
        {
          connection_id : var.stablesats_connection
        }
      )
    },
  }
}

module "dataset" {
  source = "../raw_dataset"

  component   = "stablesats"
  environment = var.environment
}

resource "google_bigquery_job" "job" {
  for_each = local.tables

  project = local.project
  job_id  = "import-${local.env}-stablesats-${each.key}-${local.timestamp}"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = each.value.query

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = module.dataset.dataset_id
      table_id   = each.value.table_id
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/stablesats"
  }
}
