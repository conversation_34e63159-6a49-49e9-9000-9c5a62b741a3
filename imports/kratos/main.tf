variable "environment" {}
variable "kratos_connection" {}

locals {
  project           = "galoy-reporting"
  env               = var.environment
  kratos_connection = var.kratos_connection
  timestamp         = replace(timestamp(), "/[-| |T|Z|:]/", "")

  tables = {
    identities = {
      table_id = "identities"
      query = templatefile("${path.module}/queries/identities.sql.tmpl",
        {
          connection_id : var.kratos_connection
        }
      )
    },
    identity_credentials = {
      table_id = "identity_credentials"
      query = templatefile("${path.module}/queries/identity_credentials.sql.tmpl",
        {
          connection_id : var.kratos_connection
        }
      )
    },
  }
}

module "dataset" {
  source = "../raw_dataset"

  component   = "kratos"
  environment = var.environment
}

resource "google_bigquery_job" "job" {
  for_each = local.tables

  project = local.project
  job_id  = "import-${local.env}-kratos-${each.key}-${local.timestamp}"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = each.value.query

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = module.dataset.dataset_id
      table_id   = each.value.table_id
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/kratos"
  }
}
