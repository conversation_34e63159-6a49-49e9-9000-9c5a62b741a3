variable "component" {}
variable "environment" {}
variable "project" { default = "galoy-reporting" }
variable "view_all_envs" { default = false }
variable "expose_views_to_dataform" { default = false }
variable "dataset_location" { default = "US" }

locals {
  project             = var.project
  environment         = var.environment
  component           = var.component
  dataset_location    = var.dataset_location
  dataset_id          = replace("${local.environment}_${local.component}_raw", "-", "_")
  dataform_dataset_id = replace("dataform_${local.environment}", "-", "_")
  concourse_user      = "serviceAccount:<EMAIL>"

  data_team = {
    jireva  = "<EMAIL>",
    jcarter = "<EMAIL>"
    sv      = "<EMAIL>"
    oms     = "<EMAIL>"
    krtk    = "<EMAIL>"
  }

  viewers                  = var.view_all_envs || (local.environment == "galoy-staging" || local.environment == "volcano-staging") ? local.data_team : {}
  expose_views_to_dataform = ((local.component == "kafka" || local.component == "functions") || local.component == "stablesats") ? true : false
}

resource "google_bigquery_dataset" "raw_import" {
  project                    = local.project
  dataset_id                 = local.dataset_id
  friendly_name              = "${local.environment} ${local.component} raw"
  description                = "This is a raw dataset for ${local.environment} ${local.component}"
  location                   = local.dataset_location
  delete_contents_on_destroy = true
}

resource "google_bigquery_dataset_iam_member" "viewer" {
  for_each   = local.viewers
  project    = local.project
  dataset_id = google_bigquery_dataset.raw_import.dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "user:${each.value}"
}

resource "google_bigquery_dataset_iam_member" "owner" {
  project    = local.project
  dataset_id = google_bigquery_dataset.raw_import.dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = local.concourse_user
}

resource "google_bigquery_dataset_access" "access" {
  count      = local.expose_views_to_dataform ? 1 : 0
  project    = local.project
  dataset_id = google_bigquery_dataset.raw_import.dataset_id
  dataset {
    dataset {
      project_id = local.project
      dataset_id = local.dataform_dataset_id
    }
    target_types = ["VIEWS"]
  }
}

output "dataset_id" {
  value = google_bigquery_dataset.raw_import.dataset_id
}
