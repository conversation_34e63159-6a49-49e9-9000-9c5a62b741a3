variable "environment" {}
variable "blink_kyc_connection" {}

locals {
  project              = "galoy-reporting"
  env                  = var.environment
  blink_kyc_connection = var.blink_kyc_connection
  timestamp            = replace(timestamp(), "/[-| |T|Z|:]/", "")

  tables = {
    identities = {
      table_id = "identities"
      query = templatefile("${path.module}/queries/Applicant.sql.tmpl",
        {
          connection_id : var.blink_kyc_connection
        }
      )
    },
  }
}

module "dataset" {
  source = "../raw_dataset"

  component   = "blink_kyc"
  environment = var.environment
}

resource "google_bigquery_job" "job" {
  for_each = local.tables

  project = local.project
  job_id  = "import-${local.env}-blink-kyc-${each.key}-${local.timestamp}"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = each.value.query

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = module.dataset.dataset_id
      table_id   = each.value.table_id
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/blink-kyc"
  }
}
