variable "environment" {}
variable "lava_bank_connection" {}

locals {
  project              = "galoy-reporting"
  location             = "EU"
  env                  = var.environment
  lava_bank_connection = var.lava_bank_connection
  timestamp            = replace(timestamp(), "/[-| |T|Z|:]/", "")

  tables = {
    lava-bank-user-events = {
      table_id = "lava_bank_user_events"
      query = templatefile("${path.module}/queries/user_events.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-users = {
      table_id = "lava_bank_users"
      query = templatefile("${path.module}/queries/users.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-loan-terms = {
      table_id = "lava_bank_loan_terms"
      query = templatefile("${path.module}/queries/loan_terms.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-loans = {
      table_id = "lava_bank_loans"
      query = templatefile("${path.module}/queries/loans.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-loan-events = {
      table_id = "lava_bank_loan_events"
      query = templatefile("${path.module}/queries/loan_events.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-withdraws = {
      table_id = "lava_bank_withdraws"
      query = templatefile("${path.module}/queries/withdraws.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-withdraw-events = {
      table_id = "lava_bank_withdraw_events"
      query = templatefile("${path.module}/queries/withdraw_events.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-jobs = {
      table_id = "lava_bank_jobs"
      query = templatefile("${path.module}/queries/jobs.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-job-events = {
      table_id = "lava_bank_job_events"
      query = templatefile("${path.module}/queries/job_events.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
    lava-bank-job-executions = {
      table_id = "lava_bank_job_executions"
      query = templatefile("${path.module}/queries/job_executions.sql.tmpl",
        {
          connection_id : var.lava_bank_connection
        }
      )
    },
  }
}

module "dataset" {
  source = "../raw_dataset"

  component        = "lava_bank"
  environment      = var.environment
  dataset_location = local.location
}

resource "google_bigquery_job" "job" {
  for_each = local.tables

  project  = local.project
  location = local.location
  job_id   = "import-${local.env}-lava-bank-${each.key}-${local.timestamp}"

  labels = {
    "timestamp" = local.timestamp
  }

  query {
    query = each.value.query

    write_disposition = "WRITE_TRUNCATE"

    destination_table {
      project_id = local.project
      dataset_id = module.dataset.dataset_id
      table_id   = each.value.table_id
    }

    script_options {
      key_result_statement = "LAST"
    }
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "imports/lava-bank"
  }
}
