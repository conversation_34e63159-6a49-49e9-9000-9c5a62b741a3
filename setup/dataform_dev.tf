resource "google_bigquery_dataset" "dataform_dev" {
  for_each = local.data_team
  project  = local.project

  dataset_id    = "dataform_${each.key}"
  friendly_name = "${each.key} dataform"
  description   = "Dataform playground for ${each.key}"
  location      = "US"
}

resource "google_bigquery_dataset" "dataform_assertions_dev" {
  for_each = local.data_team
  project  = local.project

  dataset_id    = "dataform_assertions_${each.key}"
  friendly_name = "${each.key} assertions dataform"
  description   = "Dataform assertions for ${each.key}"
  location      = "US"
}

resource "google_bigquery_dataset_iam_member" "dataform_assertions_dev" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.dataform_assertions_dev[each.key].dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = "user:${each.value}"
}

resource "google_bigquery_dataset_iam_member" "dataform_dev" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.dataform_dev[each.key].dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = "user:${each.value}"
}

resource "google_bigquery_dataset_iam_member" "concourse_viewer" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.dataform_dev[each.key].dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_bigquery_dataset_iam_member" "output_viewer" {
  for_each   = { for item in local.output_viewers : "${item.dataset}.${item.member}" => item }
  project    = local.project
  dataset_id = each.value.dataset
  role       = "roles/bigquery.dataViewer"
  member     = "user:${each.value.member}"
}


resource "google_bigquery_dataset" "dataform_eu_dev" {
  for_each = local.data_team
  project  = local.project

  dataset_id    = "dataform_eu_${each.key}"
  friendly_name = "${each.key} dataform"
  description   = "Dataform playground for ${each.key}"
  location      = "EU"
}

resource "google_bigquery_dataset" "dataform_assertions_eu_dev" {
  for_each = local.data_team
  project  = local.project

  dataset_id    = "dataform_assertions_eu_${each.key}"
  friendly_name = "${each.key} assertions dataform"
  description   = "Dataform assertions for ${each.key}"
  location      = "EU"
}

resource "google_bigquery_dataset_iam_member" "dataform_assertions_eu_dev" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.dataform_assertions_eu_dev[each.key].dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = "user:${each.value}"
}

resource "google_bigquery_dataset_iam_member" "dataform_eu_dev" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.dataform_eu_dev[each.key].dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = "user:${each.value}"
}

resource "google_bigquery_dataset_iam_member" "concourse_eu_viewer" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.dataform_eu_dev[each.key].dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_bigquery_dataset_iam_member" "eu_output_viewer" {
  for_each   = { for item in local.output_viewers : "${item.dataset}.${item.member}" => item }
  project    = local.project
  dataset_id = each.value.dataset
  role       = "roles/bigquery.dataViewer"
  member     = "user:${each.value.member}"
}
