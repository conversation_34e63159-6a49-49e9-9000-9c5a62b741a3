resource "google_project_iam_member" "dataform_job_user" {
  for_each = local.data_team
  project  = local.project
  role     = "roles/bigquery.jobUser"
  member   = "user:${each.value}"
}

resource "google_project_iam_member" "read_session_user" {
  for_each = local.data_team
  project  = local.project
  role     = "roles/bigquery.readSessionUser"
  member   = "user:${each.value}"
}

resource "google_project_iam_member" "dataform_resource_viewer" {
  for_each = local.data_team
  project  = local.project
  role     = "roles/bigquery.resourceViewer"
  member   = "user:${each.value}"
}

resource "google_project_iam_member" "service_usage_consumer" {
  for_each = local.data_team
  project  = local.project
  role     = "roles/serviceusage.serviceUsageConsumer"
  member   = "user:${each.value}"
}

resource "google_project_iam_member" "cloud_function_viewer" {
  for_each = local.data_team
  project  = local.project
  role     = "roles/cloudfunctions.viewer"
  member   = "user:${each.value}"
}

resource "google_project_iam_member" "logging_view_accessor" {
  for_each = local.data_team
  project  = local.project
  role     = "roles/logging.viewAccessor"
  member   = "user:${each.value}"
}

resource "google_project_iam_custom_role" "datatransfers_get_role" {
  project     = local.project
  role_id     = "datatransfersGetRole"
  title       = "Bigquery datatransfers get role"
  description = "This role allows a user to see configured datatransfers"
  permissions = [
    "bigquery.transfers.get",
  ]
}

resource "google_project_iam_member" "datatransfers_get" {
  for_each = local.data_team
  project  = local.project
  role     = google_project_iam_custom_role.datatransfers_get_role.id
  member   = "user:${each.value}"
}
