resource "google_bigquery_dataset" "galoy_data_sync" {
  project                    = local.project
  dataset_id                 = "galoy_data_sync_dev"
  friendly_name              = "Dataset for galoy-data-sync dev work"
  description                = "Dataset for galoy-data-sync dev work"
  location                   = "US"
  delete_contents_on_destroy = true

}

resource "google_bigquery_dataset_iam_member" "concourse_dev_owner" {
  project    = local.project
  dataset_id = google_bigquery_dataset.galoy_data_sync.dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_bigquery_dataset_iam_member" "dev_owner" {
  project    = local.project
  dataset_id = google_bigquery_dataset.galoy_data_sync.dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = local.justin
}

output "galoy_data_sync_id" {
  value = google_bigquery_dataset.galoy_data_sync.dataset_id
}
