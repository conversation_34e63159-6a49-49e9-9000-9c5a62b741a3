resource "google_project_iam_custom_role" "list_objects" {
  project     = local.project
  role_id     = local.objects_list_role_name
  title       = "List bucket Objects"
  description = "Role to _only_ list objects (not get them)"
  permissions = [
    "storage.objects.list",
  ]
}

resource "google_storage_bucket" "tf_state" {
  name                        = local.tf_state_bucket_name
  project                     = local.project
  location                    = local.location
  uniform_bucket_level_access = true
  versioning {
    enabled = true
  }
}

data "google_iam_policy" "tf_state_access" {
  binding {
    role    = "roles/storage.admin"
    members = [local.justin]
  }

  dynamic "binding" {
    for_each = toset([local.justin, "serviceAccount:${google_service_account.concourse.email}"])
    content {
      role = google_project_iam_custom_role.list_objects.id
      members = [
        binding.key
      ]
    }
  }

  dynamic "binding" {
    for_each = toset([local.justin, "serviceAccount:${google_service_account.concourse.email}"])
    content {
      role = "roles/storage.objectAdmin"
      members = [
        binding.key
      ]

      condition {
        title      = "imports"
        expression = "resource.name.startsWith(\"projects/_/buckets/${google_storage_bucket.tf_state.name}/objects/imports\")"
      }
    }
  }

  dynamic "binding" {
    for_each = toset([local.justin, "serviceAccount:${google_service_account.concourse.email}"])
    content {
      role = "roles/storage.objectAdmin"
      members = [
        binding.key
      ]

      condition {
        title      = "functions"
        expression = "resource.name.startsWith(\"projects/_/buckets/${google_storage_bucket.tf_state.name}/objects/functions\")"
      }
    }
  }

  dynamic "binding" {
    for_each = toset([local.justin, "serviceAccount:${google_service_account.concourse.email}"])
    content {
      role = "roles/storage.objectAdmin"
      members = [
        binding.key
      ]

      condition {
        title      = "kafka"
        expression = "resource.name.startsWith(\"projects/_/buckets/${google_storage_bucket.tf_state.name}/objects/kafka\")"
      }
    }
  }
}

resource "google_storage_bucket_iam_policy" "policy" {
  bucket      = google_storage_bucket.tf_state.name
  policy_data = data.google_iam_policy.tf_state_access.policy_data
}
