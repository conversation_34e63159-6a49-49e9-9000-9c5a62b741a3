resource "google_bigquery_dataset" "cloud_billing_dataset" {
  project                    = local.project
  dataset_id                 = "cloud_billing"
  friendly_name              = "Cloud Billing"
  description                = "Contains Cloud Billing data export"
  location                   = "US"
  delete_contents_on_destroy = true
}

resource "google_bigquery_dataset_iam_member" "cloud_billing_data_team" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.cloud_billing_dataset.dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "user:${each.value}"
}

resource "google_bigquery_dataset_iam_member" "cloud_billing_concourse" {
  project    = local.project
  dataset_id = google_bigquery_dataset.cloud_billing_dataset.dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.concourse.email}"
}
