resource "google_storage_bucket" "functions" {
  project  = local.project
  name     = "reporting-cloud-functions"
  location = "us-east1"

  versioning {
    enabled = true
  }
}

resource "google_storage_bucket_iam_member" "concourse" {
  bucket = google_storage_bucket.functions.name
  role   = "roles/storage.admin"
  member = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_service_account" "functions" {
  project      = local.project
  account_id   = "functions"
  display_name = "Account for running functions"
}

output "functions_sa" {
  value = google_service_account.functions.email
}

resource "google_service_account_iam_member" "concourse" {
  service_account_id = google_service_account.functions.id
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_project_iam_member" "functions_job_user" {
  project = local.project
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${google_service_account.functions.email}"
}

resource "google_project_iam_member" "functions_secret_accessor" {
  project = local.project
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.functions.email}"
}

resource "google_bigquery_dataset" "functions_dev" {
  for_each = local.data_team
  project  = local.project

  dataset_id    = "functions_${each.key}"
  friendly_name = "${each.key} functions"
  description   = "Functions playground for ${each.key}"
  location      = "US"
}

resource "google_bigquery_dataset_iam_member" "functions_dev" {
  for_each   = local.data_team
  project    = local.project
  dataset_id = google_bigquery_dataset.functions_dev[each.key].dataset_id
  role       = "roles/bigquery.dataOwner"
  member     = "user:${each.value}"
}
