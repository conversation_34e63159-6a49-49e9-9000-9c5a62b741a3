
resource "google_storage_bucket" "external_code" {
  project       = local.project
  name          = "galoy_reporting_external_code"
  location      = "US"
  force_destroy = true
}

resource "google_storage_bucket_object" "libphonenumber" {
  name   = "libphonenumber-max.js"
  bucket = google_storage_bucket.external_code.name
  source = "../external_code/libphonenumber-max.js"

  content_type = "application/javascript"
}

resource "google_storage_bucket_object" "fuzzball" {
  name   = "fuzzball.umd.min.js"
  bucket = google_storage_bucket.external_code.name
  source = "../external_code/fuzzball.umd.min.js"

  content_type = "application/javascript"
}

resource "google_storage_bucket_iam_member" "concourse_external_code" {
  bucket = google_storage_bucket.external_code.name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_storage_bucket_iam_member" "data_team_external_code" {
  for_each = local.data_team
  bucket   = google_storage_bucket.external_code.name
  role     = "roles/storage.objectViewer"
  member   = "user:${each.value}"
}
