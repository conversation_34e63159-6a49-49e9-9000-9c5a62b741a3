resource "google_pubsub_topic" "minutes" {
  project = local.project
  name    = "minutes"
}

resource "google_pubsub_topic_iam_member" "minutes" {
  project = google_pubsub_topic.minutes.project
  topic   = google_pubsub_topic.minutes.name
  role    = "roles/viewer"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_cloud_scheduler_job" "every_minute" {
  project     = local.project
  region      = "us-east1"
  name        = "every-minute"
  description = "Ticks every minute"
  schedule    = "* * * * *"

  pubsub_target {
    topic_name = google_pubsub_topic.minutes.id
    data       = base64encode("tick")
  }
}

resource "google_pubsub_topic" "hours" {
  project = local.project
  name    = "hours"
}

resource "google_pubsub_topic_iam_member" "hours" {
  project = google_pubsub_topic.hours.project
  topic   = google_pubsub_topic.hours.name
  role    = "roles/viewer"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_cloud_scheduler_job" "every_hour" {
  project     = local.project
  region      = "us-east1"
  name        = "every-hour"
  description = "Ticks every hour"
  schedule    = "0 * * * *"

  pubsub_target {
    topic_name = google_pubsub_topic.hours.id
    data       = base64encode("tick")
  }
}

resource "google_pubsub_topic" "days" {
  project = local.project
  name    = "days"
}

resource "google_pubsub_topic_iam_member" "days" {
  project = google_pubsub_topic.days.project
  topic   = google_pubsub_topic.days.name
  role    = "roles/viewer"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_cloud_scheduler_job" "every_day" {
  project     = local.project
  region      = "us-east1"
  name        = "every-day"
  description = "Ticks every day"
  schedule    = "0 0 * * *"

  pubsub_target {
    topic_name = google_pubsub_topic.days.id
    data       = base64encode("tick")
  }
}
