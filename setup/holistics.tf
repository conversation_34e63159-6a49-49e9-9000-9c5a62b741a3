resource "google_service_account" "holistics" {
  project      = local.project
  account_id   = "holistics"
  display_name = "Holistics"
}

resource "google_project_iam_member" "holistics_job_user" {
  project = local.project
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${google_service_account.holistics.email}"
}

resource "google_bigquery_dataset_iam_member" "holistics_viewer" {
  for_each   = toset(local.dataform_outputs)
  project    = local.project
  dataset_id = each.value
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.holistics.email}"
}
