resource "google_service_account" "circles_staging" {
  project      = local.project
  account_id   = "blink-circles-staging"
  display_name = "blink-circles-staging"
}

resource "google_service_account" "circles_bbw" {
  project      = local.project
  account_id   = "blink-circles-bbw"
  display_name = "blink-circles-bbw"
}

resource "google_project_iam_member" "circles_staging" {
  project = local.project
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${google_service_account.circles_staging.email}"
}

resource "google_project_iam_member" "circles_bbw" {
  project = local.project
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "staging_circles_tables" {
  for_each = {
    "1"  = { dataset = "dataform_galoy_staging", table = "report_rt_circles" }
    "2"  = { dataset = "dataform_galoy_staging", table = "report_circle_updates" }
    "3"  = { dataset = "dataform_galoy_staging", table = "stg_rt_onboarding_graph" }
    "4"  = { dataset = "dataform_galoy_staging", table = "stg_batch_onboarding_graph" }
    "5"  = { dataset = "dataform_galoy_staging", table = "stg_rt_new_onboardings" }
    "6"  = { dataset = "dataform_galoy_staging", table = "stg_journal_entries" }
    "7"  = { dataset = "dataform_galoy_staging", table = "stg_rt_circles_wallets" }
    "8"  = { dataset = "dataform_galoy_staging", table = "stg_rt_circles_accounts" }
    "9"  = { dataset = "dataform_galoy_staging", table = "stg_user_wallets" }
    "10" = { dataset = "dataform_galoy_staging", table = "stg_solette_accounts" }
    "11" = { dataset = "dataform_galoy_staging", table = "stg_accounts" }
    "12" = { dataset = "dataform_galoy_staging", table = "stg_user_accounts" }
    "13" = { dataset = "galoy_staging_kafka_raw", table = "mongodb_galoy_medici_transactions" }
    "14" = { dataset = "galoy_staging_kafka_raw", table = "mongodb_galoy_accounts" }
    "15" = { dataset = "galoy_staging_kafka_raw", table = "mongodb_galoy_wallets" }
    "16" = { dataset = "galoy_staging_galoy_raw", table = "accounts" }
  }
  project    = local.project
  dataset_id = each.value.dataset
  table_id   = each.value.table
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_staging.email}"
}

resource "google_bigquery_table_iam_member" "bbw_circles_tables" {
  for_each = {
    dataform_galoy_bbw  = "report_rt_circles"
    dataform_galoy_bbw  = "report_circle_updates"
    dataform_galoy_bbw  = "stg_rt_onboarding_graph"
    dataform_galoy_bbw  = "stg_batch_onboarding_graph"
    dataform_galoy_bbw  = "stg_rt_new_onboardings"
    dataform_galoy_bbw  = "stg_journal_entries"
    dataform_galoy_bbw  = "stg_rt_circles_wallets"
    dataform_galoy_bbw  = "stg_rt_circles_accounts"
    dataform_galoy_bbw  = "stg_user_wallets"
    dataform_galoy_bbw  = "stg_solette_accounts"
    dataform_galoy_bbw  = "stg_accounts"
    dataform_galoy_bbw  = "stg_user_accounts"
    galoy_bbw_kafka_raw = "mongodb_galoy_medici_transactions"
    galoy_bbw_kafka_raw = "mongodb_galoy_accounts"
    galoy_bbw_kafka_raw = "mongodb_galoy_wallets"
    galoy_bbw_galoy_raw = "accounts"
  }
  project    = local.project
  dataset_id = each.key
  table_id   = each.value
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_rt_circles" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "report_rt_circles"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_circles_updates" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "report_circle_updates"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_rt_onboarding_graph" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_rt_onboarding_graph"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_batch_onboarding_graph" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_batch_onboarding_graph"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_rt_new_onboardings" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_rt_new_onboardings"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_journal_entries" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_journal_entries"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_mongodb_galoy_medici_transactions" {
  project    = local.project
  dataset_id = "galoy_bbw_kafka_raw"
  table_id   = "mongodb_galoy_medici_transactions"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_rt_circles_wallets" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_rt_circles_wallets"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_rt_circles_accounts" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_rt_circles_accounts"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_mongodb_galoy_accounts" {
  project    = local.project
  dataset_id = "galoy_bbw_kafka_raw"
  table_id   = "mongodb_galoy_accounts"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_accounts" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_accounts"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_user_accounts" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_user_accounts"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_mongodb_galoy_wallets" {
  project    = local.project
  dataset_id = "galoy_bbw_kafka_raw"
  table_id   = "mongodb_galoy_wallets"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_user_wallets" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_user_wallets"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_solette_accounts" {
  project    = local.project
  dataset_id = "dataform_galoy_bbw"
  table_id   = "stg_solette_accounts"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}

resource "google_bigquery_table_iam_member" "bbw_raw_accounts" {
  project    = local.project
  dataset_id = "galoy_bbw_galoy_raw"
  table_id   = "accounts"
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.circles_bbw.email}"
}
