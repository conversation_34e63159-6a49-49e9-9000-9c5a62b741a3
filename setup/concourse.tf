resource "google_service_account" "concourse" {
  project      = local.project
  account_id   = "concourse"
  display_name = "For provisioning from concourse"
}

resource "google_project_iam_custom_role" "concourse_bq" {
  project     = local.project
  role_id     = "concourseBq"
  title       = "Concourse BigQuery"
  description = "This role is for concourse to be able to do bigquery stuff"
  permissions = [
    "bigquery.datasets.create",
    "bigquery.datasets.update",
    "bigquery.datasets.get",
    "bigquery.tables.create",
    "bigquery.tables.get",
    "bigquery.tables.update",
    "bigquery.tables.updateData",
    "bigquery.jobs.create",
    "bigquery.jobs.get",
  ]
}

resource "google_project_iam_member" "concourse_resource_viewer" {
  project = local.project
  role    = "roles/bigquery.resourceViewer"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_project_iam_member" "concourse_job_user" {
  project = local.project
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_project_iam_member" "concourse_cloudfunctions" {
  project = local.project
  role    = "roles/cloudfunctions.developer"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

data "google_service_account" "cloud_function_default_sa" {
  account_id = local.cloud_function_default_sa
}

resource "google_service_account_iam_member" "gce-default-account-iam" {
  service_account_id = data.google_service_account.cloud_function_default_sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_project_iam_member" "concourse_bq" {
  project = local.project
  role    = google_project_iam_custom_role.concourse_bq.id
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_project_iam_member" "concourse_secrets_admin" {
  project = local.project
  role    = "roles/secretmanager.admin"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}

resource "google_project_iam_member" "concourse_service_account_admin" {
  project = local.project
  role    = "roles/iam.serviceAccountAdmin"
  member  = "serviceAccount:${google_service_account.concourse.email}"
}
