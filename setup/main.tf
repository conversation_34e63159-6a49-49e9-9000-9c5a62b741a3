locals {
  project                = "galoy-reporting"
  location               = "US-EAST1"
  tf_state_bucket_name   = "galoy-reporting-tf-state"
  objects_list_role_name = "objects_list"

  justin = "user:<EMAIL>"

  connection_users = ["serviceAccount:<EMAIL>"]

  dataform_sa               = "<EMAIL>"
  cloud_function_default_sa = "<EMAIL>"

  data_team = {
    jireva  = "<EMAIL>",
    jcarter = "<EMAIL>"
    sv      = "<EMAIL>"
    oms     = "<EMAIL>"
    krtk    = "<EMAIL>"
  }

  dataform_outputs = [
    "dataform_galoy_staging",
    "dataform_galoy_bbw",
    "dataform_assertions_galoy_staging",
    "dataform_assertions_galoy_bbw",
  ]

  output_viewers = flatten([for out in local.dataform_outputs : [
    for k, v in local.data_team : {
      dataset = out
      member  = v
    }
  ]])
}

terraform {
  backend "gcs" {
    bucket = "galoy-reporting-tf-state"
    prefix = "galoy-reporting/setup"
  }
}
